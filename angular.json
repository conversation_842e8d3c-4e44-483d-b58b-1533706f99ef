{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"customer-v2": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/customer-v2", "index": "src/index.html", "main": "src/main.ts", "sourceMap": true, "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public", "output": "/public"}, "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "node_modules/@fortawesome/fontawesome-pro/css/all.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "serviceWorker": true}, "configurations": {"sandbox": {"sourceMap": true, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "extractLicenses": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.sandbox.ts"}], "namedChunks": true}, "staging": {"sourceMap": true, "optimization": false, "extractLicenses": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "namedChunks": true}, "production": {"sourceMap": true, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": false, "fonts": true}}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "customer-v2:build:production"}, "development": {"buildTarget": "customer-v2:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public", "output": "/public"}], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "node_modules/@fortawesome/fontawesome-pro/css/all.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}}}}}, "cli": {"analytics": "20a990d4-0d03-46eb-89ba-b244368784ee"}}
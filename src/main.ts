import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { environment } from './environments/environment'; 
import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import * as Sentry from '@sentry/angular';


if (environment.production) {
  enableProdMode();
}

// Initialize Sentry
Sentry.init({
  environment: environment.envName, 
  dsn: 'https://<EMAIL>/****************',
  integrations: [
    // Registers and configures the Tracing integration,
    // which automatically instruments your application to monitor its
    // performance, including custom Angular routing instrumentation
    Sentry.browserTracingIntegration(),
    // Registers the Replay integration,
    // which automatically captures Session Replays
    Sentry.replayIntegration({
        // Additional SDK configuration goes in here, for example:
        maskAllText: false,
        maskAllInputs: true,
        blockAllMedia: false,
        networkDetailAllowUrls: [/.*/],
        networkRequestHeaders: ["Cache-Control"],
        networkResponseHeaders: ["Referrer-Policy"],
      }
    ),

  ],

  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 1.0,

  // Set `tracePropagationTargets` to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: [environment.crmApiUrl, environment.usermanagementApiUrl],
  
  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,

  // Add beforeSend callback to filter out errors from localhost
  beforeSend(event, hint) {
    if (window.location.hostname === 'localhost') {
      return null;
    }
    return event;
  }
});

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => console.error(err));

  
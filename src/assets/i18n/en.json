{"applicationUpdateMessage": "A new version of the application is available. Please update to the latest version by clicking okay.", "login.header": "Between", "login.subheader": "Customer portal", "login.loginText": "Please log in with your phone number", "login.sendCode": "Send code", "login.confirmCode": "Confirm code", "login.invalidOtp": "Invalid OTP", "otp.enterPin": "Enter the 4-digit code sent to your phone", "otp.didntReceiveSMS": "Didn't receive the SMS?", "otp-modal.otpSent": "OTP sent", "otp.sendSMSAgain": "Send SMS again", "otp-modal.seconds": "seconds", "otp.invalidOTP": "Invalid OTP", "otp.confirmBtn": "Confirm", "otp.goBack": "Go back", "otp.otpFailed": "The phone number you entered is not registered. Please check your phone number and try again.", "pageNotFound.heading": "Page not found", "pageNotFound.description": "The page you are looking for does not exist.", "pageNotFound.attemptedURL": "Attempted URL: ", "goToHomePage": "Go to home page", "company-title": "Do you have any questions? Contact us!", "seller-title": "Do you have any questions? Contact me!", "order.details.hideCrew": "<PERSON>de", "order.details.seeCrew": "Who's coming?", "order.details.noTime": "No time", "order.details.executionTime": "Time", "order.details.job": "Order from", "order.details.workorder": "Standalone job", "order.details.arrivalWindow": "Arrival window", "order.details.addresses": "Addresses", "order.details.seeLess": "Show less", "order.details.seeAll": "Show all", "order.details.crew_on_way": "We're on our way", "order.details.crew_on_way_desc": "We're on our way and we will arrive shortly", "order.details.order_scheduled": "Not started", "order.details.in_progress": "In progress", "order.details.completed": "Completed", "order.details.awaiting_payment": "Awaiting payment", "order.details.payment_completed": "Payment successful", "order.details.cancelled": "Cancelled", "order.details.unknown_status": "Unknown status", "order.details.prepare_space": "", "order.details.team_working": "The team is currently working on your order.", "order.details.order_completed": "The order has been completed.", "order.details.awaiting_payment_desc": "The order is awaiting payment. please complete the payment to proceed.", "order.details.payment_completed_desc": "The payment has been completed successfully.", "order.details.cancelled_desc": "The order has been cancelled.", "order.details.contact_support": "Please contact support for more information.", "order.details.invoice_sent": "Invoice sent", "order.details.description.invoice_sent": "The invoice has been sent to your email.", "order.details.missingAddress": "No address", "order.details.updating_status": "Updating order...", "order.details.updating_status_message": "We are updating your order. Please wait.", "order.details.status_update_failed": "We couldn't update the order. Please refresh the page or try again later.", "order.details.status_update_error": "An error occurred while updating the order. Please try again later.", "order.workOrder.seeAll": "See all jobs", "order.details.allWorkOrders": "All jobs", "phoneInput.required": "Phone number is required", "phoneInput.invalidPhone": "Invalid phone number", "quotes.title": "Quotes", "quotes.orderDate": "Order date", "quotes.time": "Time", "quote.viewDetials": "View details", "quote.modal.header": "Confirm quote", "quote.modal.text": "Thank you for confirming your offer. Your order will now be processed", "quote.modal.gotoOrder": "Go to order", "quote.modal.confirmQuoteMessage": "Are you sure you want to accept this quote?", "quote.modal.confirmBtn": "Confirm", "order-details.title": "Order ID", "order-details.status": "Order status", "order-details.crew.teamLeader": "Team leader", "order-details.whosComing": "Who's coming?", "order-details.paymentText": "Your order is now ready for payment, please proceed to payment", "order-details.paymentBtn": "Go to payment", "quotes.contact": "Contact", "quotes.notes": "Messages", "order-list.hello": "Hello", "order-list.title": "My orders", "order-list.orders": "Orders", "order-list.allOrders": "All orders", "order-list.noAllOrders": "You have no orders", "order-list.noUnpaidOrders": "You have no unpaid orders", "order-list.unpaid": "Unpaid orders", "order-list.past": "Past", "order-list.subscriptions": "Subscriptions", "order-list.subscriptions.active": "Active", "subscriptions.header": "Repeating job", "subscriptions.schedule-details.header": "Recurring schedule", "subscriptions.schedule-details.status": "Status", "subscriptions.schedule-details.time": "Working hours", "subscriptions.schedule-details.repeats": "Repeats", "subscriptions.schedule-details.contactUs": "Contact us to edit the schedule", "subscriptions.orders.title1": "Your next", "subscriptions.orders.title2": "orders", "subscriptions.schedule-details.summary.header": "Order summary", "subscriptions.schedule-details.summary.description": "Description", "subscriptions.schedule-details.summary.amount": "Amount", "subscriptions.schedule-details.summary.selectedProducts": "Selected products", "subscriptions.schedule-details.summary.total": "Total", "subscriptions.choosePayment": "Choose payment method", "subscriptions.choosePaymentDesc": "Choose a payment method for your subscription", "subscriptions.subscriptionIsActive": "Subscription is active", "subscriptions.subscriptionIsPausedDescQuickpay": "Going forward, we will directly charge your bank card when the order is completed. If you wish to change the card, please contact us.", "subscriptions.subscriptionIsPausedDescInvoice": "We will now send you the invoice each time the order is completed on: {{email}}. If you wish to change the e-mail address, contact us.", "repeating.order": "Next job", "repeating.orders": "Next jobs", "repeatingPayment.payment": "Fixed price", "repeatingPayment.repeatingPayment": "Repeating payment", "repeatingPayment.sent": "<PERSON><PERSON>", "repeatingPayment.invoiced": "Invoiced", "repeatingPayment.interval": "Interval", "repeatingPayment.frequency": "Frequency", "repeatingPayment.subscription": "Contact us to edit the subscription", "repeatingPayment.every": "every", "repeatingPayment.nextPayment": "Next payment", "repeatingPayment.noNextPayment": "No next payment", "repeatingPayment.nextFiveOrders": "Next five orders", "repeatingPayment.summary": "Summary", "repeatingPayment.repeatingOrder": "Repeating job", "repeating.seeMore": "See more", "repeating.seeLess": "See less", "repeatingPayment.repeatingOrder.starting": "Start time", "repeatingPayment.repeatingOrder.estimated": "Estimated duration", "repeatingPayment.repeatingOrder.startDate": "Start date", "repeatingPayment.repeatingOrder.stopDate": "Stop date", "repeatingPayment.repeatingOrder.howOften": "How often?", "repeatingPayment.repeatingOrder.every": "every", "repeatingPayment.repeatingOrder.weekdays": "Weekdays", "repeatingPayment.repeatingOrder.repeat": "The work order will repeat every", "repeatingPayment.repeatingOrder.on": "on the", "repeatingPayment.repeatingOrder.day": "day", "repeatingPayment.repeatingOrder.repeats": "Repeats", "order.details.unknownEndTime": "Unknown end time", "notesCard.addNotes": "Add messages", "notesCard.addNotes.btn": "Send message", "notesCard.daysAgo": "days ago", "notesCard.hoursAgo": " hours ago", "notesCard.minutesAgo": "minutes ago", "notesCard.justNow": "just now", "upsell-products.removeBtn": "Remove", "upsell-products.addBtn": "Add", "upsell-products.addUnit": "Enter number of", "images.title": "Would you like to add other services?", "important-information.heading": "Important information", "important-information.checkboxlabel": "I have read and agree to the information above", "important-information.errorMessage": "Please confirm the information above", "order-summary.title": "Order summary", "order-summary.containsHourlyRatedOrderLines": "Order totals will be calculated after the job has been finished", "order-summary.refundedAmount": "Refunded amount", "order-summary.remainingAmount": "Outstanding amount", "order-summary.amount": "Amount", "order-summary.heading": "Order summary", "order-summary.description": "Description", "order-summary.cost": "Amount", "order-summary.total": "Total", "order-summary.vat": "VAT", "order-summary.showExVatLabel": "Excl. VAT", "order-summary.showIncVatLabel": "Incl. VAT", "order-summary.confirmQuoteBtn": "Confirm quote", "order-summary.confirmQuoteBtnText": "Click here to confirm order", "order-summary.discount": "Discount", "order-summary.confirmQuoteInformationErrorPopup": "Please confirm the information above", "order-summary.confirmQuoteInformationErrorPopupCloseBtn": "Close", "order-summary.paymentStatus": "Payment status", "order-summary.paymentDate": "Payment date", "order-summary.paymentMethod": "Payment method", "order-summary.totalCalculated": "The total will be calculated after the job is completed.", "order-summary.estimatedPrice": "This is only an estimate, the final price will be calculated after the order is completed.", "order-summary.quoteDaysLeft": "Days left", "order-summary.day": "day", "order-summary.days": "days", "order-summary.quoteExpired": "This quote has expired.", "payment.goto": "Go to payment", "payment.header": "Select payment method", "payment.headerTitle": "Outstanding payments", "payment.paymentInformation": "Payment Information", "payment.dueDate": "Due Date", "payment.status": "Status", "payment.invoiceSentAt": "Invoice Sent", "payment.sendType": "Send Type", "payment.invoiceEmail": "Invoice Email", "payment.comment": "Comment", "payment.notSent": "Not sent", "payment.paymentMethod": "Payment Method", "payment.amount": "Amount", "terms.title": "Terms and conditions", "terms.privacyPolicy": "Privacy policy", "terms.backBtn": "Back", "cq.header": "Questions for", "cq.preparation": "Preparation for the job", "cq.answered": "Questions are answered", "cq.descriptionNotAnswered": "Please help us prepare for the job by answering some questions.", "cq.descriptionAnswered": "Thank you for answering the questions. We have received your responses and are ready to proceed with the job.", "cq.btn1": "Questions", "cq.btn2": "Check answers", "cq.description": "Please help us prepare for the job by answering some questions.", "order-summary.confirmQuoteHeading": "Are you sure you want to confirm this quote?", "order-feedback.howWasOurService": "How was our service?", "order-feedback.helpUsImprove": "Your feedback will help us improve.", "order-feedback.sendFeedback": "Send feedback", "order-feedback.leaveComment": "Write comment here...", "order-feedback.comment": "Your comment", "order-feedback.feedbackSent": "Thank you for your feedback!", "order-feedback.errorRequired": "Please provide either a rating or a comment before submitting.", "order-feedback.noComment": "No comment", "payment.summary.paid": "Paid", "payment.summary.yourPayments": "Your payments", "payment.summary.payment": "Payment", "payment.summary.receipt": "Receipt", "payment.summary.totalExVat": "Total Excl. VAT", "payment.summary.vat": "VAT", "payment.summary.total": "Total", "payment.summary.button": "Go to summary", "payment.summary.seeMore": "See more", "payment.summary.seeLess": "See less", "payment.pollingMessage": "Checking payment status...", "order-payment.paymentId": "Payment ID", "order-payment.subscriptionActive": "Subscription Active", "order-payment.subscriptionActiveDesc": "You have an active subscription.", "order-payment.activeSubscription": "Active Subscription", "order-payment.subscriptionType": "Subscription Type", "order-payment.noType": "No type specified", "order-payment.nextBilling": "Next Billing Date", "order-payment.noBillingDate": "No billing date available", "order-payment.amount": "Amount", "order-payment.manageSubscription": "Manage Subscription", "order-payment.cancelSubscription": "Cancel Subscription", "order-payment.changePaymentDetails": "If you wish to change your payment details, cancel the subscription and add your desired payment details again", "order-payment.cardOrVipps": "Card or Vipps", "order-payment.card": "Card", "order-payment.AddSubscription": "Add subscription", "order-payment.quickpay": "Quickpay", "order-payment.quickpayTag": "Pay with Visa, MasterCard, Vipps, ApplePay, GooglePay", "order-payment.quickpayTagWithoutVipps": "Pay with Visa, MasterCard, ApplePay, GooglePay", "order-payment.quickpayDetails": "After clicking “Pay now”, you will be redirected to Quickpay to complete your purchase securely.", "order-payment.quickpayDetailsSubscription": "After clicking 'Create Subscription', you will be redirected to Quickpay to complete your subscription securely.", "order-payment.dintero": "<PERSON><PERSON>", "order-payment.svea": "Pay later & installments", "order-payment.sveaTag": "Pay later or in installments with Svea", "order-payment.sveaDetails": "After clicking “Pay now”, you will be redirected to Svea to complete your purchase securely.", "order-payment.invoice": "Invoice", "order-payment.invoiceTag": "Pay by invoice", "order-payment.invoiceTagSent": "An invoice has already been sent. You can still pay using the alternatives listed above, or pay using your received invoice.", "order-payment.invoiceDetails": "After clicking “Pay now”, you will shortly receive an invoice at: ", "order-payment.pay": "Pay Now", "order-payment.sendInvoice": "Send invoice", "order-payment.save": "Save", "order-payment.noEmail": "No email registered, contact us.", "order-payment.createSubscription": "Create subscription", "order-payment.noMethodsEnabled": "No payment methods available, please contact us for further information.", "order-payment.noEmailText": "No email registered, please register an email address before selecting this payment method.", "order-payment.email": "Your email here...", "payment.details.payment_completed": "Payment completed", "toast.paymentExpiredMessage": "Betalingen er utløpt. Vennligst prøv igjen.", "riverty.installment": "Installments", "riverty.prMonth": ",-/mth", "riverty.firstName": "First name", "riverty.lastName": "Last name", "riverty.email": "Email", "riverty.phone": "Phone", "riverty.street": "Street", "riverty.streetNumber": "Number", "riverty.postalPlace": "Postal place", "riverty.postalCode": "Postal code", "riverty.firstNameRequired": "First name is required", "riverty.lastNameRequired": "Last name is required", "riverty.identificationNumber": "Identification number (11 digits)", "riverty.emailRequired": "Email is required", "riverty.phoneRequired": "Phone is required", "riverty.streetRequired": "Street is required", "riverty.streetNumberRequired": "Street number is required", "riverty.postalPlaceRequired": "Postal place is required", "riverty.postalCodeRequired": "Postal code is required", "riverty.identificationNumberRequired": "Identification number is required", "riverty.useDeliveryAddress": "Use same address as delivery address", "riverty.addressHeader": "Billing address", "riverty.numberOfInstallments": "Number of installments:", "riverty.installmentAmount": "Installment amount:", "riverty.totalAmount": "Total amount:", "riverty.intresertRate": "Interest rate:", "riverty.monthlyFee": "Monthly fee:", "riverty.startupFee": "Startup fee:", "riverty.payNowBtn": "Pay now", "riverty.termsAndCondition": "Terms and conditions", "riverty.privacyStatement": "Privacy statement", "contact.contactUs": "Contact Us", "contact.companyName": "Company name", "contact.companyPhone": "Phone", "contact.companyEmail": "Email", "contact.companyAddress": "Address", "contact.companyOrgNumber": "Org. number", "contact.backBtn": "Back to order", "important-information.required": "Information marked with (*) must be confirmed", "important-information.see-more": "See more", "attached.images.header": "Attached files", "attached.images.download": "Download", "attached.images.moreImages": "Images", "attached.images.delete": "Delete", "attached.images.upload": "Upload Image", "attached.images.noFiles": "No files upladed. Click the button below to upload files.", "attached.images.uploading": "Uploading...", "attached.images.error.size": "File size exceeds 10MB limit", "attached.images.error.upload": "Failed to upload file", "bottom.navbar.orders": "My orders", "bottom.navbar.quotes": "Quotes", "incidents.images.header": "Incidents", "incidents.reportedBy": "Reported by:", "incidents.select.work.order": "Select job", "incidents.select.work.order.placeholder": "Select job", "incidents.description": "Description", "incidents.description.placeholder": "Enter description", "incidents.upload.image.placeholder": "No images uploaded", "incidents.no.reports": "No reports", "incidents.add.images": "Add Images", "incidents.add.report": "Add Report", "incidents.saving.report": "Saving report...", "incidents.visible.for.customer": "Visible for customer", "incidents.select.work.order.required": "You must select a job", "otp-modal.heading": "Enter pin from sms", "otp-modal.didntReceiveSMS": "Didn’t receive a sms?", "otp-modal.sendSMSAgain": "Send sms again", "otp-modal.otpSentPleaseTryAgain": "OTP sent, please try again in", "otp-modal.invalidOTP": "Invalid OTP", "otp-modal.confirmBtn": "Confirm", "otp-modal.cancelBtn": "Cancel", "otp-modal.confirmTerms": "By confirming you accept the ", "otp-modal.termsOfSale": "Terms of sale", "common.phone": "Phone number", "common.quoteFrom": "Quote from", "common.phoneError": "Invalid phone number", "common.address": "Address", "common.for": "performed by", "common.to": "to", "common.orderId": "Order ID", "common.customer": "Customer", "common.save": "Save", "common.submit": "Submit", "common.next": "Next", "common.previous": "Previous", "common.close": "Close", "common.noDate": "Date not decided yet", "common.per": "per", "common.cancel": "Cancel", "common.yes": "Yes", "common.no": "No", "common.notSet": "Not set", "left.menu.terms": "Terms", "left.menu.privacyPolicy": "Privacy policy", "left.menu.contactUs": "Contact us", "left.menu.myOrders": "My orders", "left.menu.title": "Navigate", "pdf-invoice.receiptTitle": "Receipt", "pdf-invoice.paymentRecipient": "Payment recipient", "pdf-invoice.quotationDetailsHeading": "Details", "pdf-invoice.receiptNumber": "Order number", "pdf-invoice.receiptDate": "Payment date", "pdf-invoice.paymentMethod": "Payment method", "pdf-invoice.paymentDetails": "Products", "pdf-invoice.phone": "Phone", "pdf-invoice.email": "Email", "pdf-invoice.deliveryAddress": "Delivery address:", "pdf-invoice.productName": "Product Name", "pdf-invoice.qty": "Qty", "pdf-invoice.unitPrice": "Unit price", "pdf-invoice.amount": "Amount", "pdf-invoice.totalExVAT": "Subtotal", "pdf-invoice.discount": "Discount", "pdf-invoice.vat": "VAT", "pdf-invoice.total": "Total (inc VAT)", "pdf-invoice.totalEx": "Total (ex VAT)", "pdf-invoice.generated-by": "This pdf is generated by Between. www.between.as ", "chat-widget.agentLabel": "Technical support", "chat-widget.autoResponseMessage": "Hi, do you have any technical problems? We are here to help!", "chat-widget.browserSideAuthorLabel": "", "chat-widget.collapsedMode": "chip", "chat-widget.conversationEndConfirmationQuestion": "Are you sure you want to end this chat?", "chat-widget.conversationEndLabel": "Your chat session has ended. Thanks for chatting!", "chat-widget.conversationEndMenuLinkLabel": "End <PERSON>", "chat-widget.conversationEndTranscriptPlaceholder": "Email to send transcript of chat", "chat-widget.conversationRatingLabel": "How would you rate this chat?", "chat-widget.conversationRatingPlaceholder": "How can we improve?", "chat-widget.conversationRatingThankYou": "Thanks for rating your chat session!", "chat-widget.conversationTranscriptSentThankYou": "Thanks! You will receive your transcript shortly.", "chat-widget.defaultCancelBtnLabel": "Cancel", "chat-widget.defaultNoBtnLabel": "No", "chat-widget.defaultOkBtnLabel": "Ok", "chat-widget.defaultSendBtnLabel": "Send", "chat-widget.defaultYesBtnLabel": "Yes", "chat-widget.noAnswerWithEmail": "Oops! Sorry no one has responded yet. We have your email on file if you need to leave or you can continue to wait.", "chat-widget.noAnswerWithoutEmail": "Oops! Sorry no one has responded yet. We have your email on file if you need to leave or you can continue to wait.", "chat-widget.offlineEmailPlaceholder": "Email", "chat-widget.offlineGreeting": "Sorry we are away, but we would love to hear from you and chat soon!", "chat-widget.offlineMessagePlaceholder": "Your message here", "chat-widget.offlineNamePlaceholder": "Name (optional but helpful)", "chat-widget.offlineSendButton": "Send", "chat-widget.offlineThankYouMessage": "Thanks for your message. We will be in touch soon!", "chat-widget.offlineTitle": "Contact Us", "chat-widget.onlineMessagePlaceholder": "Type message here...", "chat-widget.onlineTitle": "How can we help you?", "chat-widget.requestScreenshotAllowLabel": "Take screenshot", "chat-widget.requestScreenshotDeclineLabel": "Decline", "chat-widget.requestScreenshotText": "Operator would like to take a screenshot of your browser. Confirm below.", "chat-widget.requireInfoGreeting": "Enter your name and email to start chatting!", "chat-widget.requireInfoSubmitBtn": "Start", "chat-widget.status": "online", "chat-widget.titleColor": "#3f51b5", "chat-widget.titleFontColor": "#fff", "chat-widget.widgetAlignment": "bottomRight", "schedule.daily.everyDay": "every day", "schedule.daily.everyXDays": "every {{count}} days", "schedule.weekly.everyWeekOn": "every week on {{weekdays}}", "schedule.weekly.everyXWeeksOn": "every {{count}} weeks on {{weekdays}}", "schedule.monthly.everyMonthOnDate": "every month on the {{date}}.", "schedule.monthly.everyXMonthsOnDate": "every {{count}} months on the {{date}}.", "schedule.monthly.nthWeekdayEveryMonth": "{{nth}} {{weekday}} every month", "schedule.monthly.nthWeekdayEveryXMonths": "{{nth}} {{weekday}} every {{count}} months", "schedule.weekdays.1": "monday", "schedule.weekdays.2": "tuesday", "schedule.weekdays.3": "wednesday", "schedule.weekdays.4": "thursday", "schedule.weekdays.5": "friday", "schedule.weekdays.6": "saturday", "schedule.weekdays.7": "sunday", "schedule.nth.1": "first", "schedule.nth.2": "second", "schedule.nth.3": "third", "schedule.nth.4": "fourth", "verification.title": "Are you sure?", "footer.credits": "Software by Between AS"}
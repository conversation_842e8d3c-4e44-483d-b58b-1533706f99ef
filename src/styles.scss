@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Readex+Pro:wght@400;700&display=swap');

// Import Bootstrap's functions, variables, and mixins
@import "../node_modules/bootstrap/scss/functions";
@import "../node_modules/bootstrap/scss/variables";
@import "../node_modules/bootstrap/scss/mixins";
@import "../node_modules/bootstrap/scss/bootstrap";

// Override Bootstrap's default variables with your custom colors
$theme-colors: (
  "primary": #448c74,
  "secondary": #121515,
  "success": #249689,
  "info": #ffffff,
  "warning": #f9cf58,
  "danger": #d41c27,
  "light": #f6f9fc,
  "dark": #14181b
);


// Define other custom colors
$tertiary: #edcb50;
$alternate: #e0e3e7;

$accent-1: #3f4cfe;
$accent-2: #4d39d2c0;
$accent-3: #4dee8b60;
$accent-4: #cc5e1e1e;

// Utility colors
$primary-text: #14181B;
$secondary-text: #3c4545;
$primary-bg: #f6f9fc;
$secondary-bg: #ffffff;

// Font settings
$font-family-sans-serif: 'Inter', sans-serif;
$font-family-base: $font-family-sans-serif;
$headings-font-family: 'Readex Pro', sans-serif;
$font-size-base: 0.875rem; // 16px

// Custom font sizes based on the provided settings
$display-1-size: 4rem; // 64px
$display-2-size: 2.75rem; // 44px
$display-3-size: 2.25rem; // 36px
$display-4-size: 2rem; // 32px
$font-size-lg: 1.25rem; // 20px
$font-size-md: 1rem; // 16px
$font-size-sm: 0.875rem; // 14px
$font-size-xs: 0.75rem; // 12px

// Override Bootstrap headings
$headings-font-weight: 500;
$headings-line-height: 1.2;
$headings-color: $primary-text;

h1, .h1 { font-size: $display-1-size; font-family: $headings-font-family; }
h2, .h2 { font-size: $display-2-size; font-family: $headings-font-family; }
h3, .h3 { font-size: $display-3-size; font-family: $headings-font-family; }
h4, .h4 { font-size: $display-4-size; font-family: $headings-font-family; }
h5, .h5 { font-size: 1.25rem; font-family: $headings-font-family; }
h6, .h6 { font-size: 1rem; font-family: $headings-font-family; }

.title {
  font-size: 0.875rem;
  font-family: $headings-font-family;
}

.font-size-lg {
  font-size: $font-size-lg; // 1.25rem (20px)
}

.font-size-md {
  font-size: $font-size-md; // 1rem (16px)
}

.font-size-sm {
  font-size: $font-size-sm; // 0.875rem (14px)
}

.font-size-xs {
  font-size: $font-size-xs; // 0.75rem (12px)
}

p{
  font-size: $font-size-base;
  color: $primary-text;
}

// Override input border color
$input-border-color: $alternate; // Replace with your desired border color
$input-border-width: 2px;

// Override hr settings

hr{
  color: #bdc0c3 !important;
  };



// Override Bootstrap's border settings
$border-width:                1px;
$border-widths: (
  1: 1px,
  2: 2px,
  3: 3px,
  4: 4px,
  5: 5px
);
$border-style:                solid;
$border-color:                $alternate;
$border-color-translucent:    rgba($black, .175);

// Button customizations
$btn-border-radius: 0.625rem; // 10px
$btn-padding-y: 0.75rem; // 12px (top and bottom)
$btn-padding-x: 1.25rem; // 20px (left and right)
$btn-border-width: 2px;
$btn-primary-color: #ffffff !important; // Text color for primary button
$btn-secondary-color: #ffffff !important; // Text color for secondary button
$btn-success-color: #ffffff !important; // Text color for success button
$btn-info-color: #14181b !important; // Text color for info button
$btn-warning-color: #14181b !important; // Text color for warning button
$btn-danger-color: #ffffff !important; // Text color for danger button
$btn-light-color: #14181b !important; // Text color for light button
$btn-dark-color: #ffffff !important; // Text color for dark button

.btn-dark-color{
    color: #ffffff !important;
    background-color: $primary-text !important;
    border-color: $primary-text !important;
    border-radius: 6.25rem !important;
    padding: 0.3rem 0.75rem !important  ;
}

.btn {
  font-family: $headings-font-family !important;
}

.btn-primary {
  color: #ffffff !important;
}


.page-container{
  padding: 100px 0 1rem 0;

  @media (min-width: 768px) {
    padding: 130px 4rem 2rem 4rem;
  }
}

:root {
  --primary-color: #448c74;
  --alternate: $alternate;
  --danger-color: #dc5d56;
  --warning-color: #f3db8e;
}

.contactPage-container {
  padding: 100px 1rem 1rem 0;
}

.component-container{
  padding: 0.75rem;
}


.bg-green{
  background-color: #448c74;
}

.contact-container{
  padding: 0.75rem;
}


i {
  color: $secondary-text
}

.black {
  color: $primary-text;
}

.contact-container{
  padding: 0.75rem;
}

.form-check-input:checked{
  color: $primary-text;
  background-color: #448c74;
  border-color: #448c74;
}

input.form-control:focus {
  border-color: #448c74 !important; /* Change this to your desired color */
  box-shadow: 0 0 0 0.2rem rgba(68, 140, 116, 0.25) !important; /* Optional: Adds a shadow effect */
}
.shadow-custom {
  box-shadow: 0px 24px 48px 0px rgba(158, 158, 158, 0.20);
}
.small {
  border-radius: 30px;
  padding: 0.5rem 1.5rem;
}

html, body { height: 100%; }
body { margin: 0;}

html, body { height: 100%; }
body { margin: 0;}


* {
  touch-action: manipulation;
}

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }


.btn-link{
  color: var(--primary-color);
}

.btn-link:hover {
  color: var(--primary-color);
  opacity: 0.75;
}

.bottom-modal .modal-dialog {
  position: fixed;
  bottom: 0;
  margin: auto;
  left: 0;
  right: 0;
  transform: none;
  width: auto;
  max-width: 600px;
}

.bottom-section {
  margin-top: auto;
}

.app-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh
}

// Bottom sheet modal styles
.bottom-sheet-modal {
  .modal-dialog {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    max-width: none;
    width: 100%;

    .modal-content {
      border: none;
      border-radius: 16px 16px 0 0;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }
  }
}

// Prevent body scrolling when modal is open on mobile
@media (max-width: 767.98px) {
  body.modal-open {
    position: fixed;
    width: 100%;
  }
}

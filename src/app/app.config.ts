import { APP_INITIALIZER, ApplicationConfig, ErrorHandler, importProvidersFrom, LOCALE_ID, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, Router } from '@angular/router';

import { routes } from './app.routes';
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { CommonModule, registerLocaleData } from '@angular/common';
import { TranslateModule, TranslateLoader,  } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {ServiceWorkerModule} from "@angular/service-worker";
import {environment} from "../environments/environment";
import * as Sentry from "@sentry/angular";
import localeNo from '@angular/common/locales/nb';
import localeEn from '@angular/common/locales/en';

registerLocaleData(localeNo);
registerLocaleData(localeEn);


export const appConfig: ApplicationConfig = {

  providers: [
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler({
        showDialog: false,
      }),
    }, {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    {
      provide: APP_INITIALIZER,
      useFactory: () => () => {},
      deps: [Sentry.TraceService],
      multi: true,
    },
    { provide: LOCALE_ID, useValue: 'nb' }, // Default to Norwegian
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(),
    BrowserModule,
    CommonModule,
    importProvidersFrom(HttpClient),
    importProvidersFrom(
      // Translate module
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
      }}),

      // Version check/update module
      ServiceWorkerModule.register('ngsw-worker.js', {
        enabled: environment.production,
        // Register the ServiceWorker as soon as the application is stable or after 30 seconds (whichever comes first)
        registrationStrategy: 'registerWhenStable:5000'
      })
    ), provideAnimationsAsync('noop'), provideAnimationsAsync(), provideAnimationsAsync(),
  ]
};

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

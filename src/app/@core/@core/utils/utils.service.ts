import { Injectable } from '@angular/core';
import {format} from "date-fns";
import {TranslateService} from "@ngx-translate/core";
import {
  OrderLineResponse,
  OrderResponse, OrderScheduleOrderLineResponse,
  OrderScheduleResponse,
} from "../../../@shared/models/order/order.module";



export function convertResponseDatetime(obj: any) {
  for (const prop in obj) {
    if (obj.hasOwnProperty(prop)) {

      // If prop is object, iterate recursively
      if (typeof obj[prop] === 'object' && obj[prop] !== null) {
        convertResponseDatetime(obj[prop]);
      }

      // If prop is string and ISO date, convert to local datetime
      else if (typeof obj[prop] === 'string' && isISODateString(obj[prop])) {
        const isoDateString = obj[prop].replace(' ', 'T');
        const date = new Date(isoDateString);
        obj[prop] = convertDateUTCToLocal(date);
      }
    }
  }
}


export function currencyFormat(number: number | null | undefined, showDecimals: boolean = true): string {
  if (number === null || number === undefined) {
    number = 0;
  }
  const decimals = showDecimals ? 2 : 0;
  const formattedNumber = number.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ' ').replace('.', ',');
  return (showDecimals ? '' : '') + formattedNumber + (showDecimals ? '' : ',-');
}

  export function convertPayloadDatetime(data: any) {
    for (const prop in data) {
      if (data.hasOwnProperty(prop)) {
        if (typeof data[prop] === 'object' && data[prop] !== null) {
          convertPayloadDatetime(data[prop]);
        }
        else if (typeof data[prop] === 'string' && isISODateString(data[prop])) {
          let date = new Date(data[prop]);
          date = convertDateLocalToUTC(date)
          data[prop] = format(date, 'yyyy-MM-dd HH:mm:ss');
        }
        if (data[prop] instanceof Date) {
          data[prop] = convertDateLocalToUTC(data[prop])
          data[prop] = format(data[prop], 'yyyy-MM-dd HH:mm:ss');
        }
      }
    }
    return data;
  }

function convertDateUTCToLocal(date: Date) {
  return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
}


function convertDateLocalToUTC(date: Date) {
  return new Date(date.getTime() + date.getTimezoneOffset() * 60000)
}


function isISODateString(value: string): boolean {
  const isoDatePattern = /^\d{4}-\d{2}-\d{2}/;
  return isoDatePattern.test(value);
}

@Injectable({
  providedIn: 'root'
})
export class UtilsService {

  constructor( private translate: TranslateService) { }


  // getOrderData(order : OrderResponse, type : keyof OrderLineResponse){
  //   let response : OrderLineResponse[keyof OrderLineResponse];
  //   order.order_lines?.map((line : OrderLineResponse) => {
  //   });
  //   return response!;
  // }

  getOrderData(order: OrderResponse, type: keyof OrderLineResponse): OrderLineResponse[keyof OrderLineResponse] | undefined {
    if (order.order_lines) {
      const line = order.order_lines.find((line) => line[type] !== undefined);
      return line ? line[type] : undefined;
    }
    return undefined;
  }

  getOrderScheduleData(order : OrderScheduleResponse, type : keyof OrderScheduleOrderLineResponse){
    let response : OrderScheduleOrderLineResponse[keyof OrderScheduleOrderLineResponse];
    order.order_lines?.map((line : OrderScheduleOrderLineResponse) => {
      if(line.is_taskable === 1){
        response = line[type];
      }
    });
    return response!;
  }




  formatDateTime(date: Date | string | undefined, format: string): string {
    if (!date) {
      return '';
    }

    const d = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(d.getTime())) {
      return '';
    }

    // Define mappings for weekdays and months
    const en_weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const en_months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    const no_weekdays = ['Søndag', 'Mandag', 'Tirsdag', 'Onsdag', 'Torsdag', 'Fredag', 'Lørdag'];
    const no_months = ['januar', 'februar', 'mars', 'april', 'mai', 'juni', 'juli', 'august', 'september', 'oktober', 'november', 'desember'];

    //get current translation (en/no)
    const lang = this.translate.currentLang;
    const weekdays = lang === 'no' ? no_weekdays : en_weekdays;
    const months = lang === 'no' ? no_months : en_months;

    const weekday = weekdays[d.getDay()];
    const day = d.getDate();
    const month = months[d.getMonth()];
    const fullYear = d.getFullYear().toString();
    const twoDigitYear = fullYear.slice(-2);
    const hours = d.getHours();
    const minutes = d.getMinutes();

    let formattedDate = format
      .replace('EEEE', `${weekday}`)
      .replace('DD', `${day < 10 ? '0' + day : day}`)
      .replace('MMMM', `${month}`)
      .replace('HH', `${hours < 10 ? '0' + hours : hours}`)  // Using HH for hours
      .replace('mm', `${minutes < 10 ? '0' + minutes : minutes}`);

    if (format.includes('yyyy')) {
      formattedDate = formattedDate.replace('yyyy', `${fullYear}`);
    }
    if (format.includes('yy')) {
      formattedDate = formattedDate.replace('yy', `${twoDigitYear}`);
    }

    return formattedDate;
  }

  getOrderLineQuantity(order: OrderResponse, productId: number): number {
    let quantity = 0;
    if (order.order_lines) {
      for (const line of order.order_lines) {
        if (line.product_id === productId) {
          quantity = line.quantity;
          break;
        }
      }
    }
    return quantity;
  }


  isUpsellProduct(order : OrderResponse, productId : number): number | null{
    let response : number | null = null;
    order.order_lines?.forEach((line : OrderLineResponse) => {
      if(line.product_id === productId){
        response = line.order_line_id;
      }
    });
    return response;
  }


  formatCurrency(number: number, showDecimals: boolean = true): string {
    const decimals = showDecimals ? 2 : 0;
    const formattedNumber = number?.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ' ').replace('.', ',');
    return (showDecimals ? '' : '') + formattedNumber + (showDecimals ? '' : ',-');
  }

}

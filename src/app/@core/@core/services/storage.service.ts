import { Injectable } from '@angular/core';
import {ToastService} from "./toast.service";
const USER_KEY = 'auth-user';
const COMPANY_KEY = 'selected-company';

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  
  constructor() {}
  local = window.localStorage;
  session = window.sessionStorage;
  private orderIdKey = 'orderId';
  private accessLevelKey = 'accessLevel';
  private serviceRecipientKey = 'serviceRecipient';


  set(key: string, value: string) {
    this.local.setItem(key, value);
  }

  get(key: string): string | null {
    return this.local.getItem(key);
  }

  remove(key: string) {
    this.local.removeItem(key);
  }

  public isLoggedIn(): boolean {
    const user = this.local.getItem(USER_KEY);
    return !!user;
  }

  clean(): void {
    window.localStorage.removeItem(this.orderIdKey);
    window.localStorage.removeItem(this.accessLevelKey);
    window.localStorage.removeItem(this.serviceRecipientKey);
    this.session.clear();
    this.local.clear();
  }

  // public getSelectedCompanyId(silent= false): string {
  //   const company = this.local.getItem(COMPANY_KEY);
  //   if (company) {
  //     return JSON.parse(company).entity_id;
  //   } else {
  //     if (!silent) {
  //         this.toastService.errorToast('missing_company_id')
  //     }
  //     throw new Error('No company selected - getSelectedCompanyId()');
  //   }
  // }

  public getUser(): any {
    const user = localStorage.getItem(USER_KEY);
    if (user) {
      return JSON.parse(user);
    }

    return {};
  }

  public saveUser(user: any): void {
    localStorage.removeItem(USER_KEY);
    localStorage.setItem(USER_KEY, JSON.stringify(user));
  }


  public saveSelectedCompany(company: any): void {
    localStorage.removeItem(COMPANY_KEY);
    localStorage.setItem(COMPANY_KEY, JSON.stringify(company));
  }

  

  setOrderId(orderId: string): void {
    window.localStorage.setItem(this.orderIdKey, orderId);
  }


  public getSelectedCompanyId(): string {
    const company = localStorage.getItem(COMPANY_KEY);
    if (company) {
      return JSON.parse(company).company_id;
    }
    return '';
  }

  public getSelectedCompany(): any {
    const company = this.local.getItem(COMPANY_KEY);
    if (company) {
      return JSON.parse(company);
    }
    return {};
  }

  // getOrderId(): string | null {
  //   return window.localStorage.getItem(this.orderIdKey);
  // }

  public getOrderId() {
    const orderId = localStorage.getItem('order_id');
    if(orderId){
      return JSON.parse(orderId);
    }

    return;
  }

  setAccessLevel(accessLevel: string): void {
    window.localStorage.setItem(this.accessLevelKey, accessLevel);
  }

  getAccessLevel(): string | null {
    return window.localStorage.getItem(this.accessLevelKey);
  }

  setServiceRecipient(serviceRecipient: string): void {
    window.localStorage.setItem(this.serviceRecipientKey, serviceRecipient);
  }

  getServiceRecipient(): string | null {
    return window.localStorage.getItem(this.serviceRecipientKey);
  }


}

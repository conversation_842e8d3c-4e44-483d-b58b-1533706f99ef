import { Injectable } from '@angular/core';
import {CookieService} from "ngx-cookie-service";

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private readonly TOKEN_KEY = 'ACCESS_TOKEN';

  constructor(private cookieService: CookieService) {}

  // Save the token in a secure cookie
  saveToken(token: string): void {
    console.log('Saving token:', token ? 'Token exists' : 'Token is empty');

    // Get the current domain
    const domain = window.location.hostname;
    console.log('Current domain:', domain);

    // Set the cookie with appropriate options
    this.cookieService.set(this.TOKEN_KEY, token, {
      expires: 1,
      secure: true,
      sameSite: 'Lax', // Changed from 'Strict' to 'Lax' to allow cross-site requests
      path: '/',
      // Don't set domain for localhost, but set it for other environments
      ...(domain !== 'localhost' && { domain })
    });

    // Also save to localStorage as a backup
    localStorage.setItem(this.TOKEN_KEY, token);

    console.log('Token saved successfully to cookie and localStorage');
  }

  // Retrieve the token from secure cookies or localStorage
  getToken(): string | null {
    // Try to get the token from the cookie first
    let token = this.cookieService.get(this.TOKEN_KEY) || null;

    // If not found in cookie, try localStorage
    if (!token) {
      token = localStorage.getItem(this.TOKEN_KEY);
      if (token) {
        console.log('Token found in localStorage but not in cookie, restoring cookie');
        // Restore the cookie from localStorage
        this.saveToken(token);
      }
    }

    console.log('Getting token:', token ? 'Token exists' : 'Token is null');
    return token;
  }

  // Remove the token from cookies and localStorage
  removeToken(): void {
    console.log('Removing token');
    // Remove from cookie
    this.cookieService.delete(this.TOKEN_KEY, '/');
    // Remove from localStorage
    localStorage.removeItem(this.TOKEN_KEY);
    console.log('Token removed from cookie and localStorage');
  }

  // Check if the user is logged in by verifying if the token exists
  isLoggedIn(): boolean {
    console.log('isLoggedIn check called');
    const isLoggedIn = this.getToken() !== null;
    console.log('isLoggedIn result:', isLoggedIn);
    return isLoggedIn;
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { TokenService } from './token.service';
import { map, Observable } from 'rxjs';
import { CRM_ORD_38, USM_USR_8, USM_USR_9 } from '../../../@shared/models/input/input.service';
import { EndpointService } from '../../../@shared/services/endpoints.service';
import { LoginResponse } from '../../../@shared/models/authentication/authentication.module';
import { UserResponse } from '../../../@shared/models/user/user.module';
import { StorageService } from './storage.service';
import { GetResponse } from '../../../@shared/models/global/response-wrapper.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(
    private http: HttpClient,
    private tokenService: TokenService,
    private router: Router,
    private endpointService: EndpointService,
    private storageService: StorageService) {}

  login(params : CRM_ORD_38): Observable<any> {
    return this.endpointService.crm_ord_38(params).pipe(map((data)=>{
        return data;
    }));
  }

  logout(): void {
    this.tokenService.removeToken();
    this.router.navigate(['/login']);
  }

  handleLoginSuccess(response: LoginResponse): void {
    console.log('handleLoginSuccess called');
    const token = response.access_token;
    console.log('Token from response:', token ? 'Token exists' : 'Token is empty');
    this.tokenService.saveToken(token);
    this.router.navigate(['/orders-overview']); // Redirect after login
  }

  handleLoginSuccessFromOrderToken(response: LoginResponse): void {
    console.log('handleLoginSuccessFromOrderToken called');
    const token = response.access_token;
    console.log('Token from order token response:', token ? 'Token exists' : 'Token is empty');
    this.tokenService.saveToken(token);
  }

  initiateOtp(params: USM_USR_9) {
    return this.endpointService.usm_usr_9(params).pipe(map((data) => {
      return data;
    }));
  }

  sendOtpAndLogin(params: USM_USR_8) {
    return this.endpointService.usm_usr_8(params).pipe(map((data) => {
      return data;
    }));
  }

  sendOtp(params: USM_USR_8) {
    return this.endpointService.usm_usr_8(params).pipe(map((data) => {
      return data;
    }));
  }

  fetchUser(): Observable<UserResponse> {
    return this.endpointService.usm_usr_3().pipe(
      map((response: GetResponse<UserResponse>) => {
        this.storageService.saveUser(response.data);
        return response.data; // Assuming `data` contains the UserResponse object
      })
    );
  }

}
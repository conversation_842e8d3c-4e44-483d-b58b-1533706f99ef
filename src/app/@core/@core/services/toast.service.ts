import {Injectable, MissingTranslationStrategy, TemplateRef} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";

export interface Toast {
  message: string;
	classname?: string;
	delay?: number;
}

@Injectable({ providedIn: 'root' })
export class ToastService {
	toasts: Toast[] = [];

	showError(message: string) {
    let toast: Toast = {
      message: message,
      classname: 'error-toast message-danger',
      delay: 5000
    }
    this.toasts.push(toast);
	}

	remove(toast: Toast) {
		this.toasts = this.toasts.filter((t) => t !== toast);
	}

	clear() {
		this.toasts.splice(0, this.toasts.length);
	}
}

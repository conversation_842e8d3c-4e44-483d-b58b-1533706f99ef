import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from "@ngx-translate/core";
import { CommonModule } from "@angular/common";

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss']
})
export class ButtonComponent {
  @Input() buttonClass: string = 'btn btn-primary';
  @Input() borderRadius: string = '0.625rem';
  @Input() height: string = 'auto';
  @Input() isLoading: boolean = false;
  @Input() width: string = '';
  @Input() disabled: boolean = false;
  @Output() buttonClick = new EventEmitter<void>();

  get calculatedWidth(): string {
    // If width is provided, apply it with !important for priority
    return this.width ? `${this.width} !important` : 'auto';
  }

  onClick() {
    if (!this.isLoading && !this.disabled) {
      this.buttonClick.emit();
    }
  }
}

/* button.component.scss */

button {
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    opacity: 0.8;
  }

  @media (max-width: 575px) {
    width: 100% !important;
  }
}

/* Only apply primary-color background if .btn-primary is present */
button.btn-primary {
  background-color: var(--primary-color);

  &:disabled {
    background-color: var(--primary-color);
  }

  &:not(:disabled):hover {
    background-color: var(--primary-color);
    opacity: 0.8;
  }
}

import {Component, Input, OnInit} from '@angular/core';
import {OrderNoteResponse, OrderResponse} from "../../models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {ButtonComponent} from "../button/button.component";
import {FormsModule} from "@angular/forms";
import {OrderService} from "../../services/order.service";

@Component({
  selector: 'app-notes',
  standalone: true,
  imports: [CommonModule, TranslateModule, ButtonComponent, FormsModule],
  templateUrl: './notes.component.html',
  styleUrl: './notes.component.scss'
})
export class NotesComponent  implements OnInit {
  @Input() orderData: OrderResponse = {} as OrderResponse;
  notes: OrderNoteResponse[] = [];
  current_user_id: string = '';
  noteText: string = '';
  constructor(private storageService: StorageService, private translateService: TranslateService, private orderService: OrderService) {}

  ngOnInit(): void {
    this.orderService.order$.subscribe((order) => {
      this.orderData = order;
    });
    if (this.orderData) {
      this.getNotes();
    }
    this.current_user_id = this.storageService.getUser().user_id;
  }

  ngOnChanges(): void {
    this.getNotes();
  }

  getNotes(): void {
    this.notes = [];
    if(this.orderData.order_notes) {
      for (const note of this.orderData.order_notes) {
        this.notes.push(note);
      }
    }
  }

  getRelativeTime(date: Date | string | undefined): string {
    let inputDate;

    if (date === undefined) {
      return '';
    }

    if (!(date instanceof Date)) {
      inputDate = new Date(date);
    } else {
      inputDate = date;
    }
    const currentDate = new Date();
    const elapsed = currentDate.getTime() - inputDate.getTime();
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return days.toString() + ' ' + this.translateService.instant('notesCard.daysAgo');
    } else if (hours > 0) {
      return hours.toString() + ' ' + this.translateService.instant('notesCard.hoursAgo');
    } else if (minutes > 0) {
      return minutes.toString() + ' ' + this.translateService.instant('notesCard.minutesAgo');
    } else {
      return this.translateService.instant('notesCard.justNow');
    }
  }

  formatNote(note: OrderNoteResponse): string {
    let text = note.deleted_at ? "" : note.note_text;
    return note.internal ? `${text}` : text;
  }

  addNote(): void {
    let payload = {
      note_text: this.noteText,
      internal: 0,
      order_id: this.orderData.order_id
    };
    this.orderService.addOrderNoteToOrder(payload).subscribe({
      next: (res) => {
        this.notes.push(res);
        this.noteText = '';
      },
      error: (err) => {
        console.error('Failed to add note to order', err);
      }
    });
    }



}

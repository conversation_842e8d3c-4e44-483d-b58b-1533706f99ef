<div class="border rounded-4 bg-white">
  <div class="component-container">
  <div class="mb-3">
    <h6>{{ "notesCard.addNotes" | translate }}</h6>
    <div><textarea required="" type="text" name="note" [(ngModel)]="noteText" id="note_input" maxlength="1000" class="form-control form-control-sm" rows="3"></textarea>
      <div class="invalid-feedback">
        {{ "orderDetails.notes.description" | translate }}
      </div>
    </div>

    <!--    Notes Footer    -->
    <div class="footer-border">
      <div class="d-sm-flex justify-content-end mt-2">
        <app-button [disabled]="noteText.length == 0" [buttonClass]="'btn btn-primary py-1'" (buttonClick)="addNote()">{{ "notesCard.addNotes.btn" | translate }}</app-button>
      </div>
    </div>
  </div>


<div id="written-message" class="row custom-message-line mb-2 mx-1" *ngFor="let note of notes">

    <div class="col-auto profile-image align-self-top ms-0 ps-0 me-1 pe-1">
      <img *ngIf="note.updated_by_profile_image_url" class="avatar-s rounded-image" src="{{note.updated_by_profile_image_url}}" alt="" width="32px" height="32px">
      <img *ngIf="!note.updated_by_profile_image_url" class="avatar-s rounded-image" src="/assets/images/no-profile-image.jpeg" alt="No profile image" width="32px" height="32px"></div>
    <div class="col justify-content-start ps-0 pe-0">
      <span class="title" style="color: #313A46;">{{ note.updated_by_name }}</span>
      <span class="font-size-xs mt-0 mb-0 ms-2">{{ note.deleted_at ? 'Deleted ' + getRelativeTime(note.deleted_at) : note.updated_at ? 'Updated ' + getRelativeTime(note.updated_at) : getRelativeTime(note.created_at) }}</span>
      <div [ngClass]="note.updated_by == current_user_id ? 'written-message-background-self' : 'written-message-background-other'" class="mt-1">
        <p class="mb-0 mt-0 font-14" [innerHTML]="formatNote(note)"></p>
      </div>
</div>
  </div>
</div>
</div>





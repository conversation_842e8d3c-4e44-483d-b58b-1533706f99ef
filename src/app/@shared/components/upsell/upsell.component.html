<div *ngIf="upsellProducts.length > 0" class="border rounded-4 mb-3 bg-white">
  <h6 *ngIf="upsellProducts.length > 0" class="m-3">{{ "images.title" | translate }}</h6>
  <div *ngFor="let product of upsellProducts">
    <div class="component-container">
      <div class="d-flex justify-content-between">
        <div class="title font-size-md">{{ product.product_name }}</div>
        <div>kr {{ getProductPriceIncVat(product) }} - per {{ product.unit_abbreviation }}</div>
      </div>
      <div>{{ product.product_description }}</div>

      <div class="d-flex mt-3" *ngIf="shouldShowAddRemoveButtons(product)">
        <app-button
          *ngIf="!utilsService.isUpsellProduct(orderData, product.product_id)"
          [isLoading]="individualLoadingStates[product.product_id]"
          buttonClass="btn btn-dark-color"
          [disabled]="loadingProductIds.has(product.product_id) || individualLoadingStates[product.product_id]"
          (buttonClick)="addUpsellProduct(product)">
          <span class="font-size-sm">{{ "upsell-products.addBtn" | translate }}</span>
        </app-button>

        <app-button
          *ngIf="isProductInOrderLines(product)"
          [isLoading]="individualLoadingStates[product.product_id]"
          buttonClass="btn btn-dark-color"
          [disabled]="loadingProductIds.has(product.product_id) || individualLoadingStates[product.product_id]"
          (buttonClick)="removeUpsellProduct(product, getOrderLineId(product))">
          {{ "upsell-products.removeBtn" | translate }}
        </app-button>
      </div>

      <div class="quantity-selector d-flex align-items-center w-100" *ngIf="!shouldShowAddRemoveButtons(product)">
        <button class="font-size-lg btn btn-outline-secondary quantity-button-minus"
                (click)="decreaseQuantity(product)"
                [disabled]="loadingProductIds.has(product.product_id) || individualLoadingStates[product.product_id] || utilsService.getOrderLineQuantity(orderData, product.product_id) <= 0">
          -
        </button>
        <input type="number" inputmode="numeric"
               [value]="utilsService.getOrderLineQuantity(orderData, product.product_id)"
               (change)="onQuantityChange($event, product)"
               [disabled]="loadingProductIds.has(product.product_id) || individualLoadingStates[product.product_id]"
               class="text-center input-border no-spinner quantity-input">
        <button class="font-size-lg btn btn-outline-secondary quantity-button-plus"
                (click)="increaseQuantity(product)"
                [disabled]="loadingProductIds.has(product.product_id) || individualLoadingStates[product.product_id]">
          +
        </button>
        <span
          class="ms-2 flex-grow-1 small-font">{{ "upsell-products.addUnit" | translate }} {{ product.unit_name }}</span>
      </div>
    </div>
  </div>
</div>

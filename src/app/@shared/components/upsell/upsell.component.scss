.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100px;
  margin-top: 10px;
}

.quantity-selector button {
  width: 30px;
  padding-left: 5px;
  padding-right: 5px;
  height: 24px;
  //border: none;
  background-color: #fff;
  cursor: pointer;
  font-size: 20px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-selector button:hover {
  background-color: #fff;
  color: inherit;
}

.quantity-selector span {
  font-size: 16px;
}

.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -moz-appearance: textfield;
}

.quantity-button, .quantity-input {
  width: 50px;
}

.small-font {
  font-size: 0.8rem !important;
}

.quantity-button-plus{
  width: 50px;
  border: 1px solid rgb(118, 118, 118);
  border-radius: 0 6px 6px 0 !important;
}

.quantity-button-minus{
  width: 50px;
  border: 1px solid rgb(118, 118, 118);
  border-radius: 6px 0 0 6px !important;
}

.input-border{
  border: none;
  width: 40px;
  height: 26px;
  border-top: 1px solid rgb(118, 118, 118);
  border-bottom: 1px solid rgb(118, 118, 118);
}

//
//border-top: 16px solid var(--primary-color);

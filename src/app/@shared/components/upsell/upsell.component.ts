import {ChangeDetectorRef, Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import { TranslateModule } from "@ngx-translate/core";
import { OrderLineResponse, OrderResponse } from "../../models/order/order.module";
import { CRM_ORD_42, CRM_ORD_46, CRM_ORD_47, CRM_PRD_34 } from "../../models/input/input.service";
import { UpsellProductResponse } from "../../models/product/product.module";
import { UpsellProductService } from "../../services/upsell-product.service";
import { CommonModule } from "@angular/common";
import { UtilsService } from "../../../@core/@core/utils/utils.service";
import { ButtonComponent } from "../button/button.component";

import { catchError, of, Subject, switchMap, takeUntil } from "rxjs";
import { OrderService } from "../../services/order.service";

@Component({
  selector: 'app-upsell',
  standalone: true,
  imports: [
    TranslateModule, CommonModule,
    ButtonComponent
  ],
  templateUrl: './upsell.component.html',
  styleUrl: './upsell.component.scss'
})
export class UpsellComponent implements OnInit, OnDestroy {
  orderData: OrderResponse = {} as OrderResponse;
  upsellProducts: Array<UpsellProductResponse | OrderLineResponse> = [];
  minusLoading: number | null = null;
  plusLoading: number | null = null;
  isLoading: boolean = false;
  loadingProductIds: Set<number> = new Set();
  individualLoadingStates: { [productId: number]: boolean } = {};

  private updateQuantitySubject = new Subject<{ product: UpsellProductResponse | OrderLineResponse, quantity: number }>();
  private destroy$ = new Subject<void>();
  private cancelCalculation$ = new Subject<void>();

  constructor(
    private upsellProductsService: UpsellProductService,
    public utilsService: UtilsService,
    private orderService: OrderService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.orderService.order$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(order => {
      this.orderData = order;
      if (this.orderData) {
        this.fetchUpsellProducts();
      }
    });

    this.updateQuantitySubject.pipe(
      switchMap(({ product, quantity }) => this.updateQuantity(product, quantity)),
      catchError(error => {
        console.error('Error updating quantity:', error);
        this.fetchUpsellProducts();
        return of(null);
      }),
      takeUntil(this.destroy$)
    ).subscribe(res => {
      if (res) {
        this.updateOrderData(res);
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.cancelCalculation$.next();
    this.cancelCalculation$.complete();
  }

  increaseQuantity(product: UpsellProductResponse | OrderLineResponse) {
    const orderLine = this.orderData.order_lines.find(line => line.product_id === product.product_id);
    if (orderLine) {
      orderLine.quantity++;
      this.updateQuantitySubject.next({ product: orderLine, quantity: orderLine.quantity });
      this.cdr.detectChanges();
    } else {
      this.addUpsellProduct(product, true);
    }
  }

  decreaseQuantity(product: UpsellProductResponse | OrderLineResponse) {
    const orderLine = this.orderData.order_lines.find(line => line.product_id === product.product_id);
    if (orderLine) {
      if (orderLine.quantity > 1) {
        orderLine.quantity--;
        this.updateQuantitySubject.next({ product: orderLine, quantity: orderLine.quantity });
        this.cdr.detectChanges();
      } else {
        this.removeUpsellProduct(product, orderLine.order_line_id);
      }
    }
  }

  updateQuantity(product: UpsellProductResponse | OrderLineResponse, newQuantity: number) {
    if (this.isOrderLineResponse(product)) {
      const params: CRM_ORD_47 = {
        order_id: this.orderData.order_id,
        order_line: {
          order_line_id: product.order_line_id,
          product_id: product.product_id,
          quantity: newQuantity
        }
      };

      return this.upsellProductsService.updateUpsellProduct(params).pipe(
        takeUntil(this.cancelCalculation$)
      );
    }
    return of(null);
  }

  addUpsellProduct(product: UpsellProductResponse | OrderLineResponse, withDelay: boolean = false) {
    const productId = product.product_id;
    if (this.individualLoadingStates[productId]) return;

    this.isLoading = true;
    this.minusLoading = productId;
    this.plusLoading = productId;
    this.loadingProductIds.add(productId);
    this.individualLoadingStates[productId] = true;

    const params: CRM_ORD_42 = {
      order_id: this.orderData.order_id,
        product_id: productId,
        quantity: 1,
    };

    this.upsellProductsService.addUpsellProduct(params).pipe(
      takeUntil(this.cancelCalculation$)
    ).subscribe({
      next: (res) => {
        if (res) {
          this.orderData = {
            ...this.orderData,
            order_lines: [...this.orderData.order_lines, ...res.order_lines]
          };
          this.updateOrderData(res);
          console.log("ERERERERRERE ", res)
          if (withDelay) {
            setTimeout(() => {
              this.isLoading = false;
              this.minusLoading = null;
              this.plusLoading = null;
              this.loadingProductIds.delete(productId);
              this.individualLoadingStates[productId] = false;
              this.cdr.detectChanges();
            }, 500);  // Delay of 500ms
          } else {
            this.isLoading = false;
            this.minusLoading = null;
            this.plusLoading = null;
            this.loadingProductIds.delete(productId);
            this.individualLoadingStates[productId] = false;
            this.cdr.detectChanges();
          }
        }
      },
      error: (error) => {
        console.error('Error adding upsell product:', error);
        this.isLoading = false;
        this.minusLoading = null;
        this.plusLoading = null;
        this.loadingProductIds.delete(productId);
        this.individualLoadingStates[productId] = false;
      },
    });
  }

  addUpsellProductWithQuantity(product: UpsellProductResponse | OrderLineResponse, quantity: number) {
    const productId = product.product_id;
    if (this.individualLoadingStates[productId]) return;

    this.isLoading = true;
    this.minusLoading = productId;
    this.plusLoading = productId;
    this.loadingProductIds.add(productId);
    this.individualLoadingStates[productId] = true;

    const params: CRM_ORD_42 = {
      order_id: this.orderData.order_id,
      product_id: productId,
      quantity: quantity,
    };

    this.upsellProductsService.addUpsellProduct(params).pipe(
      takeUntil(this.cancelCalculation$)
    ).subscribe({
      next: (res) => {
        console.log(res)
        if (res) {
          this.orderData = {
            ...this.orderData,
            order_lines: [...this.orderData.order_lines, ...res.order_lines]
          };

          this.updateOrderData(res);
        }
        this.isLoading = false;
        this.minusLoading = null;
        this.plusLoading = null;
        this.loadingProductIds.delete(productId);
        this.individualLoadingStates[productId] = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error adding upsell product:', error);
        this.isLoading = false;
        this.minusLoading = null;
        this.plusLoading = null;
        this.loadingProductIds.delete(productId);
        this.individualLoadingStates[productId] = false;
      },
    });
  }

  removeUpsellProduct(product: OrderLineResponse | UpsellProductResponse, orderLineId?: number) {
    const productId = this.isOrderLineResponse(product) ? product.order_line_id : orderLineId ?? null;
    if (productId === null || this.individualLoadingStates[productId]) return;

    this.isLoading = true;
    this.minusLoading = productId;
    this.plusLoading = productId;
    this.loadingProductIds.add(productId);
    this.individualLoadingStates[productId] = true;

    const orderLine = this.orderData.order_lines.find(line => line.order_line_id === productId);

    if (orderLine) {
      const params: CRM_ORD_46 = {
        order_line_id: orderLine.order_line_id
      };

      this.upsellProductsService.removeUpsellProduct(params).pipe(
        takeUntil(this.cancelCalculation$)
      ).subscribe({
        next: (res) => {
          if (res) {
            this.orderData = {
              ...this.orderData,
              order_lines: this.orderData.order_lines.filter(line => line.order_line_id !== orderLine.order_line_id)
            };
            this.updateQuantitySubject.next({ product: orderLine, quantity: orderLine.quantity });
            // this.cdr.detectChanges();
            this.updateOrderData(res);
            this.cdr.detectChanges();
          }
          this.isLoading = false;
          this.minusLoading = null;
          this.plusLoading = null;
          this.loadingProductIds.delete(productId);
          this.individualLoadingStates[productId] = false;
        },
        error: (err) => {
          console.error('Error removing upsell product:', err);
          this.isLoading = false;
          this.minusLoading = null;
          this.plusLoading = null;
          this.loadingProductIds.delete(productId);
          this.individualLoadingStates[productId] = false;
        }
      });
    } else {
      console.error('Order line not found for order line ID:', productId);
      this.isLoading = false;
      this.individualLoadingStates[productId] = false;
    }
  }

  onQuantityChange(event: Event, product: OrderLineResponse | UpsellProductResponse) {
    const target = event.target as HTMLInputElement;
    const newQuantityValue = target.value;
    const validQuantityRegex = /^[1-9][0-9]*$/;

    if (validQuantityRegex.test(newQuantityValue)) {
      const newQuantity = parseInt(newQuantityValue, 10);
      const orderLine = this.orderData.order_lines.find(line => line.product_id === product.product_id);

      if (orderLine) {
        orderLine.quantity = newQuantity;
        this.updateQuantitySubject.next({ product: orderLine, quantity: newQuantity });
      } else {
        this.addUpsellProductWithQuantity(product, newQuantity);
      }
      this.cdr.detectChanges();
    } else if (parseInt(newQuantityValue, 10) === 0 && this.isOrderLineResponse(product)) {
      this.removeUpsellProduct(product, product.order_line_id);
    }
  }

  updateOrderData(newOrderData: OrderResponse) {
    this.orderService.refreshOrder(newOrderData);
    this.isLoading = false;
    this.cdr.detectChanges();
  }

  fetchUpsellProducts() {
    const params: CRM_PRD_34 = {
      order_id: this.orderData.order_id
    };

    this.upsellProductsService.getUpsellProducts(params).pipe(
      takeUntil(this.cancelCalculation$)
    ).subscribe(res => {
      this.upsellProducts = res.map(product => {
        return {
          ...product,
          unit_name: product.unit_id !== 1 ? product.unit_name.toLowerCase() : 'kvadratmeter',
        };
      });

      this.orderData.order_lines.forEach((line: OrderLineResponse) => {
        if (
          line.added_by_customer === 2 &&
          !this.upsellProducts.some(
            (product: UpsellProductResponse | OrderLineResponse) =>
              product.product_id === line.product_id
          )
        ) {
          this.upsellProducts.push(line);
        }
      });

      // Remove duplicates based on product_id
      this.upsellProducts = this.upsellProducts.filter(
        (product, index, self) =>
          index === self.findIndex(
            (p) => p.product_id === product.product_id
          )
      );

      this.upsellProducts.sort(
        (a: UpsellProductResponse | OrderLineResponse, b: UpsellProductResponse | OrderLineResponse) =>
          a.product_id - b.product_id
      );
    });
  }

  getProductPriceIncVat(product: UpsellProductResponse | OrderLineResponse): number {
    if (this.isOrderLineResponse(product)) {
      return product.unit_price_inc_vat;
    } else {
      return product.product_price_inc_vat;
    }
  }

  isOrderLineResponse(product: OrderLineResponse | UpsellProductResponse): product is OrderLineResponse {
    return (product as OrderLineResponse).order_line_id !== undefined;
  }

  isUpsellProductResponse(product: OrderLineResponse | UpsellProductResponse): product is UpsellProductResponse {
    return (product as UpsellProductResponse).payment_type_id !== undefined;
  }

  shouldShowAddRemoveButtons(product: UpsellProductResponse | OrderLineResponse): boolean {
    if (this.isUpsellProductResponse(product)) {
      return product.unit_id === 2;
    } else if (this.isOrderLineResponse(product)) {
      const matchingUpsellProduct = this.upsellProducts.find(
        (p) => p.product_id === product.product_id && this.isUpsellProductResponse(p)
      ) as UpsellProductResponse;

      if (matchingUpsellProduct) {
        return matchingUpsellProduct.unit_id === 2 && matchingUpsellProduct.payment_type_id === 1;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  isProductInOrderLines(product: UpsellProductResponse | OrderLineResponse): boolean {
    return this.orderData.order_lines.some(line => line.product_id === product.product_id);
  }

  getOrderLineId(product: UpsellProductResponse | OrderLineResponse): number | undefined {
    const line = this.orderData.order_lines.find(line => line.product_id === product.product_id);
    return line ? line.order_line_id : undefined;
  }
}

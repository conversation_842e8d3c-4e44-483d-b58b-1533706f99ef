.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.8); /* Optional: Add a background overlay */
    z-index: 1; /* Ensure the spinner is on top of other content */
  }

  .spinner {
    border: 16px solid #f3f3f3; /* Light grey */
    border-top: 16px solid var(--primary-color);
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

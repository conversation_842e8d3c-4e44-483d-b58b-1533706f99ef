


<div id="quotePdfContent" #content *ngIf="payment.payment_recipient">
  <!-- Header Section -->
  <div class="header">
    <div class="title">{{ "pdf-invoice.receiptTitle" | translate }}</div>
    <div class="logo">
      <img src="{{companyData.logo_url}}" style="max-height: 30px;" alt="">
    </div>
  </div>

  <!-- Information section -->
  <div class="information-section">
    <!-- Billed To Section -->
    <div class="section billed-to">
<!--      <div class="heading">{{ "pdf-invoice.paymentRecipient" | translate }}</div>-->
      <h4><strong>{{ payment.payment_recipient.name }}</strong></h4>
      <div class="text">{{ (payment.payment_recipient.phone) }}</div>
      <div class="text mb-2">{{ payment.payment_recipient.email }}</div>
      <div><strong>{{ "pdf-invoice.receiptNumber" | translate }}:</strong> #{{ payment.order_number}}</div>
      <div><strong>{{ "pdf-invoice.receiptDate" | translate }}:</strong> {{ payment.captured_at | date: 'EEEE d. MMM y': '' : 'nb-NO' }}</div>
<!--      <div class="mt-2 text" *ngIf="!payment.payment_recipient.is_private && payment.work_orders.length > 0 && order.work_orders[0].addresses.length > 0"><strong> {{ "pdf-invoice.deliveryAddress" | translate }}</strong> {{ order.work_orders[0].addresses[0].display}}</div>-->
    </div>


    <!-- Quotation Details Section -->
    <div *ngIf="companyData" class="section details">
<!--      <div class="heading">{{ "pdf-invoice.quotationDetailsHeading" | translate }}</div>-->
      <div class="text"><strong>{{ companyData.company_name }}</strong></div>
      <div class="text">{{ companyData.organisation_number }}</div>

      <!-- Address -->
      <div *ngIf="companyData?.address?.display" class="text">
        {{ companyData.address.display }}
      </div>

      <div class="text">{{ companyData.postal_code }} {{ companyData.city }}</div>
      <div class="text">{{ companyData.country }}</div>
      <div class="text">{{ companyData.phone }}</div>
      <div class="text">{{ companyData.email }}</div>
    </div>
  </div>

  <!-- Single payment order lines -->
  <div *ngIf="payment.order_lines.length > 0">
<!--    <h5 *ngIf="fixedRepeatedPayments.length > 0 || repeatedWorkOrderPayments.length > 0" class="mb-2 px-4">{{"pdf-invoice.singlePayment" | translate}}</h5>-->
    <table class="items-table">
      <!-- Table Head -->
      <thead class="table-header">
      <tr>
        <th>{{ "pdf-invoice.productName" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>
        <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>
      </tr>
      </thead>
      <!-- Table Body Order lines -->
      <tbody>
      <tr *ngFor="let orderLine of payment.order_lines">
        <td style="max-width: 300px;">{{ orderLine.order_line_name }}
          <div *ngIf="orderLine.comment"> <em>{{orderLine.comment}}</em></div>
        </td>
        <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>
        <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ (orderLine.unit_price_inc_vat) }}</td>
        <td *ngIf="order.show_prices_inc_vat === 1" class="text-right">{{ (orderLine.calculated_total_price_inc_vat) }}</td>
        <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ (orderLine.unit_price_ex_vat) }}</td>
        <td *ngIf="order.show_prices_inc_vat === 0" class="text-right">{{ (orderLine.calculated_total_price_ex_vat) }}</td>
      </tr>
      </tbody>
    </table>

    <div class="container-fluid pe-4">
      <div class="row justify-content-end">
        <div class="col-auto">
          <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.totalExVAT" | translate }}</div>
          <div *ngIf="payment.total_discount_amount_inc_vat">{{ "pdf-invoice.discount" | translate }}</div>
          <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>
          <div *ngIf="order.show_prices_inc_vat">{{ "pdf-invoice.vat" | translate }}</div>
          <div *ngIf="order.show_prices_inc_vat"><strong>{{ "pdf-invoice.total" | translate }}</strong></div>
        </div>
        <div class="col-auto">
          <div *ngIf="payment.total_discount_amount_inc_vat" class="text-right">{{ currencyFormat(payment.total_discount_amount_inc_vat + payment.total_amount_inc_vat) }}</div>
          <div class="text-right" *ngIf="payment.total_discount_amount_inc_vat">-{{ currencyFormat(payment.total_discount_amount_inc_vat) }}</div>
          <div><strong>{{ currencyFormat(payment.total_amount_ex_vat) }}</strong></div>
          <div *ngIf="order.show_prices_inc_vat" class="text-right">{{ currencyFormat(payment.total_amount_inc_vat - payment.total_amount_ex_vat) }}</div>
          <div *ngIf="order.show_prices_inc_vat"><strong>{{ currencyFormat(payment.total_amount_inc_vat) }}</strong></div>
        </div>
      </div>
    </div>
  </div>

</div>









<!--<div id="receiptPdfContent" #content *ngIf="payment.payment_recipient" class="pdf-container">-->
<!--  <div class="pdf-main-content">-->
<!--    &lt;!&ndash; Header Section &ndash;&gt;-->
<!--    <div class="header">-->
<!--      <div class="title">{{ "pdf-invoice.receiptTitle" | translate }}</div>-->
<!--      <div class="logo">-->
<!--        <img id="s3-logo-image" alt="{{ 'pdf-invoice.s3ImageAlt' | translate }}">-->
<!--      </div>-->
<!--    </div>-->

<!--    &lt;!&ndash; Information Section &ndash;&gt;-->
<!--    <div class="information-section">-->
<!--      &lt;!&ndash; Billed To Section &ndash;&gt;-->
<!--      <div class="section billed-to">-->
<!--        <div class="heading">{{ "pdf-invoice.paymentRecipient" | translate }}</div>-->
<!--        <h4><strong>{{ payment.payment_recipient.name }}</strong></h4>-->
<!--        <div class="text">123 12 312</div>-->
<!--        <div class="text mb-2">{{ payment.payment_recipient.email }}</div>-->
<!--      </div>-->

<!--      &lt;!&ndash; Receipt Details Section &ndash;&gt;-->
<!--      <div class="section details">-->
<!--        <div class="heading">INFO</div>-->
<!--        <div><strong>{{ "pdf-invoice.receiptNumber" | translate }}:</strong> {{ payment.payment_number }}</div>-->
<!--&lt;!&ndash;        <div><strong>{{ "pdf-invoice.receiptDate" | translate }}:</strong> {{ payment.captured_at | date: 'EEEE d. MMM y': '' : 'nb-NO' }}</div>&ndash;&gt;-->
<!--        <div><strong>{{ "pdf-invoice.receiptDate" | translate }}:</strong> {{ payment.captured_at | date: 'EEEE d. MMM y': '' : 'nb-NO' }}</div>-->

<!--        <div><strong>{{ "pdf-invoice.paymentMethod" | translate }}:</strong> {{ payment.payment_method_name }}</div>-->
<!--      </div>-->
<!--    </div>-->

<!--    &lt;!&ndash; Order Lines Section &ndash;&gt;-->
<!--    <div *ngIf="payment.order_lines.length > 0">-->
<!--      <h5 class="mb-2 px-4">{{ "pdf-invoice.paymentDetails" | translate }}</h5>-->
<!--      <table class="items-table">-->
<!--        <thead class="table-header">-->
<!--        <tr>-->
<!--          <th>{{ "pdf-invoice.productName" | translate }}</th>-->
<!--          <th class="text-right">{{ "pdf-invoice.qty" | translate }}</th>-->
<!--          <th class="text-right">{{ "pdf-invoice.unitPrice" | translate }}</th>-->
<!--          <th class="text-right">{{ "pdf-invoice.amount" | translate }}</th>-->
<!--        </tr>-->
<!--        </thead>-->
<!--        <tbody>-->
<!--        <tr *ngFor="let orderLine of payment.order_lines">-->
<!--          <td>-->
<!--            {{ orderLine.product_name }}-->
<!--            <div *ngIf="orderLine.comment"><em>{{ orderLine.comment }}</em></div>-->
<!--          </td>-->
<!--          <td class="text-right">{{ orderLine.quantity }} {{ orderLine.unit_abbreviation }}</td>-->
<!--          <td class="text-right">{{ utilsService.formatCurrency(getUnitPrice(orderLine)) }}</td>-->
<!--          <td class="text-right">{{ utilsService.formatCurrency(getLineTotalPrice(orderLine)) }}</td>-->
<!--        </tr>-->
<!--        </tbody>-->
<!--      </table>-->

<!--      &lt;!&ndash; Totals Section &ndash;&gt;-->
<!--      <div class="container-fluid pe-4">-->
<!--        <div class="row justify-content-end">-->
<!--          <div class="col-auto">-->
<!--            <div *ngIf="payment.discount_amount">{{ "pdf-invoice.totalExVAT" | translate }}</div>-->
<!--            <div *ngIf="payment.discount_amount">{{ "pdf-invoice.discount" | translate }}</div>-->
<!--            <div><strong>{{ "pdf-invoice.totalEx" | translate }}</strong></div>-->
<!--            <div>{{ "pdf-invoice.vat" | translate }}</div>-->
<!--            <div><strong>{{ "pdf-invoice.total" | translate }}</strong></div>-->
<!--          </div>-->
<!--          <div class="col-auto">-->
<!--            <div *ngIf="payment.discount_amount" class="text-right">-->
<!--              {{ utilsService.formatCurrency(payment.discount_amount + payment.total_amount_inc_vat) }}-->
<!--            </div>-->
<!--            <div class="text-right" *ngIf="payment.discount_amount">-->
<!--              -{{ utilsService.formatCurrency(payment.discount_amount) }}-->
<!--            </div>-->
<!--            <div><strong>{{ utilsService.formatCurrency(payment.total_amount_ex_vat) }}</strong></div>-->
<!--            <div class="text-right">-->
<!--              {{ utilsService.formatCurrency(payment.total_amount_inc_vat - payment.total_amount_ex_vat) }}-->
<!--            </div>-->
<!--            <div><strong>{{ utilsService.formatCurrency(payment.total_amount_inc_vat) }}</strong></div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->

<!--  &lt;!&ndash; Footer Section &ndash;&gt;-->
<!--  <div style="max-height: 100px;" class="footer-section">-->
<!--    &lt;!&ndash; Terms and Conditions Section &ndash;&gt;-->
<!--    <div class="col section terms">-->
<!--      &lt;!&ndash; Optional: Add terms and conditions content here &ndash;&gt;-->
<!--    </div>-->

<!--    &lt;!&ndash; Company Information Section &ndash;&gt;-->
<!--    <div class="col section notes">-->
<!--      <div class="company-information">-->
<!--        <h5 class="mb-0 text-right" *ngIf="companyData">{{ companyData.company_name }}</h5>-->
<!--        <p class="m-0 text-right" *ngIf="companyData">{{ "pdf-invoice.phone" | translate }}: {{ displayPhoneNumber(companyData.phone) }}</p>-->
<!--        <p class="m-0 text-right" *ngIf="companyData">{{ "pdf-invoice.email" | translate }}: {{ companyData.email }}</p>-->
<!--      </div>-->
<!--    </div>-->
<!--    <div class="generated-by mb-2">{{ "pdf-invoice.generated-by" | translate }}</div>-->
<!--  </div>-->
<!--</div>-->

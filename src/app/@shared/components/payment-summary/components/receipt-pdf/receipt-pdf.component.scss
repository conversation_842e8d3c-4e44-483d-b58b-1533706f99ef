


//
//#receiptPdfContent {
//  max-width: 720px; /* Updated from 190mm */
//  margin: 0.5rem 1rem 0.5rem 1rem;
//  font-family: 'Helvetica', 'Arial', sans-serif;
//  font-size: 12px;
//  line-height: 1.4;
//  color: #333;
//  background: #FFF;
//  position: relative;
//}
//
///* Header */
//.header {
//  display: flex;
//  justify-content: space-between;
//  align-items: center;
//  padding: 20px 15px;
//  background-color: #e4f4f1;
//}
//
///* Information Section */
//.information-section {
//  display: flex;
//  justify-content: space-between;
//  padding: 10px 15px;
//}
//
///* Title */
//.title {
//  font-size: 24px;
//  font-weight: bold;
//  color: #448c74;
//}
//
///* Logo Image */
//.logo img {
//  max-width: 100%;
//  height: auto;
//}
//
///* Sections */
//.section {
//  margin-bottom: 24px;
//}
//
//.section .heading {
//  font-weight: bold;
//  margin-bottom: 8px;
//}
//
///* Items Table */
//.items-table {
//  width: 100%;
//  border-collapse: collapse;
//  margin-bottom: 24px;
//}
//
///* Table Cells */
//.items-table th,
//.items-table td {
//  border: 1px solid #ddd;
//  padding: 8px;
//}
//
///* Table Header */
//.items-table th {
//  background-color: #448c74;
//  color: white;
//}
//
///* Text Alignment */
//.text-right {
//  text-align: right;
//}
//
///* Footer Section */
//.footer-section {
//  background-color: #e4f4f1;
//  padding: 20px 15px;
//  position: relative;
//  bottom: 0;
//  width: 100%;
//  margin-top: 30px !important;
//}
//
///* Generated By */
//.generated-by {
//  position: absolute;
//  right: 0;
//  bottom: 0;
//  padding: 0 15px 0 0;
//  font-family: 'Helvetica', 'Arial', sans-serif;
//  font-size: 10px;
//  line-height: 1.4;
//  color: #333;
//}
//
///* Reset Margins */
//p {
//  margin: 0 !important;
//}
//
///* Page Breaks */
//@media print {
//  .pdf-container, .pdf-main-content, .footer-section {
//    page-break-inside: avoid;
//  }
//
//  .items-table tr {
//    page-break-inside: avoid;
//    page-break-after: auto;
//  }
//
//  .page-break {
//    page-break-before: always;
//  }
//}





#quotePdfContent {
  width: 794px; /* A4 width */
  min-height: 1122px; /* A4 height */
  font-family: 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  background: #FFF;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 45px 35px;
  background-color: #e4f4f1;
}

.information-section{
  display: flex;
  justify-content: space-between;
  padding: 20px 35px;
}
.title {
  font-size: 30px; /* Adjust as needed */
  font-weight: bold;
  color: #448c74
}

.logo img {
  max-width: 200px;  /* Maximum width is set */
  max-height: 120px;  /* Maximum height is set */
  width: auto;  /* Width adjusts to maintain the aspect ratio */
  height: auto;  /* Height adjusts to maintain the aspect ratio */
}



.section {
  margin-bottom: 24px; /* Adjust as needed */
}

.section .heading {
  font-weight: bold;
  margin-bottom: 8px;
}

.items-table {
  width: 100%;
  border-collapse: separate; /* This is required for border-radius to work */
  border-spacing: 0; /* Removes the default spacing between borders */
  margin-bottom: 24px; /* Adjust as needed */
  padding: 0px 35px;
}

/* Header cells */
.items-table th {
  background-color: #f2f2f2;
  padding: 8px;
  border: 1px solid #ddd;
}

.written-message-background-other {
  background-color: #f5f5f5;
  border-radius: 10px;
  padding: 7px 10px;
  margin-bottom: 10px;
  font-size: 12px;
}

.written-message-background-self {
  background-color: #dadcff;
  border-radius: 10px;
  padding: 7px 10px;
  margin-bottom: 10px;
}


/* Apply border-radius to the first and last th elements of the thead */
.items-table thead th:first-child {
  border-top-left-radius: 8px; /* Adjust as needed */
}

.items-table thead th:last-child {
  border-top-right-radius: 8px; /* Adjust as needed */
}

/* Remove border-radius for tbody if the table has multiple rows */
.items-table tbody tr:first-child td:first-child {
  border-top-left-radius: 0;
}

.items-table tbody tr:first-child td:last-child {
  border-top-right-radius: 0;
}

/* For a single row in tbody, if you want to have rounded bottom corners */
.items-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: 8px; /* Adjust as needed */
}

.items-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 8px; /* Adjust as needed */
}
.items-table th,
.items-table td {
  border: 0.5px solid #ddd;
  padding: 8px;
}

.items-table th {
  background-color: #448c74;
  color: white;
}

/* Hide border for empty cells */
.items-table tfoot td.empty-cell {
  border: none;
}

/* Right align the text in the content cells and apply border */
.items-table tfoot td.text-right {
  border: none;
  text-align: right;
  padding-right: 16px; /* Adjust as needed */
}

/* If you want to also add border to the bottom of the last row for cells with content */
.items-table tfoot tr:last-child td.text-right {
  border: none;
}


.text-right {
  text-align: right;
}

.footer-section {
  background-color: #e4f4f1;
  padding: 20px 35px;
  position: relative;
  bottom: 0;
  width: 100%;
  min-height: 200px;
  margin: 30px 0px 0px 0px !important;
}

.row {
  margin: 0;
}
.terms, .notes {
  font-size: 10px; /* Adjust as needed */
}

.generated-by {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0px 15px 0px 0px;
  font-family: 'Helvetica', 'Arial', sans-serif;
  font-size: 10px;
  line-height: 1.4;
  color: #333;
}

.product-description p {
  margin: 0 !important;
}

p{
  margin: 0 !important;
}

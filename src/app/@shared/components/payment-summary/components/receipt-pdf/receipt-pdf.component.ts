import {Component, Input} from '@angular/core';
import {OrderPaymentResponse} from "../../../../models/payment/payment.module";
import {CommonModule} from "@angular/common";
import {OrderLineResponse, OrderResponse} from "../../../../models/order/order.module";
import {currencyFormat, UtilsService} from "../../../../../@core/@core/utils/utils.service";
import {CompanyResponse} from "../../../../models/company/company.module";
import {TranslateModule} from "@ngx-translate/core";

@Component({
  selector: 'app-receipt-pdf',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './receipt-pdf.component.html',
  styleUrl: './receipt-pdf.component.scss'
})
export class ReceiptPdfComponent {
  @Input() payment!: OrderPaymentResponse;
  @Input() companyData: CompanyResponse = {} as CompanyResponse;
  @Input() order: OrderResponse = {} as OrderResponse;
  constructor(public utilsService: UtilsService) {
  }

  ngOnInit() {
    // console.log("compoanyData", this.companyData)
  }

  protected readonly currencyFormat = currencyFormat;
}

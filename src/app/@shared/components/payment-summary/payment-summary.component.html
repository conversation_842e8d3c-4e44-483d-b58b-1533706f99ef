<div *ngIf="orderData && orderPayments.length > 0 ">
  <div class="border rounded-4 mb-3">
    <div class="p-3">
      <h6 class="mb-3">{{ "payment.summary.yourPayments" | translate }}</h6>
      <ng-container *ngFor="let categoryKey of paymentCategoryOrder">

        <div *ngIf="categorizedPayments[categoryKey].length > 0">

          <!-- Category Heading -->
<!--          <h6 class="mb-2">{{ getCategoryHeading(categoryKey) }}</h6>-->

          <!-- Payments under this category -->
          <ng-container *ngFor="let payment of (showAllPayments[categoryKey] ? getNonZeroPayments(categorizedPayments[categoryKey]): (getNonZeroPayments(categorizedPayments[categoryKey]) | slice:0:3))">

            <!-- Hidden Receipt PDF Content -->
            <div style="position: absolute; top: -9999px; left: -9999px;" [id]="'receiptPdfContent' + payment.payment_id">
              <app-receipt-pdf [order]="orderData" [companyData]="companyData" [payment]="payment"></app-receipt-pdf>
            </div>

            <div class="border rounded-4 mb-3">
              <div class="component-container p-3">
                <div class="d-flex justify-content-between align-items-center m-1" style="cursor: pointer;" (click)="togglePaymentExpansion(payment.payment_id)"><div>
<!--                 <span>{{ "payment.summary.payment" | translate }} #{{ payment.payment_number }} - {{ payment.payment_name }}</span>-->
                  <span>{{ "payment.summary.payment" | translate }} #{{ payment.payment_number }} - {{ payment.payment_name }}</span>
                  <div class="fw-bold">kr {{ utilsService.formatCurrency(getPaymentTotal(payment), true) }}</div>
                  </div>
                  <div class="d-flex align-items-center">
                  <span class="custom-badge me-2"
                        [ngClass]="isPaid(payment.payment_status_id) ? 'paid-badge' : 'unpaid-badge'">
                    {{ payment.payment_status_name }}
                  </span>
                    <span>
                      <i *ngIf="expandedPayments[payment.payment_id]; else chevronDown" class="fa-regular fa-chevron-up"></i>
                      <ng-template #chevronDown><i class="fa-regular fa-chevron-down"></i></ng-template>
                    </span>
                  </div>
                </div>

                <div *ngIf="expandedPayments[payment.payment_id]" class="mt-3">
                  <app-order-lines [showHeader]="false" [showBorder]="false" [orderLines]="payment.order_lines" [template]="true"></app-order-lines>
                  <!-- Download Receipt Button for Paid Payments -->
                  <div *ngIf="categoryKey === 'paid'">
                    <app-button [buttonClass]="'btn btn-primary btn-sm w-100 mt-2'" (buttonClick)="generateOrderPdf(payment)">
                      {{ "payment.summary.receipt" | translate }}
                    </app-button>
                  </div>
                  <div class="mt-3 d-sm-flex justify-content-end"
                       *ngIf="payment.payment_id === firstEligiblePaymentId && showPaymentButton">
                  <app-payment-button [paymentSummary]="true" [orderData]="orderData"></app-payment-button>
                </div>
                </div>

              </div>
            </div>

          </ng-container>
          <!-- See More / See Less Button -->
          <div class="text-end" *ngIf="categorizedPayments[categoryKey].length > 2">
            <div class="btn btn-link p-0" (click)="toggleShowAll(categoryKey)">
              {{ showAllPayments[categoryKey] ? ('payment.summary.seeLess' | translate) : ('payment.summary.seeMore' | translate) }}
            </div>
          </div>


        </div>
      </ng-container>

    </div>
  </div>
</div>

import {Component, OnInit} from '@angular/core';
import {OrderService} from "../../services/order.service";
import {OrderResponse} from "../../models/order/order.module";
import {PaymentService} from "../../services/payment.service";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {CommonModule} from "@angular/common";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {HttpClient} from "@angular/common/http";
import jsPDF from "jspdf";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import html2canvas from "html2canvas";
import {ReceiptPdfComponent} from "./components/receipt-pdf/receipt-pdf.component";
import {CRM_COY_1} from "../../models/input/input.service";
import {CompanyResponse} from "../../models/company/company.module";
import {CompanyService} from "../../services/company.service";
import {OrderLinesSummaryComponent} from "../order-lines-summary/order-lines-summary.component";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {ButtonComponent} from "../button/button.component";
import {OrderLinesComponent} from "../order-lines/order-lines.component";
import {
  PaymentButtonComponent
} from "../../../pages/order/order-details/components/payment-button/payment-button.component";

@Component({
  selector: 'app-payment-summary',
  standalone: true,
  imports: [CommonModule, ReceiptPdfComponent, OrderLinesSummaryComponent, TranslateModule, ButtonComponent, OrderLinesComponent, PaymentButtonComponent],
  templateUrl: './payment-summary.component.html',
  styleUrl: './payment-summary.component.scss'
})
export class PaymentSummaryComponent implements OnInit {
  orderData: OrderResponse = {} as OrderResponse;
  orderPayments: OrderPaymentResponse[] = [];
  selectedPayment: OrderPaymentResponse = {} as OrderPaymentResponse;
  companyData: CompanyResponse = {} as CompanyResponse;
  // Payment status IDs
  notSentToPaymentIds = [-1, -2];
  sentToPaymentIds = [0, 1, 4, 5, 6, 7, 8];
  paidIds = [2, 3, 9, 11, 10];
  invoiceSentIds = [];

  // Categorized payments
  categorizedPayments: { [key: string]: OrderPaymentResponse[] } = {
    paid: [],
    notSentToPayment: [],
    sentToPayment: []
  };
  paymentCategoryOrder: string[] = [ 'sentToPayment', 'notSentToPayment', 'invoiceSent','paid'];
  showAllPayments: { [key: string]: boolean } = {};
  expandedPayments: { [paymentId: number]: boolean } = {};
  showPaymentButton = false;
  firstEligiblePaymentId: number | null = null;

  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    private http: HttpClient,
    public utilsService: UtilsService,
    public translate: TranslateService,
    private companyService: CompanyService
  ) {}

  ngOnInit() {
    this.orderService.order$.subscribe((order) => {
      if (order.order_id) {
        this.orderData = order;
        this.fetchCompanyByCustomer();
        this.getPayments();
      }
    });
    this.paymentCategoryOrder.forEach((category) => {
      this.showAllPayments[category] = false; // Default to showing only 3 payments
    });
  }

  toggleShowAll(categoryKey: string): void {
    this.showAllPayments[categoryKey] = !this.showAllPayments[categoryKey];
  }

  isPaid(paymentStatusId: number): boolean {
    return this.paidIds.includes(paymentStatusId);
  }

  getPayments() {
    const params = {
      order_id: this.orderData.order_id,
    };

    this.paymentService.getPayments(params).subscribe((res) => {
      // Filter out payments with payment_method_id === 10 and payment_status_id === 0
      this.orderPayments = res.filter(payment =>
        !(payment.payment_method_id === 10 && payment.payment_status_id === 0)
      );
      this.categorizePayments();
      // Now eligiblePayment only comes from the filtered list
      const eligiblePayment = this.orderPayments.find(payment =>
        ![3, 6, 11, 10, 7].includes(payment.payment_status_id)
      );
      this.firstEligiblePaymentId = eligiblePayment ? eligiblePayment.payment_id : null;
      this.showPaymentButton = eligiblePayment ? ![10, 15].includes(eligiblePayment.payment_method_id) : false;
    });
  }

  // Method to categorize payments based on payment_status_id
  categorizePayments() {
    this.categorizedPayments = {
      paid: [],
      notSentToPayment: [],
      sentToPayment: [],
      invoiceSent: [],
    };

    this.orderPayments.forEach((payment) => {
      if (this.notSentToPaymentIds.includes(payment.payment_status_id)) {
        this.categorizedPayments['notSentToPayment'].push(payment);
      } else if (this.sentToPaymentIds.includes(payment.payment_status_id)) {
        this.categorizedPayments['sentToPayment'].push(payment);
      } else if (this.paidIds.includes(payment.payment_status_id)) {
        this.categorizedPayments['paid'].push(payment);
      }
    });
  }

  getCategoryHeading(categoryKey: string): string {
    const headings: { [key: string]: string } = {
      notSentToPayment: 'Betaling ikke sendt', // 'Payment Not Sent'
      sentToPayment: '', // 'Payment Missing'
      paid: this.translate.instant('payment.summary.paid'), // 'Paid'
      invoiceSent: 'Faktura sendt', // 'Invoice Sent'
    };

    return headings[categoryKey] || '';
  }


  // Method to get the line total price
  getLineTotalPrice(line: any): number {
    return this.orderData?.show_prices_inc_vat === 1
      ? line.calculated_total_price_inc_vat
      : line.calculated_total_price_ex_vat;
  }


  generatePdf(elementId: string, translationKey: string, payment: any): void {
    const element = document.getElementById(elementId);
    if (!element) {
      console.error(`Unable to find the element for PDF content with id: ${elementId}.`);
      return;
    }

    html2canvas(element, { scale: 3, useCORS: true, logging: true }).then(canvas => {
      const imgData = canvas.toDataURL('image/jpeg', 1); // High quality
      const pdfWidth = 595.28; // A4 width in pixels at 72 DPI
      const pdfHeight = Math.max(841.89, (canvas.height * pdfWidth) / canvas.width); // Adjusted calculation

      const doc = new jsPDF({
        orientation: 'p',
        unit: 'px',
        format: [pdfWidth, pdfHeight],
      });

      try {
        doc.addImage(imgData, 'JPEG', 0, 0, pdfWidth, pdfHeight);
      } catch (error) {
        console.error('Error adding image to PDF:', error);
      }

      // Use payment data for translation
      this.translate
        .get(translationKey, { name: payment.payment_recipient?.name || '', paymentNumber: payment.payment_number })
        .subscribe(
          (res: string) => {
            const fileName = `${res}.pdf`;
            doc.save(fileName);
            // this.toastService.successToast('pdf_generated_successfully');
          },
          (error) => {
            console.error('Error generating PDF:', error);
          }
        );
    }).catch(error => {
      console.error('Error during HTML to Canvas conversion:', error);
    });
  }

  generateOrderPdf(payment: any): void {
    const elementId = 'receiptPdfContent' + payment.payment_id;
    const translationKey = 'receipt';
    this.generatePdf(elementId, translationKey, payment);
  }

  fetchCompanyByCustomer() {
    const params: CRM_COY_1 = {
      company_id: this.orderData.company_id
    };
    this.companyService.getCompanyDataByID(params).subscribe({
      next: (res: CompanyResponse) => {
        this.companyData = res;
      },
      error: (error: any) => {
        console.log(error);
      }
    });
  }

  togglePaymentExpansion(paymentId: number): void {
    this.expandedPayments[paymentId] = !this.expandedPayments[paymentId];
  }

  // Method to calculate the total amount for a payment
  getPaymentTotal(payment: OrderPaymentResponse): number {
    return payment.order_lines.reduce((total, line) => {
      return total +
        (this.orderData.show_prices_inc_vat === 1
          ? line.calculated_total_price_inc_vat
          : line.calculated_total_price_ex_vat);
    }, 0);
  }



  // Helper method that filters out payments with total == 0.
  getNonZeroPayments(payments: OrderPaymentResponse[]): OrderPaymentResponse[] {
    return payments.filter(payment => this.getPaymentTotal(payment) !== 0);
  }

}

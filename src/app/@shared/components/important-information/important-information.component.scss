.iconbox {
  margin-bottom: 15px;
  cursor: pointer;
}

/* Custom style to enlarge the checkbox */
.enlarged-checkbox .form-check-input {
  transform: scale(1.5);  /* You can adjust the value as needed */
  margin-right: 10px;     /* Optional: Add a bit of margin to ensure the checkbox doesn't touch other content */
}

/* Class to remove padding-bottom for p element */
.no-padding-bottom {
  margin-bottom: 0 !important;
}

.row {
  flex-wrap: nowrap !important;
  margin-right: 0 !important;
}

.text-box.collapsed {
  max-height: none;
  opacity: 1;
  overflow: visible;
  transition: none; /* No transition for the truncated content */
}

.text-box {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.text-box.open {
  max-height: 5000px; /* Set to a large enough value to cover the maximum possible height */
  opacity: 1;
}

.has-error .form-check-input {
  border-color: red;
  box-shadow: 0 0 0 0.2rem rgba(255, 0, 0, 0.25);
}

/* Style for the error message */
.error-message {
  color: red;
  font-size: 0.875em;
  margin-top: 0.25rem;
}

::ng-deep {
  .ce-block__content {
    max-width: 100%;
    margin: 0;
  }

  .ce-toolbar {
    display: none;
  }

  .codex-editor {
    padding: 0 !important;
  }

  .codex-editor__redactor {
    padding-bottom: 0 !important;
  }
}

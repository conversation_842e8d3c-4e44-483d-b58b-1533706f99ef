<div *ngIf="customProductInfos.length > 0" class="border rounded-4 bg-white component-container">
  <div class="contact">
    <div>
      <h6 class="text-bold mb-2">{{ "important-information.heading" | translate }}</h6>
    </div>
  </div>

  <div class="icon-container">
    <div class="iconbox" *ngFor="let info of customProductInfos">
      <div class="row header" (click)="toggleCollapse(info)">
        <div class="d-flex justify-content-between">
          <div class="title" [class.no-padding-bottom]="info.collapsed">
            {{ info.title }}
          </div>
          <i class="fa-regular" [class.fa-chevron-right]="info.collapsed" [class.fa-chevron-down]="!info.collapsed"></i>
        </div>
      </div>
      <hr class="my-1 border-1">

      <div [class.open]="!info.collapsed" [class.collapsed]="info.collapsed" class="text-box">
        <div (click)="toggleCollapse(info)" style="cursor: pointer;">
          <div *ngIf="info.collapsed; else fullDescription">
            <div>{{ getTruncatedDescription(info.description) }}</div>
          </div>
          <ng-template #fullDescription>
            <app-editor-viewer [data]="info.description"></app-editor-viewer>
          </ng-template>
        </div>
      </div>
    </div>

    <hr class="mb-3">
    <div
      *ngIf="anyRequiredItems && orderData.order_status_id === 0"
      class="form-check enlarged-checkbox ps-4"
      [ngClass]="{ 'has-error': showRequiredError && !requiredChecked }"
      (click)="$event.stopPropagation()"
    >
      <input
        type="checkbox"
        class="form-check-input"
        id="confirmationCheckbox"
        [(ngModel)]="requiredChecked"
        (ngModelChange)="onInfoCheckboxChange()"
      />
      <label class="form-check-label title" for="confirmationCheckbox">
        {{ 'important-information.checkboxlabel' | translate }}
      </label>
      <div *ngIf="showRequiredError && !requiredChecked" class="error-message">
        <span class="ms-2">{{ 'important-information.errorMessage' | translate }}</span>
      </div>
    </div>
  </div>
</div>

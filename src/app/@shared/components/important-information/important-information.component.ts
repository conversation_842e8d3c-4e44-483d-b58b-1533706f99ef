import {Component, EventEmitter, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output} from '@angular/core';
import { OrderResponse } from "../../models/order/order.module";
import { UtilsService } from "../../../@core/@core/utils/utils.service";
import { ProductInformationResponse } from "../../models/product/product.module";
import { CompanyResponse } from "../../models/company/company.module";
import { ProductService } from "../../services/product.service";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import {OrderService} from "../../services/order.service";
import {forkJoin, Subscription} from "rxjs";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {CRM_PAY_39} from "../../models/input/input.service";
import {PaymentService} from "../../services/payment.service";
import { EditorViewerComponent } from '../editor-viewer/editor-viewer.component';

export interface CustomProductInformation extends ProductInformationResponse {
  collapsed: boolean;
  checked: boolean;
}

@Component({
  selector: 'app-important-information',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule,
    FormsModule,
    EditorViewerComponent
  ],
  templateUrl: './important-information.component.html',
  styleUrls: ['./important-information.component.scss']
})
export class ImportantInformationComponent implements OnInit, OnDestroy {
  @Input() showRequiredError: boolean = false; // Add this Input property
  @Output() allRequiredChecked = new EventEmitter<boolean>();
  orderData: OrderResponse = {} as OrderResponse;
  productInfos: ProductInformationResponse[] = [];
  companyData: CompanyResponse = {} as CompanyResponse;
  productIds: number[] = [];
  repeatingPayments: OrderPaymentResponse[] = [];

  customProductInfos: CustomProductInformation[] = [];
  anyRequiredItems: boolean = false;
  requiredChecked: boolean = false;
  orderSubscription: Subscription | null = null;

  constructor(
    public utilsService: UtilsService,
    private productService: ProductService,
    private orderService: OrderService,
    private translateService: TranslateService,
    private paymentsService: PaymentService
  ) {}

  ngOnInit(): void {
    this.orderSubscription = this.orderService.order$.subscribe(order => {
      this.orderData = order;
      this.productIds = [];
        if(this.orderData.order_id) {
          this.getRepeatingPayments();
        }
    });
  }

  ngOnDestroy(): void {
    if (this.orderSubscription) {
      this.orderSubscription.unsubscribe();
    }
  }

  getProductIds() {
    if (this.orderData.work_order_schedule_templates.length > 0) {
      this.orderData.work_order_schedule_templates.forEach(template => {
        if (template.payment && template.payment.order_lines) {
          template.payment.order_lines.forEach(line => {
            if (line.product_id !== null) {
              this.productIds.push(line.product_id);
            }
          });
        }
      });
    }

    if(this.orderData.order_lines.length > 0) {
      this.productIds = this.orderData.order_lines.filter(line => line?.product_id !== null).map(line => line.product_id);
    }

    if(this.repeatingPayments.length > 0) {
      this.repeatingPayments.forEach(payment => {
        if (payment.order_lines && payment.order_lines.length > 0) {
          payment.order_lines.forEach(line => {
            if (line.product_id !== null && !this.productIds.includes(line.product_id)) {
              this.productIds.push(line.product_id);
            }
          });
        }
      });
    }

    if (this.productIds.length > 0) {
      this.fetchProductInfo();
    }
  }


  fetchProductInfo(): void {
    this.customProductInfos = [];
    this.productInfos = [];
    const productInfoObservables = this.productIds.map(productId =>
      this.productService.getProductInformation(productId, this.orderData.company_id)
    );

    forkJoin(productInfoObservables).subscribe(responses => {
      responses.forEach(res => {
        this.productInfos.push(...res.map(info => ({ ...info, collapsed: false, collapsible: true })));
      });
      this.processProductInfos();
    });
  }

  processProductInfos(): void {
    let hasRequiredItems = false;
    const uniqueProducts: { [key: number]: boolean } = {}; // Store entry_id to ensure uniqueness

    for (let info of this.productInfos) {
      const isRequired = info.require_ack_on_important_information;
      if (isRequired) hasRequiredItems = true;

      // Check if the entry_id is already in uniqueProducts
      if (!uniqueProducts[info.entry_id]) {
        uniqueProducts[info.entry_id] = true; // Mark entry_id as seen
        this.customProductInfos.push({
          ...info,
          checked: !isRequired,
          collapsed: true
        });
      }
    }


    this.anyRequiredItems = hasRequiredItems;
    if (!hasRequiredItems || this.customProductInfos.length === 0) {
      this.allRequiredChecked.emit(true);
    } else {
      this.onInfoCheckboxChange();
    }
  }

  getRepeatingPayments() {
    const params: CRM_PAY_39 = {
      order_id: this.orderData.order_id,
    };

    this.paymentsService.getRepeatingPayments(params).subscribe(res => {
      this.repeatingPayments = res;

      this.getProductIds();
    });
  }

  onInfoCheckboxChange(): void {
    this.allRequiredChecked.emit(this.requiredChecked);
    if (this.requiredChecked) {
      this.showRequiredError = false;
    }
  }

  toggleCollapse(info: CustomProductInformation): void {
    info.collapsed = !info.collapsed;
  }

  getTruncatedDescription(description: string): string {
    try {
      const parsedData = JSON.parse(description);
      let text = '';
      for (const block of parsedData.blocks) {
        if (block.type === 'paragraph' || block.type === 'header') {
          text += block.data.text + ' ';
        }
      }

      // Clean up the text by:
      // 1. Replace HTML entities
      // 2. Remove HTML tags
      // 3. Replace multiple spaces with single space
      const cleanText = text
        .replace(/&nbsp;/g, ' ')
        .replace(/<br\s*\/?>/gi, ' ')
        .replace(/<[^>]+>/g, '')
        .replace(/\s+/g, ' ')
        .trim();

      const maxLength = 150;
      const seeMoreText = this.translateService.instant('important-information.see-more');
      return cleanText.length > maxLength
        ? cleanText.slice(0, maxLength) + '... (' + seeMoreText + ')'
        : cleanText;
    } catch (e) {
      // For plain text, apply the same cleaning and truncating
      const cleanText = description
        .replace(/&nbsp;/g, ' ')
        .replace(/<br\s*\/?>/gi, ' ')
        .replace(/<[^>]+>/g, '')
        .replace(/\s+/g, ' ')
        .trim();

      const maxLength = 150;
      const seeMoreText = this.translateService.instant('important-information.see-more');
      return cleanText.length > maxLength
        ? cleanText.slice(0, maxLength) + '... (' + seeMoreText + ')'
        : cleanText;
    }
  }



}

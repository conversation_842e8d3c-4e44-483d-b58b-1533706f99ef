.top-bar {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
  text-align: left;
  padding: 10px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar .contact-info {
  margin-left: auto;
}

.sidenav {
  height: 100%;
  width: 400px;
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  background-color: #ffffff;
  overflow-x: hidden;
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform 0.5s ease;
  border-right: 2px solid #e0e3e7;
  box-sizing: border-box;
}

.sidenav.open {
  transform: translateX(0);
}

.sidenav.close {
  transform: translateX(-100%);
}

.sidenav a {
  padding: 10px;
  text-decoration: none;
  color: #000000;
  display: block;
  transition: 0.3s;
}

.sidenav-content {
  padding: 8px;
}

.sidenav a.active {
  background-color: #448C74;
  color: #ffffff;
  border-radius: 0.75rem;
}

.sidenav .closebtn {
  font-size: 36px;
  margin-left: 0;
  cursor: pointer;
}

@media screen and (max-width: 430px) {
  .sidenav {
    width: 60%;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 998;
  pointer-events: none;
}

.overlay.show {
  opacity: 1;
  pointer-events: auto;
}

@media screen and (max-height: 450px) {
  .sidenav {padding-top: 15px;}
  .sidenav a {font-size: 18px;}
}

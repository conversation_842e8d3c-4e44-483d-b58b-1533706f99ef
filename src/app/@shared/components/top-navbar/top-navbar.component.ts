import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import {CommonModule, Location} from "@angular/common";
import {Router, RouterModule} from "@angular/router";
import { CompanyResponse } from '../../models/company/company.module';
import {TranslateModule} from "@ngx-translate/core";
import {OrderResponse} from "../../models/order/order.module";
import {CRM_COY_1} from "../../models/input/input.service";

@Component({
  selector: 'app-top-navbar',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './top-navbar.component.html',
  styleUrl: './top-navbar.component.scss'
})


export class TopNavbarComponent implements OnInit, OnChanges {

  constructor(
    private router: Router,
    private location: Location) {}

  isOpen: boolean = false;

  @Input() companyData?: CompanyResponse;
  @Input() order: OrderResponse | null = null;
  @Input() showBackButton: boolean = false;
  @Input() workOrder: boolean = false;
  @Output() SeeWorkOrder = new EventEmitter<boolean>();
  imageUrl: string = '';

  ngOnInit() {
    if (this.order) {
      this.imageUrl = this.order.company_logo_url;
    } else {
      this.imageUrl = '../../../assets/images/Logo_negative_medium.png'
    }
  }

  toggleNav() {
    this.isOpen = !this.isOpen;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['companyData']) {
      this.imageUrl = this.companyData?.logo_url!;
    }
  }

  @HostListener('document:click', ['$event'])
  onClick(event: Event) {
    const target = event.target as HTMLElement;
    if (this.isOpen && !target.closest('.sidenav') && !target.closest('.top-bar')) {
      this.isOpen = false;
    }
  }

  navigateBack() {
    if(!this.workOrder){
      this.location.back();
    }else{
      this.SeeWorkOrder.emit(false);
    }
    // this.router.navigate([`orders/${this.order?.order_id}`]);
  }
}

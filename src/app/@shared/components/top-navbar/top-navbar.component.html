
<div class="top-bar p-4" style="display: flex; justify-content: space-between; align-items: center;">
  <span  *ngIf="!showBackButton" class="border border-1 px-2 py-0 rounded-2" style="font-size:20px;cursor:pointer" (click)="toggleNav()">
    <i class="fa-solid fa-bars" style="color:white"></i>
  </span>
  <span *ngIf="showBackButton" class="px-2 py-0" style="font-size:20px;cursor:pointer" (click)="navigateBack()">
    <i class="fa-solid fa-arrow-left fa-lg" style="color:white"></i>
  </span>
  <img src="{{imageUrl}}" style="max-height: 30px;" alt="">
</div>

<div id="overlay" class="overlay" [ngClass]="{'show': isOpen}" (click)="toggleNav()"></div>

<div id="mySidenav" class="sidenav" [ngClass]="{'open': isOpen, 'close': !isOpen}" style="z-index: 99999">
  <div class="d-flex justify-content-between align-items-center ps-3">
    <div class="fs-4">{{ "left.menu.title" | translate }}</div>
    <a href="javascript:void(0)" (click)="toggleNav()"> <i class="fa-solid p-2 fa-x"></i></a>
  </div>
  <hr class="mt-0">
  <div class="sidenav-content">
    <a class="pb-2 font-size-md" routerLink="/orders-overview" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: false }">
      <i class="fa-regular fa-square-list pe-2"></i>{{ "left.menu.myOrders" | translate }}
    </a>
    <a *ngIf="companyData" class="pb-2 font-size-md" [routerLink]="'/contact/' + companyData.company_id" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: false }">
      <i class="fa-regular fa-phone pe-1"></i> {{ "left.menu.contactUs" | translate }}
    </a>
    <a *ngIf="companyData" class="pb-2 font-size-md" [routerLink]="'/terms/' + companyData.company_id" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: false }">
      <i class="fa-regular fa-circle-info pe-1"></i> {{"left.menu.terms" | translate }}
    </a>
    <a *ngIf="companyData" class="pb-2 font-size-md" [routerLink]="'/privacy/' + companyData.company_id" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: false }">
      <i class="fa-regular fa-circle-info pe-1"></i> {{"left.menu.privacyPolicy" | translate }}
    </a>
  </div>
</div>

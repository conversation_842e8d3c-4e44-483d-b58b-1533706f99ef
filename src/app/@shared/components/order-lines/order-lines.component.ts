import {Component, Input, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {CommonModule, NgForOf, NgIf} from "@angular/common";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {OrderService} from "../../services/order.service";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderLineResponse, OrderResponse, PriceRuleResponse} from "../../models/order/order.module";
import {OrderLinesSummaryComponent} from "../order-lines-summary/order-lines-summary.component";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {Subject} from "rxjs";

@Component({
  selector: 'app-order-lines',
  standalone: true,
  imports: [NgIf, CommonModule, TranslateModule, OrderLinesSummaryComponent, NgForOf],
  templateUrl: './order-lines.component.html',
  styleUrl: './order-lines.component.scss'
})

export class OrderLinesComponent implements OnInit, OnDestroy{
  @Input() orderLines: OrderLineResponse[] = [];
  @Input() template: boolean = false;
  @Input() payments: OrderPaymentResponse[] = [];
  @Input() showRemainingAmount: boolean = false
  @Input() showHeader : boolean = true;
  @Input() showBorder : boolean = true;
  hasAutomaticpriceRule4: boolean = false;
  orderData?: OrderResponse;
  displayComponent: boolean = true;
  private destroy$ = new Subject<void>();

  constructor(
    public utilsService: UtilsService,
    private orderService: OrderService,
    public translate: TranslateService,
  ) { }

  ngOnInit() {
    this.orderService.order$.subscribe(order => {
      console.log("Order data", order);
      if (order.order_id) {
        this.orderData = order;
        const linesToProcess = !this.template ? order.order_lines : this.orderLines;
        this.orderLines = linesToProcess.map(line => ({
          ...line,
          showUnitPriceOnly: line.unit_id === 2 && !line.locked && line.quantity === 1
        }));
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  calculateShowUnitPrice(line: OrderLineResponse): boolean {
    this.hasAutomaticpriceRule4 = line.price_rules.some((rule: PriceRuleResponse) => rule.price_rule_type_id === 4);
    return line.unit_id === 2 && !line.locked && line.quantity === 1;
  }
}

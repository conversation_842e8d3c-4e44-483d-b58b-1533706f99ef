
@import '../../../../styles.scss';
//.order-line {
//  padding: 1rem;
//  border-bottom: 1px solid #E0E3E7;
//}
//
///* Not Sent to Payment -
//.not-sent-to-payment {
//  background-color: #f8f9fa;
//}
//
///* Sent to Payment -
//.sent-to-payment {
//  background-color: #f8d7da; /* Light red */
//}
//
///* Paid -
//.paid {
//  background-color: #d4edda;
//}
//
//.clickable {
//  cursor: pointer;
//
//  &:hover {
//    background-color: #f9f9f9;
//  }
//}
//
//



.nowrap {
  white-space: nowrap;
  display: inline-block; // Ensures it's treated as a single unit

}




.order-line {
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }

  &.interactive {
    cursor: pointer;

    &:hover {
      background-color: #f9f9f9;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }
}

/* Touch feedback on mobile devices */
@media (hover: none) {
  .order-line.interactive:active {
    background-color: #e0e0e0;
  }
}

.clickable-icon {
  font-size: 1.2em;
  color: map-get($theme-colors, "primary");
  //color: #2c2cef;
}

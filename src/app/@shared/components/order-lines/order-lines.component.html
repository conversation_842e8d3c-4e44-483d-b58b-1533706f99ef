<ng-container *ngIf="displayComponent">
  <div [ngClass]="{'border rounded-4 mb-3': showBorder}">
    <div [ngClass]="{'component-container': showBorder}">
    <h6 *ngIf="showHeader" class="mb-2 d-flex align-items-center">
      {{ "order-summary.title" | translate }}
    </h6>
  <div *ngFor="let line of orderLines; let i = index">
    <div class="order-line py-1 px-0 d-flex justify-content-between align-items-center">
      <div>
        <!-- Conditionally display the order_line_name -->
        <p class="mb-0 title" *ngIf="(orderData?.show_prices_inc_vat === 1? line.gross_total_price_inc_vat: line.gross_total_price_ex_vat) !== 0">{{ line.order_line_name }}
          <span *ngFor="let priceRule of line.price_rules">{{ priceRule.price_rule_type_id !== 4 ? '(' + priceRule.price_rule_name + ')' : '' }}</span>
        </p>
        <!-- Display comment if available -->
        <p class="mb-0 font-size-xs" *ngIf="line.comment">{{ line.comment }}</p>
        <!-- Display price and quantity when not in showUnitPriceOnly mode -->
        <p class="mb-0" *ngIf="!calculateShowUnitPrice(line) && orderData?.hide_payment_data != 1 && ((orderData?.show_prices_inc_vat === 1  ? line.gross_total_price_inc_vat : line.gross_total_price_ex_vat) !== 0 ) && !hasAutomaticpriceRule4 ">
         kr {{orderData?.show_prices_inc_vat === 1 ? line.unit_price_inc_vat : line.unit_price_ex_vat }} x {{ line.quantity }} {{ line.unit_abbreviation }}
        </p>
      </div>

      <!-- Right-aligned price & discount information -->
      <div class="d-flex flex-column align-items-end text-end">
        <!-- For lines showing a single unit price -->
        <ng-container *ngIf="calculateShowUnitPrice(line) && orderData?.hide_payment_data != 1 && (orderData?.show_prices_inc_vat ? line.gross_total_price_inc_vat : line.gross_total_price_ex_vat) !== 0">
          <p class="title mb-0"><span class="nowrap">kr {{ utilsService.formatCurrency(orderData?.show_prices_inc_vat === 1 ? line.unit_price_inc_vat : line.unit_price_ex_vat, true) }}</span> per {{ line.unit_name.toLowerCase().slice(0, -1) }}</p>
          <p class="mb-0 font-size-xs" *ngIf="line.discount_percentage">({{ line.discount_percentage }}% {{ 'order-summary.discount' | translate }})</p>
        </ng-container>

        <!-- For lines showing the total price -->
        <ng-container *ngIf="!calculateShowUnitPrice(line) && orderData?.hide_payment_data != 1 && (orderData?.show_prices_inc_vat ? line.gross_total_price_inc_vat : line.gross_total_price_ex_vat) !== 0">
          <p class="title mb-0"><span class="nowrap">kr {{utilsService.formatCurrency(orderData?.show_prices_inc_vat === 1 ? line.calculated_total_price_inc_vat : line.calculated_total_price_ex_vat, true) }}</span></p>
          <p class="mb-0 font-size-xs" *ngIf="line.discount_percentage">({{ line.discount_percentage }}% {{ 'order-summary.discount' | translate }})</p>
        </ng-container>
      </div>
    </div>
  </div>

  <hr style="margin-top: 10px;">

  <app-order-lines-summary *ngIf="orderData?.hide_payment_data != 1" [orderLines]="orderLines" [payments]="payments" [showRemainingAmount]="false" [template]="template"></app-order-lines-summary>
  </div>
</div>
</ng-container>

import {Component, Input, OnInit} from '@angular/core';
import {OrderLineResponse, OrderResponse, WorkOrderResponse} from "../../models/order/order.module";
import {OrderService} from "../../services/order.service";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {TranslateModule} from "@ngx-translate/core";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {CommonModule} from "@angular/common";

@Component({
  selector: 'app-order-lines-summary',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule
  ],
  templateUrl: './order-lines-summary.component.html',
  styleUrl: './order-lines-summary.component.scss'
})
export class OrderLinesSummaryComponent implements OnInit {
  @Input() payment?: OrderPaymentResponse;
  @Input() template?: boolean = false;
  @Input() payments?: OrderPaymentResponse[];
  @Input() workOrder?: WorkOrderResponse;
  @Input() orderData?: OrderResponse;
  @Input() orderLines?: OrderLineResponse[];
  @Input() showRemainingAmount = true;

  discountPercentage?: number;
  discountAmount?: number;
  totalAmountExVat: number = 0;
  totalAmountIncVat: number = 0;
  remainingAmountExVat: number = 0;
  remainingAmountIncVat: number = 0;
  vatData: { [key: number]: { rate: number, amount: number } } = {};
  containsHourlyRatedOrderLines = false;

  constructor(private orderService: OrderService, public utilsService: UtilsService) {}

  ngOnInit() {
    this.orderService.order$.subscribe((order) => {
      if (order.order_id) {
        this.orderData = order;

        // Decide which lines to process:
        // - If NOT from template, use the newly arrived order.order_lines
        // - If from template, use the existing this.orderLines
        const linesToProcess = !this.template ? order.order_lines : this.orderLines;

        // If there are lines to process, map over them and compute the flags
        if (linesToProcess && linesToProcess.length > 0) {
          this.orderLines = linesToProcess.map(line => {
            return {
              ...line,
              showUnitPriceOnly: line.unit_id === 2 && !line.locked && line.quantity === 1};
          });

          this.calculateAmounts();
        }
      }
    });
  }

  calculateAmounts() {
    // Reset amounts
    this.discountAmount = 0;
    this.discountPercentage = 0;
    this.totalAmountExVat = 0;
    this.totalAmountIncVat = 0;
    this.remainingAmountExVat = 0;
    this.remainingAmountIncVat = 0;
    this.vatData = {};
    this.containsHourlyRatedOrderLines = false;

    this.orderLines!.forEach((orderLine) => {
      if (orderLine.unit_id === 2) {
        this.containsHourlyRatedOrderLines = true;
      }
      // VAT Data
      if (!(orderLine.vat_rate_id in this.vatData)) {
        this.vatData[orderLine.vat_rate_id] = {
          rate: orderLine.vat_rate,
          amount: 0,
        };
      }
      this.vatData[orderLine.vat_rate_id].amount +=
        orderLine.gross_total_price_vat_amount;

      // Discount
      this.discountAmount! += orderLine.discount_amount;
      this.discountPercentage! += orderLine.discount_percentage;

      // Total Amounts
      this.totalAmountExVat += orderLine.gross_total_price_ex_vat;
      this.totalAmountIncVat += orderLine.gross_total_price_inc_vat;

      // Remaining Amounts
      if (![3, 6, 9, 10, 11].includes(orderLine.payment_status_id)) {
        this.remainingAmountExVat += orderLine.gross_total_price_ex_vat;
        this.remainingAmountIncVat += orderLine.gross_total_price_inc_vat;
      }
    });

    // If using payment, adjust remainingAmount based on payment data
    if (this.payment) {
      this.remainingAmountExVat = this.payment.total_amount_ex_vat - this.payment.paid_amount;
      this.remainingAmountIncVat = this.payment.total_amount_inc_vat - this.payment.paid_amount;
    }
  }

  // Getter to check if VAT applies
  get hasVat(): boolean {
    return Object.keys(this.vatData).length > 0;
  }

  get orderLinesUnitIdValid() {
    if(this.orderData?.order_lines) {
      const isValid = this.orderData?.order_lines.some(line => line.unit_id == 2 && line.payment_status_id != 3)
      return isValid;
    }
    return
  }

  get hasSomeShowUnitPriceOnly(): boolean {
    return this.orderLines!.some(line => line.unit_id === 2 && !line.locked && line.quantity === 1);
  }

  calculateShowUnitPrice(line: OrderLineResponse): boolean {
    return line.unit_id === 2 && !line.locked && line.quantity === 1;
  }

  get filteredVatData(): { key: number, value: { rate: number, amount: number } }[] {
    return Object.keys(this.vatData)
      .filter(key => ![3, 4, 6].includes(Number(key)))
      .map(key => ({ key: Number(key), value: this.vatData[Number(key)] }));
  }

}

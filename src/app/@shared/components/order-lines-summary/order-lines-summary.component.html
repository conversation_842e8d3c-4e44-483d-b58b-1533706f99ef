<div *ngIf="orderData && !hasSomeShowUnitPriceOnly">
  <!-- Discount -->
  <div class="d-flex justify-content-between border-bottom mb-1 pb-1" *ngIf="discountAmount && discountAmount > 0">
    <p>{{"order-summary.discount" | translate}}<span *ngIf="discountPercentage && discountPercentage > 0"> </span>:</p>
    <p>{{ utilsService.formatCurrency(discountAmount!) }}</p>
  </div>

  <!-- Total Ex VAT -->
  <div *ngIf="totalAmountExVat > 0" class="d-flex justify-content-between mt-1">
    <p>{{ "order-summary.total" | translate }} ({{ "order-summary.showExVatLabel" | translate }})</p>
    <p>{{ utilsService.formatCurrency(totalAmountExVat) }}</p>
  </div>

<!--   VAT Breakdown-->
  <div *ngIf="hasVat">
    <div *ngFor="let vatItem of filteredVatData">
      <div class="d-flex justify-content-between">
        <p>{{ 'payment.summary.vat' | translate }} ({{ vatItem.value.rate }}%)</p>
        <p>{{ utilsService.formatCurrency(vatItem.value.amount) }}</p>
      </div>
    </div>
  </div>

  <!-- Total Inc VAT -->
  <div *ngIf="totalAmountIncVat > 0" class="d-flex justify-content-between">
    <p>{{ "order-summary.total" | translate }} ({{ "order-summary.showIncVatLabel" | translate }})</p>
    <p>{{ utilsService.formatCurrency(totalAmountIncVat) }}</p>
  </div>

  <!-- Remaining Amount -->
  <div *ngIf="showRemainingAmount && (remainingAmountIncVat > 0 || remainingAmountExVat > 0) && payments?.length != 0">

    <!-- Remaining Amount Ex VAT -->
    <div *ngIf="orderData?.show_prices_inc_vat == 0" class="d-flex justify-content-between">
      <p class="">{{ "order-summary.remainingAmount" | translate }} ({{ "order-summary.showExVatLabel" | translate }})</p>
      <p class="">{{ utilsService.formatCurrency(remainingAmountExVat!) }}</p>
    </div>
    <!-- Remaining Amount Inc VAT -->
    <div *ngIf="orderData?.show_prices_inc_vat == 1" class="d-flex justify-content-between">
      <p class="">{{ "order-summary.remainingAmount" | translate }} ({{ "order-summary.showIncVatLabel" | translate }})</p>
      <p class="">{{ utilsService.formatCurrency(remainingAmountIncVat!) }}</p>
    </div>
  </div>
</div>

<div *ngIf="orderData && hasSomeShowUnitPriceOnly">
  {{ "order-summary.containsHourlyRatedOrderLines" | translate }}
</div>

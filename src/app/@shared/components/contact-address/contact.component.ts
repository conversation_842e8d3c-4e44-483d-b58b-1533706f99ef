import {Component, Input, OnInit} from '@angular/core';
import {OrderResponse, WorkOrderResponse} from "../../models/order/order.module";
import {TranslateModule} from "@ngx-translate/core";
import { UtilsService } from "../../../@core/@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import {CompanyResponse} from "../../models/company/company.module";

@Component({
  selector: 'app-contact-address',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit {
  @Input() orderData: OrderResponse = {} as OrderResponse;
  @Input() workOrder: WorkOrderResponse = {} as WorkOrderResponse;
  @Input() seller: boolean = false;
  @Input() companyData?: CompanyResponse;
 order: OrderResponse = {} as OrderResponse;
  constructor(
    public utilsService: UtilsService,) {}


  ngOnInit(): void {
    // console.log(this.companyData, "companyData contact");
  }


  displayPhoneNumber(phoneNumber: string | null): string {
    if (!phoneNumber) {
      return '';
    }
    //add +47 to the phone number if it is not there, and the format should be +47 12 34 56 78 for all phone numbers
    const countryCode = '+47';
    if (phoneNumber.startsWith(countryCode)) {
      phoneNumber = phoneNumber.slice(countryCode.length);
    }
    const pattern = /^(\d{3})(\d{2})(\d{3})$/;
    const match = phoneNumber.trim().match(pattern);
    if (match) {
      return `${countryCode} ${match[1]} ${match[2]} ${match[3]}`;
    }

    return phoneNumber;
  }
}

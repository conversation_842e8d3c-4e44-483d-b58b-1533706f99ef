<!-- Sales person -->

<div  *ngIf="orderData" class="information-section pt-0 mb-4">
  <div class="seller-card" *ngIf="!seller && workOrder.users[0] ">
    <div class="pt-3">
      <div class="row m-0 no-gutters">
        <div class="col-3 col-sm-3 p-0 align-items-center d-flex pe-2" style="max-width: 100px; min-width: 80px;">
          <div class="image-container">
            <img
              id="profile_img"
              [src]="workOrder.users[0].profile_image_url || '../assets/images/no-profile-image.jpeg'"
              class="card-img rounded"
              alt="Profile Image"
            />
          </div>
        </div>
        <div class="px-0 col align-items-center d-flex justify-content-between" *ngIf="workOrder.users[0]">
          <div class="card-body p-0 py-2">
            <p class="text black mb-0" *ngIf="workOrder.users[0]">
              <strong>{{ workOrder.users[0].first_name }} {{ workOrder.users[0].last_name }}</strong>
            </p>
            <!-- Email link -->
            <p class="text black mb-0" *ngIf="workOrder.users[0].email">
              <a [href]="'mailto:' + workOrder.users[0].email">{{ workOrder.users[0].email }}</a>
            </p>
          </div>
          <!-- Icons for phone and SMS -->
          <div class="d-flex align-items-center" style="min-width: 50px;">
            <!-- Phone icon for calling -->
            <a *ngIf="workOrder.users[0].phone" href="tel:{{workOrder.users[0].phone}}">
              <i class="fa-solid fa-phone fa-xl me-3 contact-icon" style="color: #448c74;"></i>
            </a>
            <!-- SMS icon for sending text -->
            <a *ngIf="workOrder.users[0].phone" href="sms:{{workOrder.users[0].phone}}">
              <i class="fa-solid fa-message fa-xl contact-icon" style="color: #448c74;"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="seller-card" *ngIf="seller && orderData && orderData.seller.user_id">
    <div class="border rounded-4 mb-4 component-container">
      <h6 class="mb-3">{{ "seller-title" | translate }}</h6>
      <div class="row m-0 no-gutters">
        <div class="col-3 col-sm-2 p-0 align-items-center d-flex"  style="max-width: 100px; min-width: 80px;">
          <div class="image-container">
            <img
              id="profile_img_seller"
              [src]="orderData.seller.profile_image_url || '../assets/images/no-profile-image.jpeg'"
              class="card-img rounded"
              alt="Profile Image"
            />
          </div>
        </div>
        <div class="col align-items-center d-flex" *ngIf="orderData.seller">
          <div class="card-body p-0 py-2">
            <p class="text black mb-0" >
              <strong>{{ orderData.seller.first_name }} {{ orderData.seller.last_name }}</strong>
            </p>

            <!-- Email link -->
            <p class="text black mb-0" *ngIf="orderData.seller.email">
              <a [href]="'mailto:' + orderData.seller.email">{{ orderData.seller.email }}</a>
            </p>
          </div>
          <!-- Icons for phone and SMS -->
          <div class="ms-auto">
            <!-- Phone icon for calling -->
            <a *ngIf="orderData.seller.phone" href="tel:{{orderData.seller.phone}}">
              <i class="fa-solid fa-phone fa-xl me-3 contact-icon" style="color: var(--primary-color);"></i>
            </a>
            <!-- SMS icon for sending text -->
            <a *ngIf="orderData.seller.phone" href="sms:{{orderData.seller.phone}}">
              <i class="fa-solid fa-message fa-xl contact-icon" style="color: var(--primary-color);"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="seller-card" *ngIf="seller && orderData && !orderData.seller.user_id && companyData">
  <div class="border rounded-4 mb-4 component-container">
    <h6 class="mb-3">{{ "company-title" | translate }}</h6>
    <div class="row m-0 no-gutters">
      <div class="col align-items-center d-flex">
        <div class="card-body p-0 py-2">
          <p class="text black mb-0">
            <strong>{{ companyData.company_name }}</strong>
          </p>
          <!-- Email link -->
          <p class="text black mb-0" *ngIf="companyData.email">
            <a [href]="'mailto:' + companyData.email">{{ companyData.email }}</a>
          </p>
        </div>
        <!-- Icons for phone and SMS -->
        <div class="ms-auto">
          <!-- Phone icon for calling -->
          <a *ngIf="companyData.phone" href="tel:{{companyData.phone}}">
            <i class="fa-solid fa-phone fa-xl me-3 contact-icon" style="color: var(--primary-color);"></i>
          </a>
          <!-- SMS icon for sending text -->
          <a *ngIf="companyData.phone" href="sms:{{companyData.phone}}">
            <i class="fa-solid fa-message fa-xl contact-icon" style="color: var(--primary-color);"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
</div>

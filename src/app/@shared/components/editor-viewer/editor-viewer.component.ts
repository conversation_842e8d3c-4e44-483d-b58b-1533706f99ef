import { Component, Input, ElementRef, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import EditorJS from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';

@Component({
  selector: 'app-editor-viewer',
  standalone: true,
  template: '<div #editorContainer [id]="editorId"></div>',
  styles: [`
    :host {
      display: block;
    }

    ::ng-deep {
      .ce-block__content {
        max-width: 100%;
        margin: 0;
      }

      .ce-toolbar {
        display: none;
      }

      .codex-editor {
        padding: 0 !important;
      }

      .codex-editor__redactor {
        padding-bottom: 0 !important;
      }
    }
  `]
})
export class EditorViewerComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() data: string = '';
  uniqueId: string = Math.random().toString(36).substring(7);
  private editor: EditorJS | null = null;

  get editorId(): string {
    return `editor-${this.uniqueId}`;
  }

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {}

  ngAfterViewInit() {
    this.initializeEditor();
  }

  private initializeEditor() {
    try {
      const parsedData = this.parseContent(this.data);

      this.editor = new EditorJS({
        holder: 'editor-' + this.uniqueId,
        readOnly: true,
        tools: {
          header: Header,
          list: List
        },
        data: parsedData
      });
    } catch (e) {
      console.error('Error initializing Editor.js:', e);
    }
  }

  private parseContent(content: string) {
    try {
      return JSON.parse(content);
    } catch {
      // Fallback for plain text
      return {
        blocks: [
          {
            type: 'paragraph',
            data: {
              text: content
            }
          }
        ]
      };
    }
  }

  ngOnDestroy() {
    if (this.editor) {
      this.editor.destroy();
    }
  }
}

import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, ReactiveFormsModule, FormsModule, AbstractControl, ValidatorFn } from '@angular/forms';
import { PhoneNumberUtil, PhoneNumberFormat } from 'google-libphonenumber';
import { CommonModule } from '@angular/common';
import { CountryService } from '../../services/country.service';
import {TranslateModule, TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'app-phone-input',
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
  ],
  providers: [CountryService]
})
export class PhoneInputComponent implements OnInit, OnChanges {
  @Input() placeholder: string = 'placeholders.phone';
  @Input() defaultCountry: string = 'NO';
  @Input() initialPhoneNumber: string = '';
  @Input() isRequired: boolean = false; // New input to control if the field is required
  @Output() phoneNumberChange = new EventEmitter<string | null>();
  @Output() phoneNumberValid = new EventEmitter<boolean>(); // New output to emit the validity status

  phoneForm: FormGroup;
  countries: any[];
  selectedCountry: any;
  translatedPlaceholder: string = '';

  phoneUtil: PhoneNumberUtil = PhoneNumberUtil.getInstance();

  constructor(
    private fb: FormBuilder,
    private countryService: CountryService,
    private translate: TranslateService
  ) {
    this.countries = this.countryService.getCountries();
    this.selectedCountry = this.countries.find(country => country.iso2 === this.defaultCountry);

    this.phoneForm = this.fb.group({
      phone: ['', this.buildPhoneValidators()]
    });
  }

  ngOnInit(): void {
    if (this.initialPhoneNumber) {
      this.setInitialPhoneNumber(this.initialPhoneNumber);
    } else {
      this.emitPhoneNumber(); // Validate initial value
    }

    // Emit initial validity status
    this.phoneForm.statusChanges.subscribe(status => {
      this.phoneNumberValid.emit(this.phoneForm.valid);
    });

    // Translate placeholder
    this.translate.get(this.placeholder).subscribe((res: string) => {
      this.translatedPlaceholder = res;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isRequired']) {
      const phoneControl = this.phoneForm.get('phone');
      phoneControl?.setValidators(this.buildPhoneValidators());
      phoneControl?.updateValueAndValidity();
    }
  }

  buildPhoneValidators(): ValidatorFn[] {
    const validators: ValidatorFn[] = [this.validatePhoneNumber.bind(this)];
    if (this.isRequired) {
      validators.unshift(Validators.required);
    }
    return validators;
  }

  validatePhoneNumber(control: AbstractControl): { [key: string]: any } | null {
    const inputNumber = control.value;
    if (!inputNumber) {
      return null;
    }
    try {
      const fullNumber = `+${this.selectedCountry.dialCode}${inputNumber.replace(/\s+/g, '')}`;
      const parsedNumber = this.phoneUtil.parse(fullNumber, this.selectedCountry.iso2.toUpperCase());
      if (!this.phoneUtil.isValidNumber(parsedNumber)) {
        return { invalidPhoneNumber: true };
      }
    } catch (error) {
      return { invalidPhoneNumber: true };
    }
    return null;
  }

  formatPhoneNumberForDisplay(phoneNumber: string): string {
    try {
      const fullNumber = `+${this.selectedCountry.dialCode}${phoneNumber.replace(/\s+/g, '')}`;
      const parsedNumber = this.phoneUtil.parse(fullNumber, this.selectedCountry.iso2.toUpperCase());
      return this.phoneUtil.format(parsedNumber, PhoneNumberFormat.INTERNATIONAL).replace(`+${this.selectedCountry.dialCode} `, '');
    } catch (error) {
      return phoneNumber;
    }
  }

  emitPhoneNumber(): void {
    const rawValue = this.phoneForm.get('phone')?.value;
    if (rawValue) {
      const fullNumber = `+${this.selectedCountry.dialCode}${rawValue.replace(/\s+/g, '')}`;
      this.phoneNumberChange.emit(fullNumber);
    } else {
      this.phoneNumberChange.emit(null);
    }
    this.phoneNumberValid.emit(this.phoneForm.valid); // Emit validity status
  }

  onPhoneNumberChange(): void {
    const rawValue = this.phoneForm.get('phone')?.value;
    if (this.phoneForm.valid) {
      if (rawValue) {
        const formattedNumber = this.formatPhoneNumberForDisplay(rawValue);
        this.phoneForm.get('phone')?.setValue(formattedNumber, { emitEvent: false });
      }
      this.emitPhoneNumber();
    } else {
      this.emitPhoneNumber();
    }
    this.phoneNumberValid.emit(this.phoneForm.valid); // Emit validity status
  }

  onCountryChange(event: any): void {
    this.selectedCountry = this.countries.find(country => country.iso2 === event.target.value);
    this.phoneForm.patchValue({ phone: '' }); // Clear the phone input when country changes
    this.emitPhoneNumber();
    this.phoneNumberValid.emit(this.phoneForm.valid); // Emit validity status
  }

  setInitialPhoneNumber(phoneNumber: string): void {
    try {
      const parsedNumber = this.phoneUtil.parse(phoneNumber);
      const countryCode = parsedNumber.getCountryCode();
      this.selectedCountry = this.countries.find(country => country.dialCode == countryCode);
      const numberWithoutCountryCode = phoneNumber.replace(`+${countryCode}`, '').trim();
      const formattedNumber = this.formatPhoneNumberForDisplay(numberWithoutCountryCode);
      this.phoneForm.patchValue({ phone: formattedNumber });
      this.emitPhoneNumber();
    } catch (error) {
      console.error('Invalid initial phone number format', error);
    }
    this.phoneNumberValid.emit(this.phoneForm.valid); // Emit validity status
  }
}

<form [formGroup]="phoneForm">
  <div class="input-group mb-1">
    <div class="input-group-prepend me-2">
      <button class="btn btn-outline-secondary dropdown-toggle" style="border-color: #dee2e6; color: black; background-color: white; padding:0.95rem 0.85rem;" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <img alt="{{ selectedCountry.name }}" src="http://purecatamphetamine.github.io/country-flag-icons/3x2/{{ selectedCountry.iso2 }}.svg" class="flag-icon"/>
        +{{ selectedCountry.dialCode }}
      </button>
      <div class="dropdown-menu">
        <button *ngFor="let country of countries" class="dropdown-item" style="background: white;" (click)="onCountryChange({ target: { value: country.iso2 } })">
          <img alt="{{ country.name }}" src="http://purecatamphetamine.github.io/country-flag-icons/3x2/{{ country.iso2 }}.svg" class="flag-icon"/>
          {{ country.name }} (+{{ country.dialCode }})
        </button>
      </div>
    </div>
    <div class="form-floating">
      <input
        type="tel"
        class="form-control rounded-3"
        style="border-width: 2px;"
        id="floatingPhoneInput"
        [placeholder]="translatedPlaceholder"
        formControlName="phone"
        (blur)="phoneForm.get('phone')?.markAsTouched()"
        (input)="onPhoneNumberChange()"
      />
      <label for="floatingPhoneInput">{{ translatedPlaceholder }}</label>
    </div>
  </div>
  <div *ngIf="phoneForm.get('phone')?.invalid && phoneForm.get('phone')?.touched" class="text-danger">
    <div *ngIf="phoneForm.get('phone')?.errors?.['required']">
      {{ "phoneInput.required" | translate }}
    </div>
    <div *ngIf="phoneForm.get('phone')?.errors?.['invalidPhoneNumber']">
      {{ "phoneInput.invalidPhone" | translate }}
    </div>
  </div>
</form>

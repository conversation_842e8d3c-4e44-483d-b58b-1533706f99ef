import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonComponent} from "../button/button.component";

@Component({
  selector: 'app-verification-modal',
  standalone: true,
  // If you need extra imports (e.g., CommonModule) for *ngIf, etc., add them here
  templateUrl: './verification-modal.component.html',
  imports: [
    TranslateModule,
    ButtonComponent
  ],
  styleUrls: ['./verification-modal.component.scss']
})
export class VerificationModalComponent {
  isLoaded = false;
constructor(public activeModal: NgbActiveModal) {
}
  ngAfterViewInit() {
    this.isLoaded = true;
  }

  @Input() titleKey: string = 'verification.title';
  @Output() confirm = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();


  onConfirm(): void {
    this.confirm.emit();
  }

  onCancel(): void {
    this.cancel.emit();
  }
}

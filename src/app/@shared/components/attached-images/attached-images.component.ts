import {ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {OrderAttachmentResponse, OrderResponse} from "../../models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderService} from "../../services/order.service";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {ActivatedRoute} from "@angular/router";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {ScreenSizeService} from "../../services/screen-size.service";
import {CommonModule} from "@angular/common";
import {HammerModule} from "@angular/platform-browser";
import {NgbModal, NgbModalModule} from "@ng-bootstrap/ng-bootstrap";
import {HttpClient} from "@angular/common/http";
import {FormControl, ReactiveFormsModule} from "@angular/forms";
import {VerificationModalComponent} from "../verification-modal/verification-modal.component";
import {ButtonComponent} from "../button/button.component";
import {ToastService} from "../../../@core/@core/services/toast.service";

@Component({
  selector: 'app-attached-images',
  standalone: true,
  imports: [TranslateModule, CommonModule, HammerModule, NgbModalModule, ReactiveFormsModule, ButtonComponent],
  templateUrl: './attached-images.component.html',
  styleUrl: './attached-images.component.scss'
})
export class AttachedImagesComponent implements OnInit {
  order: OrderResponse = {} as OrderResponse;
  isMobile!: boolean;
  currentImages: string[] = [];
  currentIndex: number = 0;
  orderId: number = 0;
  fileControl = new FormControl();
  @ViewChild('imageModalTemplate') imageModalTemplate!: TemplateRef<any>;
  isUploading: boolean = false;

  constructor(
    private orderService: OrderService,
    public storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    public utilsService: UtilsService,
    private screenSizeService: ScreenSizeService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef,
    private http: HttpClient,
    private toastService: ToastService,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.orderService.order$.subscribe(order => {
      this.order = order;
    });

    this.screenSizeService.isMobile$.subscribe(isMobile => {
      this.isMobile = isMobile;
    });
  }

  isImageFile(attachment: OrderAttachmentResponse): boolean {
    const extension = attachment.extension.toLowerCase();
    return ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'webp'].includes(extension);
  }

  getFileIconClass(attachment: OrderAttachmentResponse): string {
    const extension = attachment.extension.toLowerCase();

    const iconMap: { [key: string]: string } = {
      'pdf': 'fa-regular fa-file-pdf',
      'doc': 'fa-regular fa-file-word',
      'docx': 'fa-regular fa-file-word',
      'xls': 'fa-regular fa-file-excel',
      'xlsx': 'fa-regular fa-file-excel',
      'ppt': 'fa-regular fa-file-powerpoint',
      'pptx': 'fa-regular fa-file-powerpoint',
      'zip': 'fa-regular fa-file-archive',
      'rar': 'fa-regular fa-file-archive',
      'txt': 'fa-regular fa-file-alt',
      'mp3': 'fa-regular fa-file-audio',
      'wav': 'fa-regular fa-file-audio',
      'mp4': 'fa-regular fa-file-video',
      'avi': 'fa-regular fa-file-video',
      'mov': 'fa-regular fa-file-video',
    };

    return iconMap[extension] || 'fa-regular fa-file';
  }

  openImageModal(attachment: OrderAttachmentResponse) {
    const imageAttachments = this.order.attachments.filter(att => this.isImageFile(att));

    this.currentImages = imageAttachments.map(att => att.file_url);
    this.currentIndex = imageAttachments.findIndex(att => att.attachment_id === attachment.attachment_id);

    if (this.currentIndex !== -1) {
      this.modalService.open(this.imageModalTemplate);
    }
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  prevImage(event: Event) {
    event.preventDefault();
    if (this.currentIndex > 0) {
      this.currentIndex--;
    } else {
      this.currentIndex = this.currentImages.length - 1;
    }
    this.cdr.detectChanges();
  }

  nextImage(event: Event) {
    event.preventDefault();
    if (this.currentIndex < this.currentImages.length - 1) {
      this.currentIndex++;
    } else {
      this.currentIndex = 0;
    }
    this.cdr.detectChanges();
  }

  hasOrderData(order: OrderResponse | null): boolean {
    return order !== null && Object.keys(order).length > 0;
  }

  downloadAttachment(attachment: OrderAttachmentResponse) {
    this.http.get(attachment.file_url, { responseType: 'blob' }).subscribe(blob => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.file_name;
      link.click();
      window.URL.revokeObjectURL(url);
    }, error => {
      console.error('Download failed', error);
    });
  }

  async handleFileSelect(event: Event) {
    const fileInput = event.target as HTMLInputElement;
    const file = fileInput.files?.[0];

    if (file && this.order.order_id) {
      this.isUploading = true;
      try {
        if (file.size > 10 * 1024 * 1024) {
          this.toastService.showError(this.translateService.instant('attached.images.error.size'));
          return;
        }

        const response = await this.orderService.addOrderAttachment(file, this.order.order_id).toPromise();

        if (response && response.attachments) {
          this.order.attachments = response.attachments;
          this.orderService.refreshOrder(this.order);
          this.cdr.detectChanges();
        }

      } catch (error) {
        console.error('File upload failed:', error);
        this.toastService.showError(this.translateService.instant('attached.images.error.upload'));
      } finally {
        this.isUploading = false;
        fileInput.value = ''; // Reset the input
        this.cdr.detectChanges();
      }
    }
  }


  openDeleteConfirmation(attachment: OrderAttachmentResponse) {
    const modalRef = this.modalService.open(VerificationModalComponent, {
      size: 'md',
      windowClass: 'bottom-modal'
    });

    modalRef.result
      .then((result) => {if (result === 'confirmed') {this.deleteAttachment(attachment);}})
      .catch((reason) => {
        console.log('Modal closed:', reason);
      });
  }

  deleteAttachment(attachment: OrderAttachmentResponse) {
    this.orderService.deleteOrderAttachment(attachment.attachment_id, this.order.order_id).subscribe(
      () => {
        console.log('Attachment deleted successfully');
        this.order.attachments = this.order.attachments.filter(
          (att) => att.attachment_id !== attachment.attachment_id
        );
      },
      (error) => {
        console.error('Attachment deletion failed:', error);
      }
    );
  }


}

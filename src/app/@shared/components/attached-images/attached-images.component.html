<div class="component-container border rounded-4 mb-4 bg-white">
  <h6>{{ "attached.images.header" | translate }}</h6>


  <div class="p-0">
    <div class="col-sm p-0 border-left" id="allocated-resources">
      <div style="max-height: 340px; overflow-y: auto" id="attachmentsContainer">
        <div class="h-100">
          <div style="border-radius: 0; width: 100%; min-height: 77%">
            <div id="data-body">
              <div *ngIf="!order.attachments || order.attachments.length === 0" class="text-center">
                <p class="text-muted mb-0">{{ "attached.images.noFiles" | translate }}</p>
              </div>
              <div *ngFor="let attachment of order.attachments; let i = index;" class="mb-2" [ngClass]="(i == order.attachments.length - 1) ? '' : 'border-bottom'">
                <div class="d-flex align-items-center">

                  <!-- Image/Icon Container -->
                  <div class="border rounded-2 me-2 d-flex justify-content-center align-items-center p-2" style="height: 60px; width:60px;">
                    <ng-container *ngIf="isImageFile(attachment); else fileIcon">
                      <img class="rounded"
                           style="max-width: 50px; max-height: 50px; cursor: pointer"
                           [src]="attachment.file_url"
                           (click)="openImageModal(attachment)">
                    </ng-container>
                    <ng-template #fileIcon>
                      <i [class]="getFileIconClass(attachment) + ' fa-2x'" style="cursor: pointer;"
                         (click)="downloadAttachment(attachment)"></i>
                    </ng-template>
                  </div>

                  <!-- Filename Container -->
                  <div class="flex-grow-1" style="min-width:0;">
                    <p class="mb-0" style="white-space: normal; word-break: break-word;">
                      {{ attachment.file_name }}
                    </p>
                  </div>
                  <div class="ms-2">
                    <div class="d-flex button-container">
                      <app-button
                        [buttonClass]="'btn btn-primary btn-sm'"
                        [width]="'110px'"
                        (buttonClick)="downloadAttachment(attachment)">
                        <i class="fa fa-download me-1"></i>{{ "attached.images.download" | translate }}
                      </app-button>

                      <app-button
                        *ngIf="attachment.updated_by == this.storageService.getUser().user_id,"
                        [buttonClass]="'btn btn-light btn-sm'"
                        [width]="'110px'"
                        (buttonClick)="openDeleteConfirmation(attachment)">
                        {{ "attached.images.delete" | translate }}
                      </app-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <!-- Modal Template -->
          <ng-template #imageModalTemplate let-modal>
            <div class="modal-body d-flex justify-content-center position-relative">
              <i class="fa-regular fa-xmark fa-2xl position-absolute" style="top: 30px; right: 10px; cursor: pointer; z-index: 10;" (click)="closeModal()"></i>
              <div *ngIf="currentImages.length > 1" id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
<!--                <ol class="carousel-indicators">-->
<!--                  <li *ngFor="let img of currentImages; let i = index"-->
<!--                      [ngClass]="{'active': i === currentIndex}"-->
<!--                      data-target="#carouselExampleIndicators"-->
<!--                      [attr.data-slide-to]="i"></li>-->
<!--                </ol>-->
                <div class="carousel-inner">
                  <div *ngFor="let img of currentImages; let i = index" class="carousel-item" [ngClass]="{'active': i === currentIndex}">
                    <img class="d-block w-100" [src]="img" alt="Slide {{ i + 1 }}">
                  </div>
                </div>
                <a class="carousel-control-prev custom-control" href="#" role="button" (click)="prevImage($event)">
                  <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                  <span class="sr-only">Previous</span>
                </a>
                <a class="carousel-control-next custom-control" href="#" role="button" (click)="nextImage($event)">
                  <span class="carousel-control-next-icon" aria-hidden="true"></span>
                  <span class="sr-only">Next</span>
                </a>
              </div>
              <div *ngIf="currentImages.length === 1">
                <img class="d-block w-100" [src]="currentImages[0]" alt="Image">
              </div>
            </div>
          </ng-template>

        </div>

      </div>
    </div>
        <!-- Upload Section -->
    <div class="d-sm-flex justify-content-end mt-2">
      <div class="upload-buttons d-flex gap-2 w-100">
        <app-button
          (buttonClick)="fileInput.click()"
          [buttonClass]="'btn btn-primary py-1 w-100'"
          [disabled]="isUploading"
        >
          <i class="fas fa-upload me-2"></i>
          {{ isUploading ? ("attached.images.uploading" | translate) : ("attached.images.upload" | translate) }}
        </app-button>

        <!-- Single hidden input that allows both camera and gallery -->
        <input
          #fileInput
          type="file"
          (change)="handleFileSelect($event)"
          accept="image/*"
          class="d-none">
      </div>
    </div>
  </div>
</div>


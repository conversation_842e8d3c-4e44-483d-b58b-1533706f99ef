.custom-control {
  background-color: rgba(0, 0, 0, 0.5); // Semi-transparent background
  border-radius: 50%; // Rounded shape
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%); // Center the control vertically
  z-index: 1; // Ensure controls are above the carousel images
}


.carousel-control-prev {
  left: 15px;
}

.carousel-control-next {
  right: 15px;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  filter: invert(1); // Make the icon white
  width: 20px;
  height: 20px;
}

.carousel-control-prev:hover .carousel-control-prev-icon,
.carousel-control-next:hover .carousel-control-next-icon {
  filter: invert(0.8); // Slightly lighter arrow on hover
}

.carousel-control-prev,
.carousel-control-next {
  opacity: 1;
}

.button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-end;
  align-items: center;
  max-width: fit-content;
}

//
//  app-button {
//    flex: 1 1 auto; /* This allows the buttons to shrink and wrap if necessary */
//    min-width: 95px; /* Ensures the buttons maintain their minimum size */
//  }
//}


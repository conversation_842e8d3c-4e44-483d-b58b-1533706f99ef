import { CommonModule } from '@angular/common';
import {Component, OnInit, ElementRef, Renderer2, On<PERSON><PERSON>roy, Input} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-language-switch',
  templateUrl: './language-switch.component.html',
  styleUrls: ['./language-switch.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
  ],
})
export class LanguageSwitchComponent implements OnInit, OnDestroy {
  @Input() textColor: string = 'text-white';
  currentLanguage: string = '';
  selectedLanguage: string = '';
  showDropdown: boolean = false;

  supportedLanguages: { code: string; name: string; flagUrl: string }[] = [
    { code: 'en', name: 'English', flagUrl: '/assets/images/flag/english.png' },
    { code: 'no', name: 'Norsk', flagUrl: '/assets/images/flag/norway.png' },
  ];

  private clickListener: (() => void) | undefined;

  constructor(private translate: TranslateService, private eRef: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    // Get the language from local storage or default to 'no'
    this.currentLanguage = localStorage.getItem('language') || 'no';
    this.selectedLanguage = this.currentLanguage;

    // Initialize the translation service with the selected language
    this.translate.use(this.currentLanguage);

    this.clickListener = this.renderer.listen('document', 'click', (event) => {
      if (!this.eRef.nativeElement.contains(event.target)) {
        this.showDropdown = false;
      }
    });
  }

  ngOnDestroy() {
    if (this.clickListener) {
      this.clickListener();
    }
  }

  switchLanguage(languageCode: string) {
    console.log(`Switching language to: ${languageCode}`);
    
    this.translate.use(languageCode).subscribe(() => {
      this.currentLanguage = languageCode;
      this.selectedLanguage = this.currentLanguage;
  
      console.log(`Language set in TranslateService: ${this.currentLanguage}`);
      
      // Save the selected language in local storage
      localStorage.setItem('language', languageCode);
      console.log(`Language saved to localStorage: ${languageCode}`);
  
      // Update the <html lang> attribute
      const htmlTag = document.querySelector('html');
      if (htmlTag) {
        htmlTag.setAttribute('lang', languageCode);
        console.log(`Updated <html> lang attribute to: ${languageCode}`);
      } else {
        console.error('Failed to find <html> element');
      }
  
      // Update the <meta http-equiv="Content-Language"> tag
      let metaTag = document.querySelector('meta[http-equiv="Content-Language"]');
      if (!metaTag) {
        // If the meta tag doesn't exist, create it
        metaTag = document.createElement('meta');
        metaTag.setAttribute('http-equiv', 'Content-Language');
        document.head.appendChild(metaTag);
        console.log('<meta http-equiv="Content-Language"> tag created');
      }
      metaTag.setAttribute('content', languageCode);
      console.log(`Updated <meta http-equiv="Content-Language"> content to: ${languageCode}`);
  
      // Close the dropdown (if applicable)
      this.showDropdown = false;
  
      // Optional: Reload the page if necessary to apply language changes throughout the app
      console.log('Reloading the page...');
      window.location.reload();
    });
  }

  toggleDropdown() {
    this.showDropdown = !this.showDropdown;
  }

  getCurrentLanguageName(): string {
    const currentLanguage = this.supportedLanguages.find(lang => lang.code === this.currentLanguage);
    return currentLanguage ? currentLanguage.name : '';
  }

  getCurrentLanguageFlagUrl(): string {
    const currentLanguage = this.supportedLanguages.find(lang => lang.code === this.currentLanguage);
    return currentLanguage ? currentLanguage.flagUrl : '';
  }
}

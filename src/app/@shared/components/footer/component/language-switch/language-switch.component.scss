.language-switch {
  }

  .language-dropdown {
    appearance: none;
    background-color: transparent;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    padding: 5px;
    position: relative;
    z-index: 1;
    text-align: center;
  }

  .language-dropdown option {
    background-color: #fff;
    color: #333;
  }

  .language-dropdown::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #999;
    pointer-events: none;
  }

  .language-dropdown:focus {
    outline: none;
  }

  .current-language {
    font-size: 1rem;
    color: #ffffff;
  }

  .flag-icon {
    margin-right: 5px;
    border-radius: 3px;
  }

  .current-language {
    cursor: pointer;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: block;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    color: #333;
    text-align: inherit;
    white-space: nowrap;
    background: 0 0;
    border: 0;
    cursor: pointer;
  }

  .language-dropdown::after{
    display: none;
  }
  .language-title {
    padding: 10px;
    font-size: 16px;
    font-weight: bold;
  }

<div class="language-switch">
  <div class="current-language" (click)="toggleDropdown()">
    <img style="height: 30px;" [src]="getCurrentLanguageFlagUrl()" alt="{{ getCurrentLanguageName() }}" class="flag-icon">
    <span [style.color]="textColor">{{ getCurrentLanguageName() }}</span>
    <i class="fa fa-chevron-down ms-2" [style.color]="textColor" aria-hidden="true"></i>
  </div>

  <div *ngIf="showDropdown" class="language-dropdown">
    <ng-container *ngFor="let language of supportedLanguages">
      <div class="language-option" *ngIf="language.code !== currentLanguage" (click)="switchLanguage(language.code)">
        <img [src]="language.flagUrl" style="width: 30px;" alt="flag" class="flag-icon">
        <span [style.color]="textColor">{{ language.name }}</span>
      </div>
    </ng-container>
  </div>
</div>

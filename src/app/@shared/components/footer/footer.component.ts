import { CommonModule } from '@angular/common';
import {Component, Input, OnInit} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UtilsService } from '../../../@core/@core/utils/utils.service';
import { LanguageSwitchComponent } from './component/language-switch/language-switch.component';
import {CompanyResponse} from "../../models/company/company.module";

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    LanguageSwitchComponent
],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss'
})
export class FooterComponent implements OnInit{
  @Input() paymentVisible = true;
  @Input() companyData?: CompanyResponse;
  constructor(
    public utilsService: UtilsService,
    private translate: TranslateService) {}


ngOnInit() {
  if (this.companyData?.company_color) {
    document.documentElement.style.setProperty('--primary-color', this.companyData.company_color);
  }}
}

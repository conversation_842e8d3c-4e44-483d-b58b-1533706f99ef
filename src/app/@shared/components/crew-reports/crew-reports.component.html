<div class="component-container border rounded-4 mb-4 bg-white">
  <div class="header">
  <div class="d-flex justify-content-between align-items-center">
    <h6 class="mb-0">{{ "incidents.images.header" | translate }}</h6>

    <app-button
    (buttonClick)="openAddReport()"
    [buttonClass]="'btn btn-primary btn-sm w-100'"
  >
    <i class="fas fa-plus me-1" style="color: white;"></i>
    {{ "incidents.add.report" | translate }}
    </app-button>
  </div>
  <hr>
</div>

  <div class="p-0">
    <div class="col-sm p-0 border-left" id="allocated-resources">
      <div style="max-height: 340px; overflow-y: auto" id="attachmentsContainer">
        <div class="h-100">
          <div style="border-radius: 0; width: 100%; min-height: 77%">
            <div id="data-body">
              <!-- No reports message -->
              <div *ngIf="groupedIncidents.length === 0" class="text-center p-4 text-muted">
                {{ "incidents.no.reports" | translate }}
              </div>
              
              <!-- Loop over grouped incidents -->
              <div *ngFor="let group of groupedIncidents" class="mb-4">
                <!-- Work Order Header -->
                <div class="d-flex align-items-center px-3 py-2 bg-light rounded-top border-top border-start border-end">
                  <h6 class="mb-0">{{ group.workOrderTitle }}</h6>
                  <span class="ms-2 text-muted">{{ group.workOrderExecutionAt | date:'dd MMM yyyy HH:mm' }}</span>
                </div>

                <!-- Incidents in this group -->
                <div *ngFor="let incident of group.incidents; let i = index" 
                     class="d-flex align-items-top p-3 border"
                     [class.border-top-0]="i !== 0">
                  <!-- Left side: images or placeholder -->
                  <div class="d-flex flex-wrap position-relative" style="min-width: 70px;">
                    <ng-container *ngIf="incident.images && incident.images.length > 0; else noImage">
                      <div class="rounded-2 d-flex justify-content-center align-items-center position-relative">
                        <img
                          class="rounded image-thumbnail"
                          [src]="incident.images[0]"
                          (click)="openImageModal(incident.images, 0)"
                          alt="Incident Image"
                        />
                        <!-- Multiple images indicator -->
                        <div *ngIf="incident.images.length > 1" 
                             class="multiple-images-badge"
                             title="{{ incident.images.length }} images">
                          <i class="fas fa-images" style="color: white;"></i>
                          <span>{{ incident.images.length }}</span>
                        </div>
                      </div>
                    </ng-container>
                    <ng-template #noImage>
                      <div class="rounded-2 d-flex justify-content-center align-items-center">
                        <img class="rounded" style="max-width: 70px; max-height: 70px;" src="assets/images/no_image_icon.png">
                      </div>
                    </ng-template>
                  </div>

                  <!-- Right side: description, reporter and time -->
                  <div class="d-flex flex-column ms-3 flex-grow-1">
                    <p class="mb-2">{{ incident.description }}</p>
                    <div class="d-flex align-items-center text-muted">
                      <small>
                        {{ incident.reported_by_first_name }} {{ incident.reported_by_last_name }}
                        • {{ incident.created_at | timeAgo }}
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Modal Template -->
            <ng-template #imageModalTemplate let-modal>
              <div class="modal-body d-flex justify-content-center position-relative">
                <i class="fa-regular fa-xmark fa-2xl position-absolute" style="top: 30px; right: 10px; cursor: pointer; z-index: 10;" (click)="closeModal()"></i>
                <div *ngIf="currentImages.length > 1" id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                  <div class="carousel-inner">
                    <div *ngFor="let img of currentImages; let i = index" class="carousel-item" [ngClass]="{'active': i === currentIndex}">
                      <img class="d-block w-100" [src]="img" alt="Slide {{ i + 1 }}">
                    </div>
                  </div>
                  <a class="carousel-control-prev custom-control" href="#" role="button" (click)="prevImage($event)">
                    <span class="carousel-control-prev-icon" aria-hidden="true" style="background-color: black; border-radius: 50%; opacity: 50%;"></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a class="carousel-control-next custom-control" href="#" role="button" (click)="nextImage($event)">
                    <span class="carousel-control-next-icon" aria-hidden="true" style="background-color: black; border-radius: 50%; opacity: 50%"></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
                <div *ngIf="currentImages.length === 1">
                  <img class="d-block w-100" [src]="currentImages[0]" alt="Image">
                </div>
              </div>
            </ng-template>

            <!-- Add Report Modal Template -->
            <ng-template #addReportTemplate let-modal>
              <div class="modal-header">
                <h5 class="modal-title">{{ "incidents.add.report" | translate }}</h5>
                <button type="button" class="btn-close" (click)="closeModal()"></button>
              </div>
              <div class="modal-body">
                <app-add-report 
                  [order]="order"
                  (reportAdded)="onReportAdded()">
                </app-add-report>
              </div>
            </ng-template>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

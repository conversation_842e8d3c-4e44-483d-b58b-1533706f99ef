.image-thumbnail,
.image-placeholder {
  width: 100px;
  height: 100px;
  object-fit: cover; /* Ensures the image covers the area without distortion */
  cursor: pointer;
  border: solid 1px #e0e3e7;
}

.image-thumbnail {
  width: 70px;
  height: 70px;
  object-fit: cover;
  cursor: pointer;
}

.multiple-images-badge {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  
  i {
    font-size: 0.8rem;
  }

  span {
    line-height: 1;
  }
}

// Make the image container show a subtle hover effect
.rounded-2 {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.02);
    
    .multiple-images-badge {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

import {ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {Subject, takeUntil} from "rxjs";
import {ExtendedIncidentResponse, IncidentResponse, OrderResponse, WorkOrderResponse} from "../../models/order/order.module";
import {OrderService} from "../../services/order.service";
import {NgClass, NgForOf, NgIf} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import { AddReportComponent } from './add-report/add-report/add-report.component';
import { ScreenSizeService } from '../../services/screen-size.service';
import {ButtonComponent} from "../button/button.component";
import { CommonModule, DatePipe } from '@angular/common';
import { TimeAgoPipe } from '../../../@shared/pipes/time-ago.pipe';

interface IncidentGroup {
  workOrderId: number;
  workOrderTitle: string;
  workOrderNumber: string;
  workOrderExecutionAt: Date;
  incidents: ExtendedIncidentResponse[];
}

@Component({
  selector: 'app-crew-reports',
  standalone: true,
  imports: [
    NgForOf,
    NgIf,
    TranslateModule,
    NgClass,
    AddReportComponent,
    ButtonComponent,
    CommonModule,
    DatePipe,
    TimeAgoPipe
  ],
  templateUrl: './crew-reports.component.html',
  styleUrl: './crew-reports.component.scss'
})
export class CrewReportsComponent implements OnInit {
  @Input() order?: OrderResponse;

  constructor(
    private orderService: OrderService,
    private modalService: NgbModal,
    private screenSizeService: ScreenSizeService,
    private cdr: ChangeDetectorRef
  ) {}

  allIncidents: ExtendedIncidentResponse[] = [];
  destroy$ = new Subject<void>();

  currentImages: string[] = [];
  currentIndex: number = 0;
  groupedIncidents: IncidentGroup[] = [];

  @ViewChild('imageModalTemplate') imageModalTemplate!: TemplateRef<any>;
  @ViewChild('addReportTemplate') addReportTemplate!: TemplateRef<any>;

  ngOnInit() {
    console.log('Initial order:', this.order);
    if (this.order?.order_id) {
      this.getIncidents();
    }
    
    this.orderService.order$
      .pipe(takeUntil(this.destroy$))
      .subscribe(order => {
        console.log('Order from subscription:', order);
        this.order = order;
        if (this.order?.order_id) {
          this.getIncidents();
        }
      });
  }

  private getIncidents() {
    if (this.order?.order_id) {
      console.log('Getting incidents for order:', this.order.order_id);
      this.orderService.getIncidents({order_id: this.order.order_id}).subscribe((incidents) => {
        console.log('Received incidents:', incidents);
        this.allIncidents = incidents;
        this.groupIncidents(); // Only group after receiving incidents
      });
    }
  }

  private groupIncidents() {
    console.log('Starting groupIncidents. All incidents:', this.allIncidents);
    if (!this.allIncidents?.length) {
      console.log('No incidents found');
      return;
    }

    // Create a map to group incidents by work order
    const groupMap = new Map<number, IncidentGroup>();

    this.allIncidents.forEach(incident => {
      console.log('Processing incident:', incident);
      
      if (!groupMap.has(incident.work_order_id)) {
        groupMap.set(incident.work_order_id, {
          workOrderId: incident.work_order_id,
          workOrderTitle: incident.work_order_title,
          workOrderNumber: incident.work_order_number,
          workOrderExecutionAt: incident.work_order_execution_at,
          incidents: []
        });
      }

      groupMap.get(incident.work_order_id)?.incidents.push(incident);
    });

    // Convert map to array and sort by execution date
    this.groupedIncidents = Array.from(groupMap.values())
      .sort((a, b) => b.workOrderExecutionAt.getTime() - a.workOrderExecutionAt.getTime());

    // Sort incidents within each group by creation date
    this.groupedIncidents.forEach(group => {
      group.incidents.sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    console.log('Grouped incidents:', this.groupedIncidents);
    this.cdr.detectChanges();
  }

  openAddReport() {
    const modalOptions = this.screenSizeService.isMobile ? 
      { 
        windowClass: 'bottom-sheet-modal',
        animation: false,
        backdrop: 'static' as const
      } : 
      { 
        centered: true,
        backdrop: true as const
      };

    const modalRef = this.modalService.open(this.addReportTemplate, modalOptions);
    
    if (this.screenSizeService.isMobile) {
      // Add bottom sheet class to the component
      const addReportElement = modalRef.componentInstance.addReportComponent?.elementRef.nativeElement;
      if (addReportElement) {
        addReportElement.classList.add('bottom-sheet');
      }
    }
  }

  onReportAdded() {
    console.log('Report added, refreshing incidents');
    this.closeModal();
    if (this.order?.order_id) {
      this.getIncidents(); // Just get the incidents again
    }
  }

  openImageModal(images: string[], index: number) {
    this.currentImages = images;
    this.currentIndex = index;
    this.modalService.open(this.imageModalTemplate, { centered: true, size: 'lg' });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  prevImage(event: Event) {
    event.preventDefault();
    if (this.currentIndex > 0) {
      this.currentIndex--;
    } else {
      this.currentIndex = this.currentImages.length - 1;
    }
    this.cdr.detectChanges();
  }

  nextImage(event: Event) {
    event.preventDefault();
    if (this.currentIndex < this.currentImages.length - 1) {
      this.currentIndex++;
    } else {
      this.currentIndex = 0;
    }
    this.cdr.detectChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

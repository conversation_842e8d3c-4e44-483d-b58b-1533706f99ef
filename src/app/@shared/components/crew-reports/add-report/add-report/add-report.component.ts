import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { EndpointService } from '../../../../services/endpoints.service';
import { IncidentResponse, OrderResponse, WorkOrderResponse } from '../../../../models/order/order.module';
import { CRM_ORD_180, CRM_ORD_181, CRM_ORD_161 } from '../../../../models/input/input.service';
import { TranslateModule } from '@ngx-translate/core';
import { OrderService } from '../../../../services/order.service';

@Component({
  selector: 'app-add-report',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule],
  templateUrl: './add-report.component.html',
  styleUrl: './add-report.component.scss'
})
export class AddReportComponent implements OnInit {
  @Input() order?: OrderResponse;
  @Output() reportAdded = new EventEmitter<void>();

  reportForm = new FormGroup({
    workOrderId: new FormControl<number | null>(null, Validators.required),
    description: new FormControl('', Validators.required)
  });

  selectedImages: File[] = [];
  previewUrls: string[] = [];
  isSubmitting = false;
  workOrders: WorkOrderResponse[] = [];

  constructor(
    private endpointService: EndpointService,
    private orderService: OrderService
  ) {}

  ngOnInit() {
    if (this.order?.order_id) {
      this.getAllWorkOrders();
    }

    // Add listener for description changes
    this.reportForm.get('description')?.valueChanges.subscribe(() => {
      this.checkWorkOrderSelection();
    });
  }

  formatExecutionDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getAllWorkOrders() {
    if (this.order?.order_id) {
      const params: CRM_ORD_161 = {
        order_id: this.order.order_id,
        template_filter: false
      };

      this.orderService.getAllWorkOrders(params).subscribe((res: WorkOrderResponse[]) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Filter work orders from today onwards
        let filteredOrders = res.filter(workOrder => {
          const workOrderDate = new Date(workOrder.execution_at);
          return workOrderDate >= today;
        });

        // Sort by execution date
        filteredOrders.sort((a, b) => {
          const dateA = new Date(a.execution_at).getTime();
          const dateB = new Date(b.execution_at).getTime();
          return dateA - dateB;
        });

        // Update the work orders list
        this.workOrders = filteredOrders;
      });
    }
  }

  checkWorkOrderSelection() {
    const workOrderControl = this.reportForm.get('workOrderId');
    if (!workOrderControl?.value) {
      workOrderControl?.markAsTouched();
    }
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      const files = Array.from(input.files);
      this.selectedImages = [...this.selectedImages, ...files];
      
      // Check work order selection when images are added
      this.checkWorkOrderSelection();
      
      // Generate preview URLs
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e: any) => {
          this.previewUrls.push(e.target.result);
        };
        reader.readAsDataURL(file);
      });
    }
  }

  removeImage(index: number) {
    this.selectedImages.splice(index, 1);
    this.previewUrls.splice(index, 1);
  }

  onSubmit() {
    // Mark all fields as touched to trigger validation display
    Object.keys(this.reportForm.controls).forEach(key => {
      const control = this.reportForm.get(key);
      control?.markAsTouched();
    });

    if (this.reportForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const workOrderId = this.reportForm.get('workOrderId')?.value;
      if (!workOrderId) {
        console.error('No work order selected');
        this.isSubmitting = false;
        return;
      }

      // Create the incident first
      const createIncidentPayload: CRM_ORD_180 = {
        work_order_id: workOrderId,
        description: this.reportForm.get('description')?.value || '',
        incident_type_id: 1
      };

      this.orderService.createIncident(createIncidentPayload)
        .subscribe({
          next: (response: IncidentResponse) => {
            console.log('Incident created successfully', response);
            // If there are images, upload them one by one
            if (this.selectedImages.length > 0) {
              this.uploadImages(response.incident_id);
            } else {
              this.handleSuccess();
            }
          },
          error: (error) => {
            console.error('Error creating incident', error);
            this.isSubmitting = false;
          }
        });
    }
  }

  private uploadImages(incidentId: number) {
    let uploadedCount = 0;
    
    this.selectedImages.forEach((image, index) => {
      const uploadPayload: CRM_ORD_181 = {
        incident_id: incidentId,
        image: image
      };

      this.orderService.addIncidentAttachment(uploadPayload)
        .subscribe({
          next: () => {
            uploadedCount++;
            if (uploadedCount === this.selectedImages.length) {
              this.handleSuccess();
            }
          },
          error: (error) => {
            console.error(`Error uploading image ${index + 1}`, error);
            uploadedCount++;
            if (uploadedCount === this.selectedImages.length) {
              this.handleSuccess();
            }
          }
        });
    });
  }

  private handleSuccess() {
    this.resetForm();
    this.reportAdded.emit();
  }

  private resetForm() {
    this.reportForm.reset();
    this.selectedImages = [];
    this.previewUrls = [];
    this.isSubmitting = false;
  }
}

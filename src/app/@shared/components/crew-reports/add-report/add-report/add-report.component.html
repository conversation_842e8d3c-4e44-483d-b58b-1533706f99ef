<div>

  <form [formGroup]="reportForm" (ngSubmit)="onSubmit()">
    <!-- Work Order Selection -->
    <div class="form-group mb-3">
      <select 
        id="workOrderId" 
        formControlName="workOrderId" 
        class="form-select"
        [class.is-invalid]="reportForm.get('workOrderId')?.invalid && reportForm.get('workOrderId')?.touched">
        <option [ngValue]="null">{{ "incidents.select.work.order" | translate }}</option>
        <option *ngFor="let workOrder of workOrders" [value]="workOrder.work_order_id">
          {{ workOrder.work_order_id }} - {{ formatExecutionDate(workOrder.execution_at) }}
        </option>
      </select>
      <div class="invalid-feedback" *ngIf="reportForm.get('workOrderId')?.invalid && reportForm.get('workOrderId')?.touched">
        {{ "incidents.select.work.order.required" | translate }}
      </div>
    </div>

    <!-- Image upload area -->
    <div class="upload-area">
      <div class="upload-buttons">
        <button type="button" class="upload-btn" (click)="cameraInput.click()">
          <i class="fas fa-camera"></i>
        </button>
        <button type="button" class="upload-btn" (click)="galleryInput.click()">
          <i class="fas fa-image"></i>
        </button>
        <!-- Camera input -->
        <input 
          #cameraInput
          type="file" 
          (change)="onFileSelected($event)" 
          accept="image/*" 
          capture="environment"
          class="d-none">
        <!-- Gallery input -->
        <input 
          #galleryInput
          type="file" 
          (change)="onFileSelected($event)" 
          accept="image/*" 
          multiple
          class="d-none">
      </div>
      <div class="upload-placeholder" *ngIf="previewUrls.length === 0">
        {{ "incidents.upload.image.placeholder" | translate }}
      </div>
      <div class="image-preview-container" *ngIf="previewUrls.length > 0">
        <div class="image-preview" *ngFor="let url of previewUrls; let i = index">
          <img [src]="url" alt="Preview">
          <button type="button" class="remove-image" (click)="removeImage(i)">×</button>
        </div>
      </div>
    </div>

    <!-- Description textarea -->
    <div class="description-area">
      <textarea 
        formControlName="description" 
        class="form-control" 
        rows="4"
        placeholder="{{ 'incidents.description.placeholder' | translate }}">
      </textarea>
    </div>

    <!-- Submit button -->
    <button type="submit" class="submit-btn" [disabled]="!reportForm.valid || isSubmitting">
      <span *ngIf="!isSubmitting">{{ "incidents.add.report" | translate }}</span>
      <span *ngIf="isSubmitting">
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        {{ "incidents.saving.report" | translate }}
      </span>
    </button>
  </form>
</div>

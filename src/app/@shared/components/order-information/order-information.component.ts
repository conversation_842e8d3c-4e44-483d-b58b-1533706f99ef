import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {OrderResponse, WorkOrderAddressResponse, WorkOrderResponse} from "../../models/order/order.module";
import {TranslateModule} from "@ngx-translate/core";
import { UtilsService } from "../../../@core/@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import { CompanyResponse } from '../../models/company/company.module';
import { OrderService } from '../../services/order.service';
import {ContactComponent} from "../contact-address/contact.component";
import {ButtonComponent} from "../button/button.component";

@Component({
  selector: 'app-order-information',
  standalone: true,
  imports: [CommonModule, TranslateModule, ContactComponent, ButtonComponent],
  templateUrl: './order-information.component.html',
  styleUrl: './order-information.component.scss'
})
export class OrderInformationComponent implements OnInit {
  @Input() company?: CompanyResponse = {} as CompanyResponse;
  orderData: OrderResponse = {} as OrderResponse;

  constructor(public utilsService: UtilsService, private orderService: OrderService,) {}

  ngOnInit(): void {
    this.orderService.order$.subscribe(order => {
      if (order.order_id) {
        this.orderData = order;
      }
    });
  }

}

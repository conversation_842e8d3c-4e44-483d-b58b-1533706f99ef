import {Component, Input, OnInit} from '@angular/core';
import {CommonModule, NgForOf, NgIf} from "@angular/common";
import {OrderLinesSummaryComponent} from "../order-lines-summary/order-lines-summary.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderLineResponse, OrderResponse} from "../../models/order/order.module";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {OrderService} from "../../services/order.service";
import {PaymentService} from "../../services/payment.service";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {AddPaymentDetailsComponent} from "../add-payment-details/add-payment-details.component";
import {OrderLinesComponent} from "../order-lines/order-lines.component";

@Component({
  selector: 'app-repeating-payment',
  standalone: true,
  imports: [
    NgForOf,
    NgIf,
    OrderLinesSummaryComponent,
    CommonModule,
    TranslateModule,
    AddPaymentDetailsComponent,
    OrderLinesComponent
  ],
  templateUrl: './repeating-payment.component.html',
  styleUrl: './repeating-payment.component.scss'
})
export class RepeatingPaymentComponent implements OnInit {
  @Input() isQuote: boolean = false;
  orderData?: OrderResponse;
  repeatingPayments: OrderPaymentResponse[] = [];

  constructor(
      public utilsService: UtilsService,
      private orderService: OrderService,
      public translate: TranslateService,
      private paymentService: PaymentService,
  ) {}

  ngOnInit() {
    this.orderService.order$.subscribe(order => {
      if (order.order_id) {
        this.orderData = order;
        this.getRepeatingPayments();
      }
    });
  }

  getRepeatingPayments() {
    const payload = {
      order_id: this.orderData!.order_id,
    };

    this.paymentService.getRepeatingPayments(payload).subscribe((res) => {
      this.repeatingPayments = res;
      console.log("Repeating payments:", this.repeatingPayments);
    });
  }
}

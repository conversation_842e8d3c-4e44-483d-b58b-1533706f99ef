<div class="mb-3" *ngIf="orderData && repeatingPayments.length > 0">
  <ng-container *ngFor="let payment of repeatingPayments">
    <div *ngIf="payment.order_lines.length > 0">
      <div class="border rounded-4 mb-3">
        <div class="component-container">
          <div class="d-flex justify-content-between align-items-center">
            <h5>
              <i class="fa-regular fa-credit-card me-2" aria-hidden="true" title="Repeating Payment" style="color: var(--primary-color);"></i>
              {{ "repeatingPayment.payment" | translate }}
            </h5>
            <h6 class="p-1" style="font-size:small; background-color: var(--primary-color); border-radius: 5px; color:white">{{ "repeatingPayment.repeatingPayment" | translate }}</h6>
          </div>

          <div class=" title mb-2" *ngIf="payment.payment_schedule">
            <div class="title mb-2"><i class="fa-light fa-repeat"></i> {{ "repeatingPayment.frequency" | translate }}: {{ "repeatingPayment.repeatingOrder.repeats" | translate}}  {{ "repeatingPayment.every" | translate }} {{ payment.payment_schedule.every }} {{ payment.payment_schedule.schedule_repeat_type_name }}</div>
            <!-- Display schedule description if available -->
            <div *ngIf="payment.payment_schedule.schedule_description">
              <i class="fa-light fa-file-invoice-dollar fa-lg"></i> {{ "repeatingPayment.invoiced" | translate }} {{ payment.payment_schedule.schedule_description }}
            </div>
          </div>

          <app-order-lines [showHeader]="false" [orderLines]="payment.order_lines" [template]="true" [showRemainingAmount]="false"></app-order-lines>
          <div *ngIf="payment.payment_method_id== 14" class="mb-1 mt-4">
            <app-add-payment-details [repeatingPayments]="payment"></app-add-payment-details>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</div>

import {Component, Input, OnInit} from '@angular/core';
import {OrderService} from "../../services/order.service";
import {OrderResponse} from "../../models/order/order.module";
import {AddPaymentDetailsComponent} from "../add-payment-details/add-payment-details.component";
import {OrderLinesSummaryComponent} from "../order-lines-summary/order-lines-summary.component";
import {LangChangeEvent, TranslateModule, TranslateService} from "@ngx-translate/core";
import {DatePipe, NgForOf, NgIf} from "@angular/common";
import {Subscription} from "rxjs";
import {
  WorkOrdersListComponent
} from "../../../pages/order/order-details/components/work-orders-list/work-orders-list.component";
import {OrderLinesComponent} from "../order-lines/order-lines.component";
import {RepeatingPaymentComponent} from "../repeating-payment/repeating-payment.component";
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {PaymentService} from "../../services/payment.service";

@Component({
  selector: 'app-repeating-job',
  standalone: true,
  imports: [
    AddPaymentDetailsComponent,
    OrderLinesSummaryComponent,
    TranslateModule,
    DatePipe,
    NgForOf,

    NgIf,
    WorkOrdersListComponent,
    OrderLinesComponent,
    RepeatingPaymentComponent
  ],
  templateUrl: './repeating-job.component.html',
  styleUrl: './repeating-job.component.scss'
})
export class RepeatingJobComponent implements OnInit{
  @Input() compactView: boolean = false;
  orderData: OrderResponse = {} as OrderResponse;
  langChangeSubscription: Subscription;
  currentLang: string;
  repeatingPayments: OrderPaymentResponse[] = [];

  constructor(
    private orderService: OrderService,
    private translate: TranslateService,
    private paymentService: PaymentService,
  ) {
    // Initialize current language
    this.currentLang = this.translate.currentLang || 'en';

    // Subscribe to language change events
    this.langChangeSubscription = this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this.currentLang = event.lang;
      this.translateScheduleDescriptions();
    });
  }

  ngOnInit() {
    this.orderService.order$.subscribe((order) => {
      this.orderData = order;
      this.translateScheduleDescriptions();
    });
  }

  ngOnDestroy() {
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }


  getRepeatingPayments() {
    const payload = {
      order_id: this.orderData!.order_id,
    }
    this.paymentService.getRepeatingPayments(payload).subscribe(res => {
      this.repeatingPayments = res;
    });
  }






  /**
   * Translates all schedule descriptions based on the current language.
   */
  translateScheduleDescriptions() {
    if (this.orderData.work_order_schedule_templates) {
      this.orderData.work_order_schedule_templates.forEach((template) => {
        if (template.schedule?.schedule_description) {
          template.schedule.schedule_description = this.translateScheduleDescription(
            template.schedule.schedule_description
          );
        }
      });
    }
  }

  /**
   * Translates the schedule description based on the current language.
   * @param description The original schedule description from the backend.
   * @returns The translated schedule description.
   */
  translateScheduleDescription(description: string): string {
    if (this.currentLang === 'no') {
      // Define sentence patterns and their corresponding translation keys
      const sentenceMappings: Array<{ regex: RegExp; key: string }> = [
        {
          regex: /every week on (.+)/i,
          key: 'schedule.weekly.everyWeekOn'
        },
        {
          regex: /every (\d+) weeks on (.+)/i,
          key: 'schedule.weekly.everyXWeeksOn'
        },
        {
          regex: /every day/i,
          key: 'schedule.daily.everyDay'
        },
        {
          regex: /every (\d+) days/i,
          key: 'schedule.daily.everyXDays'
        },
        {
          regex: /every month on the (\d+)./i,
          key: 'schedule.monthly.everyMonthOnDate'
        },
        {
          regex: /every (\d+) months on the (\d+)./i,
          key: 'schedule.monthly.everyXMonthsOnDate'
        },
        {
          regex: /every month on the (\d+)(?:st|nd|rd|th)? (\w+)/i,
          key: 'schedule.monthly.nthWeekdayEveryMonth'
        },
        {
          regex: /every (\d+) months on the (\d+)(?:st|nd|rd|th)? (\w+)/i,
          key: 'schedule.monthly.nthWeekdayEveryXMonths'
        }
        // Add more patterns as needed
      ];

      // Define mapping for weekdays
      const weekdayMappings: Record<string, string> = {
        Monday: this.translate.instant('schedule.weekdays.1'),
        Tuesday: this.translate.instant('schedule.weekdays.2'),
        Wednesday: this.translate.instant('schedule.weekdays.3'),
        Thursday: this.translate.instant('schedule.weekdays.4'),
        Friday: this.translate.instant('schedule.weekdays.5'),
        Saturday: this.translate.instant('schedule.weekdays.6'),
        Sunday: this.translate.instant('schedule.weekdays.7')
      };

      // Iterate over sentence patterns to find a match
      for (const mapping of sentenceMappings) {
        const matches = mapping.regex.exec(description);
        if (matches) {
          const interpolateData: Record<string, any> = {};

          // Assign interpolateData based on regex groups
          switch (mapping.key) {
            case 'schedule.weekly.everyWeekOn':
              interpolateData['weekdays'] = this.translateWeekdays(matches[1]);
              break;
            case 'schedule.weekly.everyXWeeksOn':
              interpolateData['count'] = matches[1];
              interpolateData['weekdays'] = this.translateWeekdays(matches[2]);
              break;
            case 'schedule.daily.everyDay':
              // No interpolation needed
              break;
            case 'schedule.daily.everyXDays':
              interpolateData['count'] = matches[1];
              break;
            case 'schedule.monthly.everyMonthOnDate':
              interpolateData['date'] = matches[1];
              break;
            case 'schedule.monthly.everyXMonthsOnDate':
              interpolateData['count'] = matches[1];
              interpolateData['date'] = matches[2];
              break;
            case 'schedule.monthly.nthWeekdayEveryMonth':
              interpolateData['nth'] = this.translate.instant(`schedule.nth.${matches[1]}`);
              interpolateData['weekday'] = weekdayMappings[matches[2]] || matches[2];
              break;
            case 'schedule.monthly.nthWeekdayEveryXMonths':
              interpolateData['count'] = matches[1];
              interpolateData['nth'] = this.translate.instant(`schedule.nth.${matches[2]}`);
              interpolateData['weekday'] = weekdayMappings[matches[3]] || matches[3];
              break;
            default:
              break;
          }

          return this.translate.instant(mapping.key, interpolateData);
        }
      }

      // If no sentence pattern matches, try replacing weekdays as a fallback
      return this.replaceWeekdays(description, weekdayMappings);
    }

    // For 'en' and other languages, return description as-is
    return description;
  }

  /**
   * Translates a comma-separated list of weekdays.
   * @param weekdaysStr Comma-separated weekdays (e.g., "Monday, Wednesday").
   * @returns Translated weekdays (e.g., "Mandag, Onsdag").
   */
  translateWeekdays(weekdaysStr: string): string {
    const weekdayMappings: Record<string, string> = {
      Monday: this.translate.instant('schedule.weekdays.1'),
      Tuesday: this.translate.instant('schedule.weekdays.2'),
      Wednesday: this.translate.instant('schedule.weekdays.3'),
      Thursday: this.translate.instant('schedule.weekdays.4'),
      Friday: this.translate.instant('schedule.weekdays.5'),
      Saturday: this.translate.instant('schedule.weekdays.6'),
      Sunday: this.translate.instant('schedule.weekdays.7')
    };

    return weekdaysStr
      .split(', ')
      .map(day => weekdayMappings[day] || day)
      .join(', ');
  }

  /**
   * Replaces English weekdays with their Norwegian equivalents in the description.
   * Used as a fallback if no sentence pattern matches.
   * @param description The original schedule description.
   * @param weekdayMappings Mapping of English weekdays to Norwegian.
   * @returns The description with weekdays translated.
   */
  replaceWeekdays(description: string, weekdayMappings: Record<string, string>): string {
    Object.keys(weekdayMappings).forEach((weekday) => {
      const regex = new RegExp(weekday, 'gi'); // Case-insensitive matching
      description = description.replace(regex, weekdayMappings[weekday]);
    });
    return description;
  }
}


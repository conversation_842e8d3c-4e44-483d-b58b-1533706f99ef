
<div *ngIf="orderData">
<ng-container *ngFor="let template of orderData.work_order_schedule_templates">
  <div>
    <div class="border rounded-4 mb-3">
      <div class="component-container">

        <div class="d-flex justify-content-between align-items-center">
          <h5>
            <i class="fa fa-repeat me-2" aria-hidden="true" title="Repeating Payment" style="color: var(--primary-color);"></i>
            {{ template.work_order_title || "repeatingPayment.repeatingOrder" | translate }}
          </h5>
          <h6 class="p-1" style="font-size:small; background-color: var(--primary-color); border-radius: 5px; color:white">{{ "repeatingPayment.repeatingOrder" | translate }}</h6>
        </div>


        <div class="title mb-2">
        <div *ngIf="template.addresses && template.addresses.length > 0">
            <div *ngFor="let address of template.addresses">
              <i class="fa-regular fa-location-dot"></i> {{ template.addresses.length > 1? address.address_name + ': ': ''}} {{ address.display }}
            </div>
        </div>
<!--        <div *ngIf="!template.addresses || template.addresses.length === 0">-->
<!--          {{ 'order.details.missingAddress' | translate }}-->
<!--        </div>-->
        </div>

        <div *ngIf="template.execution_at" class="title mb-2">
          <i class="fa-regular fa-clock"></i> {{ "order.details.executionTime" | translate }}: {{ template.execution_at | date: 'HH:mm' }}
        </div>
        <div class="title mb-2">
          <i class="fa-light fa-repeat"></i> {{ "repeatingPayment.frequency" | translate }}: {{ "repeatingPayment.repeatingOrder.repeats" | translate}} {{template.schedule!.schedule_description}}
        </div>
        <div class="title mb-3">
          <i class="fa-regular fa-phone"></i> {{ "repeatingPayment.subscription" | translate }}
        </div>

<!--        <div *ngIf="compactView && template.order_lines.length > 1" class="h6">-->
<!--          {{ "repeatingPayment.nextFiveOrders" | translate }}-->

<!--        </div>-->

        <app-work-orders-list *ngIf="orderData" [template]="template" [compactView]="compactView"></app-work-orders-list>
        <div *ngIf="template.payment">
          <h6 class="mt-3"> {{ "repeatingPayment.summary" | translate }}</h6>
            <app-order-lines [orderLines]="template.payment!.order_lines" [template]="true" [showRemainingAmount]="false"></app-order-lines>
          <div *ngIf="template.payment?.order_lines && template.payment.payment_method_id == 14" class="mb-1 mt-4">
            <app-add-payment-details [repeatingPayments]="template.payment!"></app-add-payment-details>
          </div>
        </div>
      </div>
    </div>


  </div>
</ng-container>
</div>

import {ChangeDetectorRef, Component, EventEmitter, Inject, Input, OnChanges, OnInit, Output, SimpleChanges} from '@angular/core';
import {USM_USR_8, USM_USR_9} from "../../models/input/input.service";
import {FormControl, Validators} from "@angular/forms";
import {AuthService} from "../../../@core/@core/services/auth.service";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {OrderResponse} from "../../models/order/order.module";
import {NgOtpInputModule} from "ng-otp-input";
import {TranslateModule} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {NgbActiveModal, NgbModalModule} from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: 'app-otpmodal',
  standalone: true,
  imports: [
    NgOtpInputModule,
    TranslateModule,
    CommonModule,
    NgbModalModule
  ],
  templateUrl: './otpmodal.component.html',
  styleUrl: './otpmodal.component.scss'
})
export class OTPModalComponent implements OnInit, OnChanges {
  @Input() phone: string = '';
  @Input() otpStatus: boolean = false;
  @Input() order: OrderResponse | null = null;

  otpControl: FormControl = new FormControl();
  isOtpSent = false;
  isLoading: boolean = false;
  resendCountdown = 60;
  isOtpResend = false;
  companyId: string | null = null;

  @Output() otpConfirmed: EventEmitter<any> = new EventEmitter<any>();
  @Output() otpResend: EventEmitter<any> = new EventEmitter<any>();

  constructor(private activeModal: NgbActiveModal,
              private changeDetectorRef: ChangeDetectorRef,
              private authService: AuthService,
              private storageService: StorageService)
              {}

  ngOnInit(): void {
    this.companyId = this.storageService.getSelectedCompanyId();
    this.otpControl.setValidators([Validators.required, Validators.minLength(4), Validators.maxLength(4)]);
    let params: USM_USR_9 = {
      phone: this.order?.payment_recipient!.phone!
    }
    console.log('params', params);
    this.authService.initiateOtp(params).subscribe();
  }

  onOtpChange(): void {
    // Handle OTP change event
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['otpStatus']) {
      this.isOtpSent = this.otpStatus;
      this.changeDetectorRef.detectChanges(); // Trigger change detection
    }
  }

  onCancel(): void {
    // Handle cancellation event
    this.activeModal.close(); // Close the modal
  }

  resendOtp() {
    this.startCountdown();
    this.authService.initiateOtp({phone: this.order?.payment_recipient!.phone!}).subscribe();
    this.resendCountdown = 60;
  }

  startCountdown(): void {
    this.isOtpResend = true;
    const countdownInterval = setInterval(() => {
      this.resendCountdown--;

      if (this.resendCountdown === 0) {
        clearInterval(countdownInterval);
        this.isOtpResend = false;
      }
    }, 1000);
  }


  onConfirm(): void {

    if (this.otpControl.value.length === 4) {
      this.isLoading = true;
      this.sendOtp(this.otpControl.value);

    } else {
      this.isLoading = false;
    }
  }

  sendOtp(otp: string) {
    this.isLoading = true;

    let phone: string | undefined;

    if (this.order && this.order.payment_recipient!.is_private === 1 && this.order.payment_recipient!.phone) {
      phone = this.order.payment_recipient!.phone;
    } else if (this.order && this.order.affiliate_contact && this.order.affiliate_contact.phone) {
      phone = this.order.affiliate_contact.phone;
    }

    if (phone) {
      const params: USM_USR_8 = {
        phone: phone,
        otp: otp
      };

      this.authService.sendOtp(params).subscribe({
        next: (res) => {
          this.isLoading = false;
          this.otpStatus = false;
          this.changeDetectorRef.detectChanges();

          this.otpConfirmed.emit(true);
          this.activeModal.close(); // Close the modal
        },
        error: (error) => {
          this.otpStatus = true;
          this.isLoading = false;
          this.changeDetectorRef.detectChanges();
        }
      });
    } else {
      // Handle the case where no valid phone number is available
      console.error('No valid phone number available for OTP');
      this.isLoading = false;
    }
  }
}

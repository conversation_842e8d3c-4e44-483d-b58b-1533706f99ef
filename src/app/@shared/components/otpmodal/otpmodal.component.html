<div class="p-3">
  <!-- <div class="d-flex justify-content-end">
    <button class="btn-close" (click)="onCancel()"></button>
  </div> -->
  <h5 class="text-center mt-2 mb-3">{{ "otp-modal.heading" | translate }}</h5>
  <div class="">
    <div class="otp-inputs text-center mb-3">
      <ng-otp-input
        [formCtrl]="otpControl"
        [config]="{ length: 4, allowNumbersOnly: true  }"
        (onInputChange)="onOtpChange()"
      ></ng-otp-input>
    </div>
    <div class="py-2">
      <p class="text-center mb-1" *ngIf="!isOtpResend">{{"otp.didntReceiveSMS" | translate}}</p>
      <p class="text-center cursor-pointer" *ngIf="!isOtpResend" (click)="resendOtp()"><ins>{{"otp.sendSMSAgain" | translate}}</ins></p>
      <p class="text-center cursor-pointer" *ngIf="isOtpResend">{{"otp-modal.otpSentPleaseTryAgain" | translate}} {{resendCountdown}} {{"otp-modal.seconds" | translate}}.</p>
    </div>
  </div>
  <div class="alert alert-danger text-center mb-4" *ngIf="otpStatus">
    {{ "otp-modal.invalidOTP" | translate }}
  </div>
  <div class="buttons d-flex justify-content-center align-items-center">
    <!-- <button class="btn btn-secondary" (click)="onCancel()">{{ "otp-modal.cancelBtn" | translate }}</button> -->
    <button
      class="btn btn-primary d-flex"
      [disabled]="isLoading || !otpControl.valid"
      (click)="onConfirm()">
        <span class="ms-1">{{ "otp-modal.confirmBtn" | translate }}</span>
    </button>
  </div>
  <div class="mt-4 text-center">
    <p>{{ "otp-modal.confirmTerms" | translate }} <a href="terms/{{companyId}}">{{ "otp-modal.termsOfSale" | translate }}</a></p>
  </div>
</div>

import {Component, EventEmitter, Input, Output} from '@angular/core';
import {CommonModule} from "@angular/common";
import {FormControl, FormsModule} from "@angular/forms";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import { CRM_ORD_37 } from '../../models/input/input.service';
import { OrderResponse } from '../../models/order/order.module';
import { OrderService } from '../../services/order.service';
import {ToastService} from "../../../@core/@core/services/toast.service";
import {ButtonComponent} from "../button/button.component";

@Component({
  selector: 'app-rating',
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule, ButtonComponent],
  templateUrl: './rating.component.html',
  styleUrl: './rating.component.scss'
})
export class RatingComponent {
  @Input() order: OrderResponse = {} as OrderResponse;
  @Input() rating: number = 0;
  @Input() comment: string = '';
  @Input() isReadOnly: boolean = false;
  stars: number[] = [1, 2, 3, 4, 5];
  currentRating: number = 0;
  currentComment: string = '';
  feedbackSent: boolean = false;
  selectedRating: number = 0;
  showError: boolean = false;

  @Output() ratingChanged = new EventEmitter<number>();
  @Output() commentSubmitted = new EventEmitter<string>();

  constructor(
    private orderService: OrderService,
    private toastService: ToastService,
    private translate: TranslateService
  ) { }

  ngOnInit() {
    if (this.rating) {
      this.currentRating = this.rating;
    }
    if (this.comment) {
      this.currentComment = this.comment;
    }
  }

  rate(rating: number) {
    if (!this.isReadOnly) {
      this.currentRating = rating;
      this.showError = false; // Hide error when rating is selected
    }
  }

  submitRating() {
    if (!this.canSubmitFeedback()) {
      this.toastService.showError(this.translate.instant("order-feedback.errorRequired"));
      return;
    }


    const params: CRM_ORD_37 = {
      order_id: this.order.order_id,
      rating: this.currentRating,
      comment: this.currentComment
    };

    this.orderService.setOrderFeedback(params).subscribe(res => {
      // Handle rating submission response if needed
    });
    this.feedbackSent = true;
    this.isReadOnly = true;
    this.showError = false; // Hide error after successful submission
  }

  canSubmitFeedback(): boolean {
    return this.currentRating > 0 || this.currentComment.trim().length > 0;
  }
}

<div class="star-rating">
  <span class="star" *ngFor="let star of stars" (click)="rate(star)">
    <i class="fa-regular fa-star mx-1" *ngIf="star > currentRating"></i>
    <i class="fa-solid fa-star text-warning mx-1" *ngIf="star <= currentRating"></i>
  </span>
</div>

<div *ngIf="!feedbackSent && !isReadOnly">
  <div class="comment-section mt-3">
    <textarea [(ngModel)]="currentComment" class="form-control" rows="3" [placeholder]="'order-feedback.leaveComment' | translate"></textarea>
  </div>

  <div class="submit-section mt-3">
    <app-button  [buttonClass]="'btn btn-primary py-1 w-100'" (buttonClick)="submitRating()">{{"order-feedback.sendFeedback" | translate}}</app-button>
  </div>
</div>

<div *ngIf="feedbackSent" class="feedback-sent-section mt-3">
  <p>{{"order-feedback.feedbackSent" | translate}}</p>
</div>

<div *ngIf="isReadOnly && currentComment" class="readonly-feedback mt-3">
  <p><strong>{{"order-feedback.comment" | translate}}:</strong> {{currentComment}}</p>
</div>

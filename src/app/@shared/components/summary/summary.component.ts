import {Component, Input, OnInit} from '@angular/core';
import {OrderLineResponse, OrderResponse, PaymentStatusHistoryResponse} from "../../models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import {OrderService} from "../../services/order.service";
import { CompanyGeneralSettingsResponse } from '../../models/company/company.module';
import {EndpointService} from "../../services/endpoints.service";
import {PaymentService} from "../../services/payment.service";
import {CRM_PAY_35} from "../../models/input/input.service";

export interface OrderLineView extends OrderLineResponse {
  orderLineName: string;
}

@Component({
  selector: 'app-summary',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class SummaryComponent implements OnInit{
  orderData: OrderResponse | undefined;
  orderLines: OrderLineView[] = [];
  paymentStatus?: PaymentStatusHistoryResponse = {} as PaymentStatusHistoryResponse;
  hasUnitId2: boolean = false;
  @Input() companyGeneralSettings: CompanyGeneralSettingsResponse | undefined;

  constructor(
    public utilsService: UtilsService,
    private orderService: OrderService,
    public translate: TranslateService,
    private paymentService: PaymentService
  ) {}

  ngOnInit() {
    this.orderService.order$.subscribe(order => {
      if (order.order_id) {
        this.orderData = order;
        this.getPayments();
      }
    });
  }




  // Get all payments on order with status "send to payment"
  getPayments(){
    console.log("ORDERDATA",this.orderData)
    if(this.orderData){
    let payload: CRM_PAY_35 = {
      order_id: this.orderData.order_id
    }
    this.paymentService.getPayments(payload).subscribe(res => {
      console.log("PAYMENTS", res)
    });
    }
  }

  // checkTriggerTypeId(line: OrderLineResponse): boolean {
  //   return line.triggered_price_rules.some(rule => rule.trigger_type_id === 4);
  // }

  checkTriggerTypeId(line: any) {
    return false;
  }
}

<div class="border rounded-4 mb-3" *ngIf="orderData">
  <div class="component-container">
    <h6 class="mt-1 mb-3">{{ "order-summary.heading" | translate }}</h6>
<!--    <div class="top" *ngIf="orderData.> 0">-->
      <div class="row header">
        <div class="col title">{{ "order-summary.description" | translate }}</div>
        <div class="col title text-end" *ngIf="orderData.show_prices_inc_vat == 1 && orderData.hide_payment_data != 1">{{ "order-summary.cost" | translate }}</div>
        <div class="col title text-end" *ngIf="orderData.show_prices_inc_vat == 0 && orderData.hide_payment_data != 1">{{ "order-summary.cost" | translate }} {{"order-summary.showIncVatLabel" | translate}}</div>
      </div>
<!--    </div>-->

<!--    <div class="border my-2" *ngIf="orderData.sales_price > 0"></div>-->
<!--    <div id="orderLineList">-->
<!--      &lt;!&ndash; Listed order lines &ndash;&gt;-->
<!--      <div id="listedOrderLines" *ngFor="let line of orderLines">-->
<!--        <div *ngIf="line.total_price_inc_vat !== 0" class="row align-items-center mb-2">-->
<!--          <div class="col-7">-->
<!--            <p class="mb-0 title">{{line.orderLineName}}</p>-->
<!--            <p class="mb-0" *ngIf="(!checkTriggerTypeId(line) && line.unit_id != 2)"> kr {{line.product_price_inc_vat}} x {{line.quantity}} {{line.unit_abbreviation}}</p>-->
<!--            <p *ngIf="line.unit_id == 2 && line.duration_hours_from && line.duration_hours_to && orderData.order_status_id != 7" class="mb-0 font-12">{{"order-summary.estimatedTimeRange" | translate}} {{line.duration_hours_from}} - {{line.duration_hours_to}} {{"common.hours" | translate}}</p>-->
<!--            <p class="mb-0 font-size-xs" *ngIf="line.comment">{{line.comment}}</p>-->
<!--          </div>-->
<!--          <div class="col text-end title " *ngIf="orderData.hide_payment_data != 1 && !companyGeneralSettings?.show_estimated_prices_to_customer">-->
<!--            {{-->
<!--              orderData.order_status_id > 4 ?-->
<!--                this.utilsService.formatCurrency(orderData.show_prices_inc_vat == 1 ? line.total_price_inc_vat : line.total_price_ex_vat, true) :-->
<!--                line.payment_type_id === 1 && line.fixed_price === 0 ?-->
<!--                  this.utilsService.formatCurrency(orderData.show_prices_inc_vat == 1 ? line.product_price_inc_vat : line.product_price_ex_vat, true) + ' per ' + line.unit_name.slice(0, -1).toLocaleLowerCase() :-->
<!--                  this.utilsService.formatCurrency(orderData.show_prices_inc_vat == 1 ? line.total_price_inc_vat : line.total_price_inc_vat, true)-->
<!--            }}-->
<!--          </div>-->
<!--          <div class="col text-end title " *ngIf="orderData.hide_payment_data != 1 && companyGeneralSettings?.show_estimated_prices_to_customer">-->
<!--            {{    this.utilsService.formatCurrency(orderData.show_prices_inc_vat == 1 ? line.total_price_inc_vat : line.total_price_inc_vat, true) }}-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

    <div *ngIf="orderData.hide_payment_data != 1" class=" my-3"></div>
<!--    <div class="border my-2" *ngIf="orderData.calculated_discount_amount > 0" ></div>-->
<!--    <div class="row" *ngIf="orderData.calculated_discount_amount > 0">-->

<!--      <div class="col">{{"order-summary.discount" | translate}} {{orderData.discount_percentage > 0 ? '(' + orderData.discount_percentage + ' %)' : ''}}</div>-->
<!--      <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.calculated_discount_amount)}}</p></div>-->
    </div>

    <div class="border my-2"></div>

    <div *ngIf="!hasUnitId2 || orderData.order_status_id > 5" class="row mb-2">
      <div class="col">{{"order-summary.showExVatLabel" | translate}}</div>
<!--      <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.sales_price_ex_vat)}}</p></div>-->
    </div>

    <div *ngIf="!hasUnitId2 || orderData.order_status_id > 5" class="row mb-2">
      <div class="col">{{"order-summary.vat" | translate}}</div>
<!--      <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.sales_price_vat_amount)}}</p></div>-->
    </div>

    <div class="row mb-2" *ngIf="orderData.refunded_amount !== 0">
      <div class="col">{{"order-summary.refundedAmount" | translate}}</div>
      <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.refunded_amount)}}</p></div>
    </div>

    <div *ngIf="!hasUnitId2 || orderData.order_status_id > 5" class="row total mb-2 title">
      <div class="col ">{{ "order-summary.total" | translate}}</div>
      <div class="col text-end ">
<!--        {{this.utilsService.formatCurrency(orderData.sales_price)}}-->
          <span *ngIf="orderData.refunded_amount !== 0" class="text-danger">(-{{this.utilsService.formatCurrency(orderData.refunded_amount)}})</span>
      </div>
    </div>


    <div *ngIf="hasUnitId2 && orderData.order_status_id <= 5 && !companyGeneralSettings?.show_estimated_prices_to_customer" class="row mt-3 mb-2">
      <div class="col">{{"order-summary.totalCalculated" | translate}}</div>
    </div>

    <div class="estimatedPrice" *ngIf="hasUnitId2 && companyGeneralSettings?.show_estimated_prices_to_customer && orderData.order_status_id <= 5">
      <div class="row mb-2">
        <div class="col">{{"order-summary.showExVatLabel" | translate}}</div>
<!--        <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.sales_price_ex_vat)}}</p></div>-->
      </div>

      <div class="row mb-2">
        <div class="col">{{"order-summary.vat" | translate}}</div>
<!--        <div class="col text-end"><p class="mb-0 ">{{this.utilsService.formatCurrency(orderData.sales_price_vat_amount)}}</p></div>-->
      </div>

      <div class="row total mb-2 title">
        <div class="col ">{{ "order-summary.total" | translate}}</div>
        <div class="col text-end ">
<!--          {{this.utilsService.formatCurrency(orderData.sales_price)}}-->
        </div>
      </div>

      <div *ngIf="hasUnitId2" class="row mt-3 mb-2">
        <div class="col">{{"order-summary.estimatedPrice" | translate}} </div>
      </div>
    </div>
    </div>



<!--    <div *ngIf="orderData.order_status_id > 6" style="padding:  0 0.75rem 0.75rem 0.75rem;">-->
<!--      <div *ngIf="orderData.order_status_id > 6" class="border mb-3"></div>-->
      <div class="row text-bold mb-2">
        <div class="col-4">{{ "order-summary.paymentStatus" | translate }}</div>
<!--        <div class="col text-end">{{orderData.payment_status_name}}</div>-->
      </div>

      <div class="row text-bold mb-2">
        <div class="col-4">{{ "order-summary.paymentDate" | translate }}</div>
<!--        <div class="col text-end">{{orderData.captured_at| date:'dd. MMM yyyy'}}</div>-->
      </div>

      <div class="row text-bold">
        <div class="col-4">{{ "order-summary.paymentMethod" | translate }}</div>
<!--        <div class="col text-end">{{orderData.payment_method_name}}</div>-->
      </div>
<!--    </div>-->

<!--  </div>-->

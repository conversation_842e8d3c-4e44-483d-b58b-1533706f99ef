import {Component, Input, OnInit} from '@angular/core';
import {OrderNoteResponse, OrderResponse} from "../../models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {ButtonComponent} from "../button/button.component";
import {Router} from "@angular/router";
import {UtilsService} from "../../../@core/@core/utils/utils.service";

@Component({
  selector: 'app-order-card',
  standalone: true,
  imports: [CommonModule, TranslateModule, ButtonComponent],
  templateUrl: './order-card.component.html',
  styleUrl: './order-card.component.scss'
})
export class OrderCardComponent implements OnInit {
  @Input() order: OrderResponse = {} as OrderResponse;
  notes: OrderNoteResponse[] = [];
  current_user_id: string = '';

  constructor( private router: Router, public utilsService: UtilsService) {
  }

  ngOnInit(): void {
  }

  navigateToOrderDetails(order: OrderResponse): void {
    this.router.navigate([`/orders/${order.order_id}`]);
  }
}

<div class="order-container mb-3 rounded-4"
     [ngClass]="{
       'blink-green': order.order_status_id === 4,
       'blink-red': order.order_status_id === 6 && order.payment_status_id !== 6
     }">

<div class="d-flex justify-content-between">
    <span *ngIf="!order.order_title" class="black">#{{order.order_number}}</span>
    <span *ngIf="order.order_title"> {{order.order_title}}</span>
    <span class="black">{{order.customer_order_status_name}}</span>
  </div>
  <hr style="margin-bottom: 0.5rem; margin-top: 0.5rem; border-width: 2px ">
  <div class="mb-3 d-flex justify-content-between fw-bold">
    <span>#{{ order.order_number }}</span>
    <span>{{ order.company_name }}</span>
  </div>
<!--  <div class="d-flex justify-content-between mb-2">-->
<!--    <span>{{ "quotes.orderDate" | translate }}</span>-->
<!--    <span class="text-capitalize">{{order.execution_at | date:'EEEE, dd. MMMM yyyy'}}</span>-->
<!--  </div>-->
<!--  <div class="d-flex justify-content-between">-->
<!--    <span>{{ "quotes.time" | translate }}</span>-->
<!--    {{order.execution_at| date:'HH:mm'}}-->
<!--  </div>-->
  <div class="d-flex justify-content-center mt-2">
    <app-button
    buttonClass="btn btn-primary small"
    (buttonClick)="navigateToOrderDetails(order)">
    {{ 'quote.viewDetials' | translate }}
  </app-button>
  </div>
</div>

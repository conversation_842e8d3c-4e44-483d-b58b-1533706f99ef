<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="mb-0">{{repeatingPayments!.subscription_active ? ("order-payment.subscriptionActive" | translate) : ("order-payment.AddSubscription" | translate) }}</h5>
            <p class="mb-0 text-muted" style="font-size: 14px;">{{repeatingPayments!.subscription_active ? ("order-payment.subscriptionActiveDesc" | translate) : ("order-payment.quickpayTagWithoutVipps" | translate) }}</p>
        </div>
        <img src="assets/images/payment-icons/VisaMaster.png" width="100px" alt="Payment Methods" class="img-fluid"/>
    </div>
    <div class="card-body">
        <ng-container *ngIf="!repeatingPayments!.subscription_active; else subscriptionActive">
            <p class="text-muted text-center mb-4" style="font-size: 14px;">{{"order-payment.quickpayDetailsSubscription" | translate}}</p>
            <div class="d-flex justify-content-center">
                <app-button class="w-100" buttonClass="btn btn-primary w-100" (buttonClick)="continuePayment()">{{"order-payment.createSubscription" | translate}}</app-button>
            </div>
        </ng-container>
        <ng-template #subscriptionActive>
            <div class="subscription-info">
                <div class="d-flex align-items-center mb-2">
                    <i class="fa-regular fa-check-circle text-success me-2 fa-lg" aria-hidden="true" title="Subscription Active"></i>
                    <span class="fw-bold">{{ "order-payment.activeSubscription" | translate }}</span>
                </div>
                    <div class="text-muted">{{ "order-payment.changePaymentDetails" | translate }}</div>
                <div class="mb-3">
                    <!-- <p class="mb-1"><strong>{{ "order-payment.subscriptionType" | translate }}:</strong>{{ subscriptionDetails.type || 'N/A' }}</p> -->
                    <!-- <p class="mb-1"><strong>{{ "order-payment.nextBilling" | translate }}:</strong>{{subscriptionDetails.nextBillingDate ? (subscriptionDetails.nextBillingDate | date: 'longDate') : 'N/A' }}</p> -->
                    <!-- <p class="mb-0"><strong>{{ "order-payment.amount" | translate }}:</strong>kr {{ utilsService.formatCurrency(subscriptionDetails.amount, true) }},-</p> -->
                </div>
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-danger" (click)="cancelSubscription()">{{ "order-payment.cancelSubscription" | translate }}</button>
                </div>
            </div>
        </ng-template>
    </div>
</div>

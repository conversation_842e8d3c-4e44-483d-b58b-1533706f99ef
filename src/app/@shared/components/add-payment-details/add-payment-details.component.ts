import {Component, Input, OnInit} from '@angular/core';
import {OrderPaymentResponse} from "../../models/payment/payment.module";
import {CRM_PAY_17, CRM_PAY_40} from "../../models/input/input.service";
import {PaymentService} from "../../services/payment.service";
import {OrderService} from "../../services/order.service";
import {OrderResponse} from "../../models/order/order.module";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonComponent} from "../button/button.component";
import {NgIf} from "@angular/common";
import {DeleteResponse} from "../../models/global/response-wrapper.service";

@Component({
  selector: 'app-add-payment-details',
  standalone: true,
  imports: [TranslateModule, ButtonComponent, NgIf],
  templateUrl: './add-payment-details.component.html',
  styleUrl: './add-payment-details.component.scss'
})
export class AddPaymentDetailsComponent implements OnInit {
  @Input() orderData?: OrderResponse;
  @Input() repeatingPayments: OrderPaymentResponse = {} as OrderPaymentResponse;
constructor(private paymentService: PaymentService, private orderService: OrderService) { }

  ngOnInit() {
    this.orderService.order$.subscribe(order => {
      if (order.order_id) {
        this.orderData = order;
      }
    });
  }

  continuePayment() {
    const payload: CRM_PAY_17 = {
      payment_id: this.repeatingPayments?.payment_id,
      payment_method_id: 14,
      // order_id: this.repeatingPayments.order_id
    };

    this.paymentService.createPaymentSubscription(payload).subscribe({
      next: (res) => {
        console.log('Subscription payment created successfully:', res);
        window.location.href = res.url;
      },
      error: (error) => {
        console.error('Subscription payment creation failed:', error);
      },
    });
  }

  cancelSubscription() {
    const payload: CRM_PAY_40 = {
      payment_id: this.repeatingPayments?.payment_id,
    };

    this.paymentService.cancelSubscription(payload).subscribe({
      next: () => {
        console.log('Subscription payment cancelled successfully');
        this.repeatingPayments.subscription_active = false;
      },
      error: (error) => {
        console.error('Subscription payment cancellation failed:', error);
      },
    });
  }


}

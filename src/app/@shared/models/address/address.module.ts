import {UnitDetails} from "../input/input.service";

export interface AddressSearchResultItemResponse {
  street: string;
  number: string;
  letter: string;
  postal_code: string;
  city: string;
  regionName: string;
  unit_id: string;
  display: string;
  number_of_floors?: number | null;
  property_type_name?: string | null;
  property_type_id?: number | null;
  livable_area?: number | null;
}

export interface AddressDetailsResponse {
  unit: UnitDetails | null;
  search_id: string;
  sections: {[key: string]: UnitDetails};
}

// export interface AmbitaAddressDetailsResponse {
//   lat: number;
//   lng: number;
//   place: string;
//   street: string;
//   zipcode: string;
//   details: {
//     sections: {
//       [key: string]: {
//         [key: string]: AmbitaAddressResponse
//       }
//     }
//   }
// }

// export interface AmbitaResponseWrapper {
//   address_details: AmbitaAddressDetailsResponse
// }

// export interface AmbitaAddressResponse {
//   buildingKey: number;
//   cadastre: string;
//   established_date: string;
//   floorNumber: number;
//   hasElevator: boolean;
//   id: string;
//   bedroom_quantity: number;
//   numberOfBathRooms: number;
//   numberOfBedRooms: number;
//   numberOfFloors: number;
//   numberOfRooms: number;
//   numberOfUnits: number;
//   numberOfWCs: number;
//   share: boolean;
//   specifiedArea: number;
//   totalAreaOfPrimarySpace: number;
//   totalFloorSpace: number;
//   typeOfBuilding: string;
//   usableArea: number;
// }

export interface AddressUnitResponse {
  address_id: number;
  external_id: string;
  city: string;
  lat: number;
  lng: number;
  display: string;
  municipality_id: number;
  municipality_name: string;
  street_id: string;
  street: string;
  number: number;
  letter: string;
  postal_code: string;
  area_id: number;
  area: string;
  cadastre: CadastreResponse | null;
  organisation_number: number;
  condominium_share: string;
  section_id: string;
  floor: number;
  radon_level: number;
  energy_score: null;
  heating_score: null;
  primary_area: number;
  livable_area: number;
  plot_area: number;
  number_of_floors: number;
  number_of_rooms: number;
  number_of_bathrooms: number;
  number_of_bedrooms: number;
  number_of_units_on_address: number;
  has_elevator: number | null;
  has_parking: number | null;
  has_garage: number | null;
  property_type_id: number;
  property_type_name: string;
  build_year: number;
  ownership_type_id: number;
  ownership_type_name?: string;
  homeowners_name: string[] | null;
  homeowners_fraction: number[] | null;
}


interface CadastreResponse {
    kommunenr: number;
    gardsnr: number;
    bruksnr: number;
    festenr: number;
    seksjonsnr: number;
}

export interface GeoAddressResponse {
  metadata: {
    totaltAntallTreff: number;
    asciiKompatibel: boolean;
    viserFra: number;
    viserTil: number;
    treffPerSide: number;
    side: number;
    sokeStreng: string;
  };
  adresser: [GeoAddressItemResponse]

}

export interface GeoAddressItemResponse {
  adressenavn: string,
  adressetekst: string,
  adressetilleggsnavn: string,
  adressekode: number,
  nummer: number,
  bokstav: string,
  kommunenummer: string,
  kommunenavn: string,
  gardsnummer: number,
  bruksnummer: number,
  festenummer: number,
  undernummer: number,
  bruksenhetsnummer: [
    string
  ],
  objtype: string,
  poststed: string,
  postnummer: string,
  adressetekstutenadressetilleggsnavn: string,
  stedfestingverifisert: boolean,
  representasjonspunkt: {
    epsg: string,
    lat: number,
    lon: number
  },
  oppdateringsdato: Date
}


export interface AddressCompactResponse {
  address_id: number;
  city: string;
  lat: number;
  lng: number;
  municipality_name: string;
  street: string;
  number: string;
  letter: string | null;
  postal_code: string;
  display: string;
}

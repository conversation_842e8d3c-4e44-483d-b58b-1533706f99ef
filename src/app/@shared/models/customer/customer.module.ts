import {AddressCompactResponse} from "../address/address.module";

// export interface BusinessCustomerResponse {
//   company_id: string;
//   customer_id: number;
//   customer_name: string;
//   organisation_number: string;
//   phone: string;
//   email: string;
//   invoice_email: string;
//   address: AddressCompactResponse | null;
//   invoice_address: AddressCompactResponse | null;
//   active: number;
//   accounting_id: string;
//   invoice_send_type_id: number;
//   invoice_send_type_name: number;
//   invoice_due_date_days: number;
//   consolidated_invoice_setting_id: number;
//   consolidated_invoice_setting_name: string;
//   accounting_data: CustomerAccountingResponse | null;
// }

// export interface PrivateCustomerResponse {
//   customer_id: number;
//   first_name: string;
//   last_name: string;
//   name: string;
//   phone: string;
//   email: string;
//   accounting_id: string;
//   user_id: string;
//   address: AddressCompactResponse | null;
//   invoice_address: AddressCompactResponse | null;
//   invoice_send_type_id: number;
//   invoice_send_type_name: number;
//   created_at: Date;
//   accounting_data: CustomerAccountingResponse | null;
// }


// export interface CustomerSearchResponse {
//   customer_id: number;
//   name: string;
//   email: string;
//   phone: string;
//   organisation_number: string | null;
//   user_id: string | null;
//   is_private: number;
//   address: AddressCompactResponse | null;
// }

export interface CustomerAccountingResponse {
  customer_id: string;
  customer_name: string;
  display_name: string;
  organisation_number: string;
  phone: string;
  email: string;
}

export interface CustomerOrderDataResponse {
  total_orders: number,
  revenue: number
}

export interface CustomerResponse {
  affiliate_id: number;
  name: string | null;
  email: string | null;
  phone: string | null;
  user_id: string | null;
  is_private: number;
  organisation_number: string | null;
  company_name: string | null;
  affiliate_contact_id: number | null;
  icon_id: number;
}

export interface CustomerEditableResponse {
  editable: boolean;
}

import {
  OrderLineResponse,
  OrderResponse,
  PaymentRecipientResponse,
  RefundOrderLineResponse
} from "../order/order.module";
import {AffiliateContactResponse} from "../affiliate/affiliate.module";

export interface VatRateResponse {
  vat_rate_id: number;
  vat_rate: number;
}

export interface PaymentScheduleOptionResponse {
  option_id: number;
  name: string;
  subtext: string | null;
  description: string | null;
  auto_send: boolean;
  custom_schedule: boolean;
  consolidated: boolean;
  fixed: boolean;
  requires_schedule: boolean;
}



export interface OrderPaymentResponse {
  payment_id: number;
  payment_number: string;
  order_id: number | null;
  order_number: string | null;
  company_id: string;
  work_order_ids: number[];
  work_order_number: string | null;
  work_order_title: string | null;
  payment_recipient: PaymentRecipientResponse;
  affiliate_contact: AffiliateContactResponse | null;
  payment_method_id: number;
  payment_method_name: string;
  provider_payment_method_name: string | null;
  payment_status_id: number;
  payment_status_name: string;
  payment_reminder_status: number;
  accounting_transfer_at: Date | null;
  accounting_payment_transfer_at: Date | null;
  accounting_transfer_status_id: number;
  accounting_transfer_status_name: string;
  accounting_payment_transfer_status_id: number;
  accounting_payment_transfer_status_name: string;
  payment_sent_at: Date | null;
  auto_send_at: Date | null;
  captured_at: Date | null;
  paid_amount: number;
  invoice_send_type_id: number;
  invoice_send_type_name: string;
  invoice_sent_at: Date | null;
  invoice_due_date_days: number;
  invoice_email: string;
  invoice_reference_text: string;
  invoice_eligible: number;
  invoice_id: string | null;
  actual_invoice_due_date_days: number | null;
  actual_invoice_due_date_text: string | null;
  invoice_date: Date | null;
  total_discount_amount_inc_vat: number;
  total_amount_inc_vat: number;
  total_amount_ex_vat: number;
  refund_amount: number;
  refunded_amount: number;
  payment_name: string;
  comment: string | null;
  refund: boolean;
  refund_reason: string | null;
  parent_payment_id: number | null;
  next_payment: {payment_id: number, auto_send_at: Date} | null;
  previous_payment: {payment_id: number, auto_send_at: Date} | null;
  total_payments: number;
  total_unpaid_sent_payments: number;
  payment_reminders_disabled: boolean;
  payment_reminder_1_scheduled_at: Date | null;
  payment_reminder_2_scheduled_at: Date | null;
  order_lines: OrderLineResponse[];
  refund_order_lines: RefundOrderLineResponse[];
  payment_schedule: PaymentScheduleResponse | null;
  template: boolean;
  subscription_active: boolean;
  payment_reference: string | null;
  is_consolidated_invoice_container: boolean;
  is_parent_consolidated_invoice: boolean;
  consolidation_container_id: number | null;
  consolidated_invoice_payment_id: number | null;
  card_expiration: string | null;
  card_last4: string | null;
  disable_customer_portal: boolean;
  customer_portal_sent_at: Date | null;
  schedule_option: PaymentScheduleOptionResponse | null;
  consolidated_parent_payment: OrderPaymentResponse | null;
  auto_capture: boolean;
  auto_send: boolean;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// export interface OrderPaymentResponse {
//   id: string;
//   url: string;
//   Gui?: {
//     Snippet: string;
//     Layout: string;
//   }
// }


export interface PaymentScheduleResponse {
  payment_schedule_id: number;
  payment_id: number;
  start_date: Date;
  end_date: Date | null;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  active: boolean;
  deactivated_at: Date | null;
  schedule_description: string;
}



export interface PaymentStatusResponse {
  payment_status_id: number;
  payment_status_name: string;
}

export interface PaymentMethodResponse {
  payment_method_id: number;
  payment_method_name: string;
  enabled: number;
  subscription_allowed: number;
}

export interface RivertyPaymentMethodResponse {
  checkoutId: string;
  outcome: string;
  paymentMethods: PaymentMethod[];
}

export interface PaymentMethod {
  type: string;
  title?: string;
  tag?: string;
  logo?: string;
  legalInfo: {
    requiresCustomerConsent: boolean;
    termsAndConditionsUrl: string;
    privacyStatementUrl: string;
    text: string;
  };
  campaigns?: {
    campaignNumber: number;
    campaignType: string;
    activeFrom: string;
    activeTo: string;
    consumerFeeAmount: number;
    paymentTerm: number;
  };
  installment?: {
    basketAmount : number;
    numberOfInstallments : number;
    installmentAmount :number;
    firstInstallmentAmount : number;
    lastInstallmentAmount : number;
    interestRate : number;
    effectiveInterestRate : number;
    effectiveAnnualPercentageRate : number;
    totalInterestAmount : number;
    startupFee : number;
    monthlyFee : number;
    totalAmount : number;
    installmentProfileNumber : number;
    readMore : string;
  };
  account?: {
    profilNo: number;
    installmentAmount: number;
    startupFee: string;
    monthlyFee: string;
    interestRate: number;
  };
}

export interface RivertyPaymentAuthorizeResponse {
  riverty_response:{
    outcome: string;
    customerNumber: string;
    reservationId: string;
    checkoutId: string;
    expirationDate: string;
    riskCheckMessages: RivertyRiskCheckMessages[];
    secureLoginUrl: string;
  },
  order: OrderResponse;
}

export interface RivertyPaymentStatusResponse {
  riverty_response:{
    orderDetails: {
      orderId: string;
      orderNumber: string;
      totalNetAmount: number;
      totalGrossAmount: number;
      currency: string;
      hasSeparateDeliveryAddress: boolean;
      customer: {
        address: {
          street: string;
          streetNumber: string;
          postalCode: string;
          postalPlace: string;
          countryCode: string;
        };
        customerNumber: string;
        firstName: string;
        lastName: string;
        companyName: string;
        email: string;
        phone: string;
        mobilePhone: string;
        birthDate: string;
        customerCategory: string;
      };
      insertedAt: string;
      updatedAt: string;
      orderItems: Array<{
        orderId: string;
        insertedAt: string;
        updatedAt: string;
        productId: string;
        groupId: string;
        description: string;
        netUnitPrice: number;
        grossUnitPrice: number;
        quantity: number;
        vatPercent: number;
        vatAmount: number;
        lineNumber: number;
      }>;
      expirationDate: string;
      status: string;
      displayStatus: string;
    };
    cancellations: Array<{
      cancellationNo: string;
      cancellationAmount: number;
      cancellationItems: any[]; // Adjust type if structure is known
    }>;
    payment: {
      type: string;
      installment: {
        numberOfInstallments: number;
      };
    };
  order: OrderResponse;
}
}

export interface RivertyRiskCheckMessages {
  actionCode: string;
  code: string;
  customerFacingMessage: string;
  message: string;
  type: string;
}

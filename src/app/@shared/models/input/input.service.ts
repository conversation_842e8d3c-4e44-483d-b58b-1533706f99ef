/**
  *
  * Standard Query Parameter Interfaces
  *
  */
import {ProductStageResponse} from "../product/product.module";
import {InternalUserResponse} from "../user/user.module";
import {ResourceResponse} from "../resources/resources.module";
import {Subject} from "rxjs";
import {CustomerResponse} from "../customer/customer.module";
import {AffiliateResponse} from "../affiliate/affiliate.module";

export interface pagination_input {
  page?: number | null;
  limit?: number | null;
  paginate?: number | null;
}

export interface sorting_input {
  order_by?: string | null;
  order_direction?: string | null;
}

export interface search_input {
  search_string?: string | number | null;
  search_string_columns?: string[] | null;
}

export interface date_input {
  date_from?: null | Date | string;
  date_to?: null | Date | string;
}

export interface full_select extends pagination_input, sorting_input, search_input, date_input {}


/**
  *
  * Input Interfaces
   * USM
   * Endpoint Interfaces
  *
  */


export interface USM_USR_0 {
    phone: string;
    password: string;
}

export interface USM_USR_2 {
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
}

export interface USM_USR_3 {}


export interface USM_USR_4 {
  email: string;
}

export interface USM_USR_6 {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
    role_id: number;
    identificator: string | null;
    taskable: number;
    entity_id: string;
    entity_name: string;
    entity_email: string;
    entity_phone: string;
}

export interface USM_USR_7 {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
}

export interface USM_USR_8 {
  phone: string;
  otp: string;
}

export interface USM_USR_9 {
  phone: string;
}

export interface USM_USR_10 extends full_select {
  entity_id: string;
}

export interface USM_USR_13 {
  phone: number;
}

export interface USM_USR_14 {
  reset_token: string;
  new_password: string;
}

export interface USM_USR_16 {
  image: File;
}

export interface USM_USR_17 {
  entity_id: string;
  user_id: string;
  image: File;
}

export interface _USM_USR_17 extends Omit<USM_USR_17, 'entity_id'> {}

export interface USM_USR_18 {
  old_password: string;
  new_password: string;
}

export interface USM_USR_19 {
  invitation_token: string;
  password: string;
  email: string;
}

export interface USM_USR_20 {
  entity_id: string;
  email?: string | null;
  phone?: string | null;
}

export interface USM_USR_21 {}

export interface USM_USR_22 {
  language: string;
  calendar_view_mode: string;
  calendar_show_weekends: number;
  calendar_view_port_from: string;
  calendar_view_port_to: string;
  calendar_order_statuses: number[];
}

export interface USM_USR_24 {
  user_id: string;
  entity_id: string;
  entity_name: string;
  entity_email: string;
  entity_phone: string;
}

export interface USM_USR_25 {
  invitation_token: string;
}

export interface USM_ENT_0 extends full_select {
  entity_id: string;
  taskable?: number | null;
  role_id?: number | null;
  include_customers?: number | null;
  user_id?: string;
}

export interface _USM_ENT_0 extends Omit<USM_ENT_0, 'entity_id'>, full_select {} {}

export interface USM_ENT_1 extends full_select {
  entity_specific_only?: number | null;
  entity_id: string;
}

export interface USM_ENT_2 {
  user_id: string;
  entity_id: string;
  role_id: number;
  identificator: string;
  taskable: number;
}

export interface USM_ENT_3 extends full_select {
  entity_tag?: string;
  role_id_list?: number[];
}

/**
  *
  * Input Interfaces
   * CRM
   * Nested Interfaces
  *
  */

export interface AddressInput {
    address_id: number | null;
    external_id?: string | null;
    city?: string | null;
    lat?: number | null;
    lng?: number | null;
    municipality_id?: number | null;
    municipality_name?: string | null;
    street_id?: string | null;
    street?: string | null;
    number?: string | null;
    letter?: string | null;
    postal_code?: string | null;
    area_id?: number | null;
    area?: string | null;
    display?: string | null;
    cadastre?: CadastreInput | null;
    organisation_number?: string | null;
    condominium_share?: string | null;
    section_id?: string | null;
    floor?: number | null;
    radon_level?: number | null;
    energy_score?: null | string;
    heating_score?: null | number;
    primary_area?: number | null;
    livable_area?: number | null;
    plot_area?: number | null;
    number_of_floors?: number | null;
    number_of_rooms?: number | null;
    number_of_bathrooms?: number | null;
    number_of_bedrooms?: number | null;
    number_of_units_on_address?: number | null;
    has_elevator?: number | null;
    has_parking?: number | null;
    has_garage?: number | null;
    property_type_id?: number;
    property_type_name?: string,
    build_year?: number | null;
    ownership_type_id?: number;
    homeowners_name?: string[] | null;
    homeowners_fraction?: number[] | null;
    shed_area?: number | null;
}

export interface UnitDetails {
    address_id: number | null;
    external_id: string | null;
    city: string | null;
    lat: number | null;
    lng: number | null;
    municipality_id: number | null;
    municipality_name: string | null;
    street_id: string | null;
    street: string | null;
    number: string | null;
    letter: string | null;
    postal_code: string | null;
    area_id: number | null;
    area: string | null;
    display?: string | null;
    cadastre: CadastreInput | null;
    organisation_number: string | null;
    condominium_share: string | null;
    section_id: string | null;
    floor: number | null;
    radon_level: number | null;
    energy_score: null | string;
    heating_score: null | number;
    primary_area: number | null;
    livable_area: number | null;
    plot_area: number | null;
    number_of_floors: number | null;
    number_of_rooms: number | null;
    number_of_bathrooms: number | null;
    number_of_bedrooms: number | null;
    number_of_units_on_address: number | null;
    has_elevator: number | null;
    has_parking: number | null;
    has_garage: number | null;
    property_type_id?: number;
    property_type_name?: string,
    build_year: number | null;
    ownership_type_id?: number;
    homeowners_name: string[] | null;
    homeowners_fraction: number[] | null;
    shed_area: number | null;
    cadastre_section_id?: string | null;
    custom_tag?: string | null;
}

interface CadastreInput {
    kommunenr: number;
    gardsnr: number;
    bruksnr: number;
    festenr: number;
    seksjonsnr: number;
}

export interface OrderLineStageInput {
  stage_id: number;
  address: UnitDetails | null;
  sets_quantity: number;
}

export interface EmbedOrderLineInput {
  product_id: number;
  quantity: number;
  is_taskable: number;
  stages: OrderLineStageInput[];
  execution_at: Date;
  price_rule_ids: number[];
}

export interface OrderLineInput {
  product_id: number | null;
  custom_product_name?: string | null;
  price_inc_vat?: number | null;
  vat_rate_id?: number | null;
  quantity: number;
  is_taskable: number;
  execution_at: Date | null;
  execution_to: Date | null;
  price_rule_ids: number[];
  stages: OrderLineStageInput[];
  user_ids?: string[];
  resource_ids?: number[];
}

export interface OrderLineAsJobInput {
  order_line_id: number;
  execution_at: Date;
  execution_to: Date;
  lane: number;
  crew: string[];
}

/**
  *
  * Input Interfaces
   * CRM
   * Endpoint Interfaces
  *
  */

export interface CRM_AFF_0 {
  company_id: string;
  organisation_number: string;
  company_name: string;
  phone: string;
  email: string;
  address: AddressInput;
  is_customer?: number;
  is_partner?: number;
  is_subcontractor?: number;
  accounting_id?: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  consolidated_invoice_setting_id?: number;
  invoice_email?: string;
  comment?: string;
}

export interface _CRM_AFF_0 extends Omit<CRM_AFF_0, 'company_id'> {}

export interface CRM_AFF_1 extends full_select {
  company_id: string;
  customers_only?: number;
  partners_only?: number;
  subcontractors_only?: number;
  private_only?: number;
  business_only?: number;
}

export interface _CRM_AFF_1 extends Omit<CRM_AFF_1, 'company_id'> {}

export interface CRM_AFF_2 {
  company_id: string;
  affiliate_id: number;
  is_partner?: number;
  is_customer?: number;
  is_subcontractor?: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  accounting_id?: string | null;
  preferred_payment_method_id?: number;
  _phone?: string | null;
  _email?: string | null;
  _consolidated_invoice_setting_id?: number;
  _invoice_email?: string;
  hide_payment_data?: number;
  comment?: string;
  address?: AddressInput;
  disable_sms?: number;
  disable_email?: number;
}

export interface _CRM_AFF_2 extends Omit<CRM_AFF_2, 'company_id'> {}

export interface CRM_AFF_3 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_4 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_5 {
  company_id: string;
  affiliate_id: number;
  user_id?: string | null;
  email?: string | null;
  phone?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  role_description?: string | null;
}

export interface _CRM_AFF_5 extends Omit<CRM_AFF_5, 'company_id'> {}

export interface CRM_AFF_6 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_AFF_7 {
  company_id: string;
  affiliate_contact_id: number;
  first_name?: string;
  last_name?: string;
  role_description?: string;
  comment?: string;
  _email?: string;
  _phone?: string;
}

export interface _CRM_AFF_7 extends Omit<CRM_AFF_7, 'company_id'> {}

export interface CRM_AFF_8 {
  company_id: string;
  affiliate_contact_id: number;
}

export interface CRM_AFF_9 {
  company_id: string;
  search_term: string | null;
  customers_only?: number;
  partners_only?: number;
  subcontractors_only?: number;
  private_only?: number;
  business_only?: number;
}

export interface CRM_EMP_0 {
  company_id: string;
  role_id: number;
  invitee: {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
  } | null,
  taskable : number;
}

export interface _CRM_EMP_0 extends Omit<CRM_EMP_0, 'company_id'> {}

export interface CRM_EMP_1 {
  company_id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  identificator: string | null;
  role_id: number;
  taskable: number;
}

export interface _CRM_EMP_1 extends Omit<CRM_EMP_1, 'company_id'> {}

export interface CRM_EMP_2 {
  company_id: string;
  user_id: string;
}

export interface CRM_EMP_4 {
  company_id: string;
  user_id: string;
}

export interface _CRM_EMP_4 extends Omit<CRM_EMP_4, 'company_id'> {}

export interface CRM_EMP_5 {
  user_id: string;
  company_id: string;
  add_product: number;
  edit_quantity: number;
  make_external_notes: number;
  make_external_reports: number;
  send_to_payment: number;
  edit_discount: number;
  view_prices: number;
  receive_order_accepted_notification: number;
  receive_order_finished_notification: number;
  payment_page_access: number;
  set_payment_to_external: number;
  receive_accounting_integration_failed_notification: number;
  view_all_events_as_crew?: number;
  view_all_orders_as_crew?: number;
  unpaid_order_notification: number;
  failed_schedule_order_creation_notification: number;
  receive_upcoming_order_not_accepted_notification: number;
  receive_sub_contractor_order_notification: number;
  checkin_geo_lock: number;
  receive_order_rating_notification: number;
  receive_new_embed_order_notification: number;
}

export interface _CRM_EMP_5 extends Omit<CRM_EMP_5, 'company_id'> {}

export interface CRM_EMP_6 {
  company_id: string;
}

export interface CRM_EMP_7 {
  company_id: string;
  user_id: string;
}

export interface _CRM_EMP_7 extends Omit<CRM_EMP_7, 'company_id'> {}

export interface CRM_EMP_8 {
  company_id: string;
  user_id: string;
  property_id: number;
  is_custom: number;
}

export interface _CRM_EMP_8 extends Omit<CRM_EMP_8, 'company_id'> {}

export interface CRM_EMP_9 {
  company_id: string;
  user_id: string;
  property_id: number;
  is_custom: number;
}

export interface _CRM_EMP_9 extends Omit<CRM_EMP_9, 'company_id'> {}

export interface CRM_EMP_10 {
  company_id: string;
  property_type_name: string;
  multi_select: number;
}

export interface _CRM_EMP_10 extends Omit<CRM_EMP_10, 'company_id'> {}

export interface CRM_EMP_11 {
  company_id: string;
  property_type_id: number;
  property_type_name: string;
  multi_select: number;
}

export interface _CRM_EMP_11 extends Omit<CRM_EMP_11, 'company_id'> {}

export interface CRM_EMP_12 {
  company_id: string;
  global_only?: number | null;
  custom_only?: number | null;
}

export interface _CRM_EMP_12 extends Omit<CRM_EMP_12, 'company_id'> {}

export interface CRM_EMP_13 {
  company_id: string;
  property_type_id: number;
}

export interface CRM_EMP_14 {
  company_id: string;
}

export interface CRM_EMP_15 {
  company_id: string;
  property_id: number;
}

export interface OrderLineAddressStage extends OrderLineStageInput, ProductStageResponse {
  childStages: OrderLineAddressStage[];
  tempId: number;
  setLater?: boolean;
  clearObservable: Subject<void>;
}

export interface TempOrderLineStage extends OrderLineStageInput {
  tempId: number;
  stage_name?: string;
  setLater?: boolean;
}

export interface TempOrderLineInput extends OrderLineInput {
  tempId: number;
  product_name: string | null;
  custom_product_name?: string | null;
  icon_id: number | null;
  price_inc_vat: number;
  stock_quantity: number | null;
  unit_abbreviation: string | null;
  productStages: ProductStageResponse[];
  tempOrderLineStages: TempOrderLineStage[];
  addressStages: OrderLineAddressStage[];
  serviceSelected?: boolean;
  user_ids: string[];
  resource_ids: number[];
}


export interface CRM_ORD_0 {
  company_id: string;
  order_lines: OrderLineInput[];
  payment_recipient_id: number;
  service_recipient_id: number | null;
  affiliate_contact_id: number | null;
  order_status_id?: number | null;
  confirmation_status_id?: number | null;
  payment_method_id?: number | null;
  partner_id?: number | null;
  partner_contact_id?: number | null;
  use_duration_calculation?: number;
  notes: {note_text: string; internal: number;}[]
  invoice_reference_text?: string | null;
  comment?: string | null;
  specifications?: [{
    choice_id: number;
    value: number;
    input: string
  }],
  order_schedule?: NestedOrderScheduleInput;
}

export interface _CRM_ORD_0 extends Omit<CRM_ORD_0, 'company_id'> {}

export interface NestedOrderScheduleInput {
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  use_date: number;
  date: number | null;
  nth_weekday: number | null;
  orders_in_advance: number;
  enable_customer_notifications: number;
  order_schedule_order_lines: NestedOrderScheduleOrderLine[];
  fixed_payment: NestedFixedPayment | null;
}

export interface NestedFixedPayment {
  date: number,
  every: number,
  total_price_inc_vat: number,
  vat_rate_id: number,
  service_description: string,
}

export interface TempTaskableOrderLine extends TempOrderLineInput {
  duration_hours: number | null;
  serviceSelected: boolean;
  customerSelected: boolean;
  unit_id?: number;
  execution_at: Date;
  execution_to: Date;
}

export interface TempOrder extends _CRM_ORD_0 {
  taskableOrderLine: TempTaskableOrderLine;
  order_lines: TempOrderLineInput[],
  tempAddress: UnitDetails | null;
  paymentRecipient?: CustomerResponse | null;
  serviceRecipient?: CustomerResponse | null;
  singleAffiliate: boolean;
  paymentRecipientSelected: boolean;
  serviceRecipientSelected: boolean;
  partnerSelected: boolean;
  partner?: AffiliateResponse | null;
  users: InternalUserResponse[];
  resources: ResourceResponse[];
  useDurationCalculation: boolean;
  durationCalculationProductSelected: boolean;
  creationReady: boolean;
  showOrderNotes: boolean;
  multiDay: boolean;
  creationReadyReasons: string[];
  errors: {
    addressError: boolean;
    timeError: boolean;
    dateError: boolean;
  };
  enableSchedule: boolean;
  scheduleInput: NestedOrderScheduleInput;
  manualHours: boolean;
}

export interface CRM_ORD_1 {
  company_id: string;
  payment_recipient_id: number;
  order_lines: EmbedOrderLineInput[];
  specifications?: [{
    choice_id: number;
    value: number;
    input: string
  }]
}

export interface CRM_ORD_2 extends full_select {
  company_id: string;
  include_order_lines?: number | null;
  partner_id?: number | null;
  partner_contact_id?: number | null;
  order_status_ids?: number[] | null;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
  captured_at_from?: Date | null;
  captured_at_to?: Date | null;
  confirmation_status_id?: number | null;
  payment_status_ids?: number[] | null;
  archived?: number | null;
  not_posted_in_accounting?: number | null;
  order_schedule_id?: number | null;
  payment_recipient_id?: number | null;
  service_recipient_id?: number | null;
  quote_sent?: number | null;
}

export interface _CRM_ORD_2 extends Omit<CRM_ORD_2, 'company_id'> {}

export interface CRM_ORD_3 extends pagination_input, sorting_input, search_input {
  company_id: string;
  is_taskable?: number | null;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
}

export interface CRM_ORD_4 {
  company_id: string;
  execution_date_from?: Date | null;
  execution_date_to?: Date | null;
}


export interface CRM_ORD_5 extends date_input {
  company_id: string;
}

export interface CRM_ORD_6 extends date_input {
  company_id: string;
}

export interface CRM_ORD_7 extends date_input {
  company_id: string;
}

export interface CRM_ORD_8 extends date_input {
  company_id: string;
}

export interface CRM_ORD_9 {
  company_id: string;
  order_lines: OrderLineAsJobInput[];
}

export interface _CRM_ORD_9 extends Omit<CRM_ORD_9, 'company_id'> {}

export interface CRM_ORD_10 {
  company_id: string;
  order_id?: number;
  order_line_id?: number;
}

export interface CRM_ORD_11 {
  company_id: string;
  execution_date_from: Date;
  execution_date_to: Date;
  order_status_ids: number[];
}

export interface CRM_ORD_12 {
  company_id: string;
  order_id: number;
  order_line: OrderLineInput;
}

export interface CRM_ORD_13 {
  company_id: string;
  order_id: number;
  discount_amount: number | null;
  discount_percentage: number | null;
  discount_reason: string;
}

export interface CRM_ORD_15 {
  company_id: string;
  order_line_id: number;
}

export interface CRM_ORD_16 extends pagination_input, sorting_input, search_input {
  execution_date_from: Date;
  execution_date_to: Date;
  order_status_ids: number[];
}

export interface CRM_ORD_17 {
  company_id: string;
  order_line_id: number;
  price_modifier_ids: number[];
  execution_at: Date;
  execution_to: Date;
  quantity: number;
}

export interface CRM_ORD_19 {
  company_id: string;
  order_id: number;
  note_text: string;
  internal: number;
}

export interface CRM_ORD_21 {
  company_id: string;
  order_note_id: number;
}

export interface CRM_ORD_22 {
  company_id: string;
  order_note_id: number;
  note_text: string;
}

export interface CRM_ORD_23 {
  order_id: number;
  note_text: string;
}

export interface CRM_ORD_25 {
  order_note_id: number;
}

export interface CRM_ORD_26 {
  order_note_id: number;
  note_text: string;
}

export interface CRM_ORD_27 {
  company_id: string;
  order_id: number;
  sms: number;
  email: number;
}

export interface CRM_ORD_28 {
  order_id: number;
  order_token: string;
}

export interface CRM_ORD_29 extends pagination_input, sorting_input, search_input {
  order_id?: number | null;
  order_schedule_id?: number | null;
  execution_date_to?: Date,
  execution_date_from?: Date
  order_status_ids?: number[];
}

export interface CRM_ORD_31 {
  company_id: string;
  order_line_id: number;
  price_rule_ids?: number[];
  execution_at?: Date;
  execution_to?: Date;
  quantity?: number;
  comment?: string | null;
}

export interface _CRM_ORD_31 extends Omit<CRM_ORD_31, 'company_id'> {}

export interface CRM_ORD_33 {
  order_id: number;
}

export interface CRM_ORD_34 {
  order_id: number;
  order_token: string;
}

export interface CRM_ORD_35 {
  company_id: string;
  execution_date_from: Date;
  execution_date_to: Date;
}

export interface CRM_ORD_36 {
  company_id: string;
  order_line_id: number;
  cargo: [
    {
      cargo_type_id: number;
      quantity: number;
    }
    ];
}

export interface CRM_ORD_37 {
  order_id: number;
  rating: number;
  comment: string;
}


export interface CRM_ORD_38 {
  order_id?: number;
  order_token: string;
  order_schedule_id?: number;
  service_recipient?: number;
}

export interface CRM_ORD_39 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_40 {
  order_id: number;
}

export interface CRM_ORD_41 {
  order_line_id: number;
  quantity: number;
}

export interface CRM_ORD_42 {
  order_id: number;
  product_id: number;
  quantity: number;
}

export interface CRM_ORD_43 {
  order_id: number;
}

export interface CRM_ORD_45 {
  order_line_id: number;
  cargo: [
    {
      cargo_type_id: number;
      quantity: number;
    }
  ]
}

export interface CRM_ORD_46 {
  order_line_id: number;
}

export interface CRM_ORD_47 {
  order_id: number;
  order_line: {
    order_line_id: number;
    product_id: number;
    quantity: number;
  }
}

export interface CRM_ORD_48 {
  order_id: number;
  company_id: string;
}

export interface CRM_ORD_49 {
  order_id: number;
  company_id: string;
}

export interface _CRM_ORD_49 extends Omit<CRM_ORD_49, 'company_id'> {}

export interface CRM_ORD_53 {
  company_id: string;
  order_line_stage_id: number;
  address: UnitDetails;
}

export interface _CRM_ORD_53 extends Omit<CRM_ORD_53, 'company_id'> {}


export interface CRM_ORD_55 {
  company_id: string;
  order_id: number;
  order_status_id: number;
  notify_customer?: number | null
}

export interface _CRM_ORD_55 extends Omit<CRM_ORD_55, 'company_id'> {}

export interface CRM_ORD_56 {
  company_id: string;
  order_id: number;
  partner_id: number | null; // For now, this should be the same as paper_company_id (PCO-X)
  partner_contact_id: number | null; // For now, this should be the same as paper_employee_id (PEM-X)
}

export interface _CRM_ORD_56 extends Omit<CRM_ORD_56, 'company_id'> {}

// export interface CRM_ORD_57 {
//   company_id: string;
//   order_id: number;
//   invoice_recipient_type_id: number; // Either 0 (Customer) or 1 (Partner)
// }

export interface CRM_ORD_60 {
  company_id: string;
  order_id: number;
  product_name: string;
  price_inc_vat: number;
  vat_rate_id: number;
}

export interface _CRM_ORD_60 extends Omit<CRM_ORD_60, 'company_id'> {}

export interface CRM_ORD_61 {
  company_id: string,
  order_line_id: number,
  stage_id: number,
  address: UnitDetails
}

export interface _CRM_ORD_61 extends Omit<CRM_ORD_61, 'company_id'> {}

export interface CRM_ORD_64 {
  company_id: string,
  order_line_stage_id: number,
}

export interface CRM_ORD_65 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_66 {
  company_id: string;
  order_line_id: number;
  user_ids: string[];
}

export interface _CRM_ORD_66 extends Omit<CRM_ORD_66, 'company_id'> {}

export interface CRM_ORD_67 {
  company_id: string;
  order_line_id: number;
  resource_ids: number[];
}

export interface _CRM_ORD_67 extends Omit<CRM_ORD_67, 'company_id'> {}

export interface CRM_ORD_68 {
  company_id: string;
  order_id: number;
  archived: number;
}

export interface CRM_ORD_69 {
  company_id: string;
  order_line_id: number;
  total_price_inc_vat: number;
}

export interface CRM_ORD_71 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_72 {
  company_id: string;
  order_id: number;
  phone: string | null;
}

export interface _CRM_ORD_72 extends Omit<CRM_ORD_72, 'company_id'> {}

export interface CRM_ORD_73 {
  company_id: string;
  order_id: number;
  order_line_id: number;
  discount_amount: number;
}

export interface _CRM_ORD_73 extends Omit<CRM_ORD_73, 'company_id'> {}

export interface CRM_ORD_74 {
  company_id: string;
  order_id: number;
  invoice_send_type_id: number;
  invoice_due_date_days: number;
  invoice_reference_text: string;
  invoice_email?: string | null;
}

export interface _CRM_ORD_74 extends Omit<CRM_ORD_74, 'company_id'> {}

export interface CRM_ORD_75 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_76 {
  company_id: string;
  start_time: Date;
  end_time: Date;
  start_date: Date;
  status?: number;
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  orders_in_advance: number;
  payment_recipient_id: number;
  service_recipient_id?: number | null;
  partner_id?: number;
  partner_contact_id?: number;
  payment_method_id?: number;
  enable_customer_notifications: number;
  order_schedule_order_lines: NestedOrderScheduleOrderLine[];
  invoice_reference_text?: string | null;
  fixed_payment: NestedFixedPayment | null;
}

export interface _CRM_ORD_76 extends Omit<CRM_ORD_76, 'company_id'> {}

export interface CRM_ORD_77 {
  company_id: string;
  order_schedule_id: number;
  cancel_future_orders: number;
}

export interface CRM_ORD_78 {
  company_id: string;
  order_schedule_id: number;
}

export interface CRM_ORD_79 {
  company_id: string;
  order_schedule_id: number;
  start_time?: Date;
  end_time?: Date;
  status?: number;
  schedule_repeat_type_id?: number;
  weekdays?: number[];
  every?: number;
  date?: number | null;
  nth_weekday?: number | null;
  orders_in_advance?: number;
  enable_customer_notifications?: number;
  partner_id?: number;
  partner_contact_id?: number;
  payment_method_id?: number;
  cancel_future_orders?: number;
  user_ids?: string[];
  resource_ids?: number[];
}

export interface _CRM_ORD_79 extends Omit<CRM_ORD_79, 'company_id'> {}

export interface CRM_ORD_80 extends pagination_input, sorting_input, search_input {
  company_id: string;
}

export interface _CRM_ORD_80 extends Omit<CRM_ORD_80, 'company_id'> {}

export interface CRM_ORD_81 {
  company_id: string;
  order_schedule_id: number;
  product_id?: number;
  quantity: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids: number[];
}

export interface _CRM_ORD_81 extends Omit<CRM_ORD_81, 'company_id'> {}

export interface CRM_ORD_82 {
  company_id: string;
  order_schedule_id: number;
  order_schedule_order_line_id: number;
  product_id?: number | null;
  quantity: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids?: number[];
  stages?: OrderLineStageInput[];
  user_ids?: string[];
  resource_ids?: number[];
}

export interface _CRM_ORD_82 extends Omit<CRM_ORD_82, 'company_id'> {}

export interface CRM_ORD_83 {
  company_id: string;
  order_schedule_id: number;
  order_schedule_order_line_id: number;
}

export interface _CRM_ORD_83 extends Omit<CRM_ORD_83, 'company_id'> {}

export interface CRM_ORD_85 {
  company_id: string;
  start_time: Date;
  end_time: Date;
  start_date: Date;
  schedule_repeat_type_id: number;
  weekdays: number[];
  every: number;
  date: number | null;
  nth_weekday: number | null;
  orders_in_advance: number;
}

export interface _CRM_ORD_85 extends Omit<CRM_ORD_85, 'company_id'> {}

export interface CRM_ORD_86 {
  company_id: string;
  order_id: number;
  payment_recipient_id: number;
  affiliate_contact_id?: number | null;
}

export interface _CRM_ORD_87 extends Omit<CRM_ORD_87, 'company_id'> {}

export interface CRM_ORD_87 {
  company_id: string;
  order_id: number;
  log_type_ids?: number[];
}


export interface CRM_ORD_89 extends pagination_input, sorting_input, search_input{
  order_schedule_id?: number;
}

export interface CRM_ORD_90 {
  company_id: string;
  order_schedule_id: number;
  for_customer: number;
}

export interface CRM_ORD_91 {
  company_id: string;
  order_schedule_id: number;
  sms: number;
  email: number;
}

export interface CRM_ORD_92 {
  order_schedule_id: number;
  payment_method_id: number;
}

export interface CRM_ORD_93 {
  company_id: string;
  order_schedule_id: number;
}

export interface CRM_ORD_95 {
  company_id: string;
  order_schedule_id: number;
  active: number;
  send_customer_confirmation: number;
}

export interface CRM_ORD_96 {
  company_id: string;
  order_schedule_id: number;
  payment_method_id: number;
}

export interface CRM_ORD_97 {
  company_id: string;
  order_id: number;
  enable_customer_notifications?: number;
  comment?: string | null;
}

export interface _CRM_ORD_97 extends Omit<CRM_ORD_97, 'company_id'> {}

export interface CRM_ORD_98 {
  company_id: string;
  order_id: number;
  attachment: File;
  work_order_id?: number;
}

export interface CRM_ORD_99 {
  company_id: string;
  attachment_id: number;
}

export interface CRM_ORD_100 {
  company_id: string;
  order_id: number;
  attachment_id: number;
}

export interface CRM_ORD_101 {
  company_id: string;
  order_id: number;
  affiliate_ids: number[];
  accept: number;
}

export interface _CRM_ORD_101 extends Omit<CRM_ORD_101, 'company_id'> {}

export interface CRM_ORD_102 {
  company_id: string;
  order_id: number;
  affiliate_id: number;
}

export interface CRM_ORD_103 {
  company_id: string;
  order_id: number;
  affiliate_id: number;
  affiliate_contact_id: number | null;
}

export interface CRM_ORD_104 {
  company_id: string;
  order_id: number;
  affiliate_id: number;
}

export interface CRM_ORD_107 {
  company_id: string;
  order_id: number;
}

export interface CRM_ORD_152 {
  company_id: string;
  order_id: number;
  order_choice_id: number
  value: number;
  input?: string;
}
export interface _CRM_ORD_152 extends Omit<CRM_ORD_152, 'company_id'> {}

export interface _CRM_ORD_86 extends Omit<CRM_ORD_86, 'company_id'> {}

export interface CRM_ORD_153 {
  order_id: number;
  work_order_id: number;
  work_order_status_ids?: number[]
  parent_work_order_id?: number;
  no_children?: boolean;
}

export interface CRM_ORD_161 {
  order_id: number;
  template_filter?: boolean;
  limit?: number;
  parent_work_order_id?: number;
}

export interface CRM_ORD_173 {
  order_id: number;
  attachment: File;
}

export interface CRM_ORD_174 {
  order_id: number;
  attachment_id: number;
}

export interface CRM_ORD_175 {
  order_id: number;
  attachment_id: number;
}

export interface CRM_ORD_180 {
  work_order_id: number;
  description: string;
  incident_type_id: number;
}

export interface CRM_ORD_181 {
  incident_id: number;
  image: File;
}

export interface CRM_ORD_182 {
  order_id: number;
}


export interface NestedOrderScheduleOrderLine {
  product_id?: number | null;
  quantity: number;
  is_taskable: number;
  total_price_inc_vat?: number | null;
  product_price_inc_vat?: number | null;
  total_price_override?: number | null;
  custom_vat_rate_id?: number | null;
  custom_product_name?: number | null;
  price_rule_ids: number[];
  stages: OrderLineStageInput[];
  user_ids: string[];
  resource_ids: number[];
}


export interface CRM_EMB_0 {
  company_id: string;
}

export interface CRM_EMB_1 {
  company_id: string;
}

export interface CRM_EMB_2 {
  embed_id: number;
  company_id: string;
  embed_name?: string;
  primary_color?: string;
  button_background_color?: string;
  button_border_color?: string;
  button_hover_background_color?: string;
  button_hover_border_color?: string;
  button_border_radius?: number;
  button_text_color?: string;
  button_hover_text_color?: string;
  button_text?: string;
  show_prices_for_customers?: number;
  require_otp?: number;
  show_upsell?: number;
  google_tracking_id?: string;
  google_conversion_send_to?: string;
}

export interface _CRM_EMB_2 extends Omit<CRM_EMB_2, 'company_id'> {}

export interface CRM_EMB_3 {
  company_id: string;
  embed_id: number;
}

export  interface _CRM_EMB_3 extends Omit<CRM_EMB_3, 'company_id'> {}

export interface CRM_EMB_4 {
  company_id: string;
  embed_id: number;
}

export interface _CRM_EMB_4 extends Omit<CRM_EMB_4, 'company_id'> {}

export interface CRM_EMB_5 {
  company_id: string;
  embed_id: number;
  product_id: number;
}

export interface _CRM_EMB_5 extends Omit<CRM_EMB_5, 'company_id'> {}

export interface CRM_EMB_6 {
  company_id: string;
  embed_id: number;
  embed_product_id: number;
  allow_recurring?: number;
  index?: number;
}

export interface _CRM_EMB_6 extends Omit<CRM_EMB_6, 'company_id'> {}

export interface CRM_EMB_7 {
  company_id: string;
  embed_id: number;
  embed_product_id: number;
}

export interface _CRM_EMB_7 extends Omit<CRM_EMB_7, 'company_id'> {}

export interface NestedEmbedSpecificationChoiceInput {
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface CRM_EMB_8 {
  company_id: string;
  embed_product_id: number;
  specification_text: string;
  radio_selection: number;
  required: number;
  index: number;
  choices: NestedEmbedSpecificationChoiceInput[]
}

export interface _CRM_EMB_8 extends Omit<CRM_EMB_8, 'company_id'> {}

export interface CRM_EMB_9 {
  company_id: string;
  embed_product_id: number;
  specification_id: number;
  specification_text?: string;
  required?: number;
  radio_selection?: number;
  index?: number;
  choices?: NestedEmbedSpecificationChoiceInput[]
}

export interface _CRM_EMB_9 extends Omit<CRM_EMB_9, 'company_id'> {}

export interface CRM_EMB_10 {
  company_id: string;
  specification_id: number;
}

export interface _CRM_EMB_10 extends Omit<CRM_EMB_10, 'company_id'> {}

export interface CRM_EMB_11 {
  company_id: string;
  specification_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_EMB_11 extends Omit<CRM_EMB_11, 'company_id'> {}

export interface CRM_EMB_12 {
  company_id: string;
  specification_id: number;
  choice_id: number;
  choice_name?: string;
  choice_ghost_text?: string | null;
  index?: number;
}

export interface _CRM_EMB_12 extends Omit<CRM_EMB_12, 'company_id'> {}

export interface CRM_EMB_13 {
  company_id: string;
  specification_id: number;
  choice_id: number;
}

export interface _CRM_EMB_13 extends Omit<CRM_EMB_13, 'company_id'> {}


export interface CompanyInviteeInput {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
}

export interface CRM_COY_0 {
  company_name: string;
  organisation_number: string;
  address: UnitDetails;
  phone: string;
  email: string;
  invitee?: CompanyInviteeInput | null;
}

export interface CRM_COY_1 {
  company_id: string;
}

export interface CRM_COY_2 extends full_select {
  company_type_id?: number | null;
}

export interface CRM_COY_3 extends full_select {
  company_id: string;
  company_name: string;
  address: UnitDetails | null;
  phone: string;
  email: string;
}

export interface _CRM_COY_3 extends Omit<CRM_COY_3, 'company_id'> {}

export interface CRM_COY_4 {
  company_id: string;
  start_time?: Date;
  end_time?: Date;
  open_from?: string;
  open_to?: string;
  days?: {
    '0': 1 | 0;
    '1': 1 | 0;
    '2': 1 | 0;
    '3': 1 | 0;
    '4': 1 | 0;
    '5': 1 | 0;
    '6': 1 | 0;
  }
}

export interface _CRM_COY_4 extends Omit<CRM_COY_4, 'company_id'> {}


export interface CRM_COY_5 {
  company_id: string;
}

export interface CRM_COY_6 {
  company_id: string;
  threshold_1: number;
  threshold_2: number;
  // open_from: string;
  // open_to: string;
  // days: {
  //   '0': 1 | 0;
  //   '1': 1 | 0;
  //   '2': 1 | 0;
  //   '3': 1 | 0;
  //   '4': 1 | 0;
  //   '5': 1 | 0;
  //   '6': 1 | 0;
  // }
}

export interface _CRM_COY_6 extends Omit<CRM_COY_6, 'company_id'> {}

export interface CRM_COY_7 {
  company_id: string;
}

export interface CRM_COY_8 {
  company_id: string;
  image: File;
}

export interface CRM_COY_9 {
  company_id: string;
  toc: string;
}

export interface CRM_COY_10 {
  company_id: string;
}

export interface notification_type_setting {
  notification_type_id: number;
  time_delta_hours: number;
  enabled: number;
}

export interface CRM_COY_11 {
  company_id: string;
  settings: notification_type_setting[]
}

export interface _CRM_COY_11 extends Omit<CRM_COY_11, 'company_id'> {}

export interface CRM_COY_12 {
  company_id: string;
}

export interface CRM_COY_13 {
  company_id: string;
}

export interface CRM_COY_14 {
  company_id: string;
}

export interface CRM_COY_15 {
  company_id: string;
  payment_method_id: number;
  credentials: {
    [key: string]: string | number;
  }
}

export interface CRM_COY_16 {
  company_id: string;
}

export interface CRM_COY_17 {
  company_id: string;
  payment_method_id: number;
  credentials: {
    [key: string]: string | number;
  }
}

export interface _CRM_COY_17 extends Omit<CRM_COY_17, 'company_id'> {}


export interface CRM_COY_18 {
  company_id: string;
}

export interface CRM_COY_19 {
  company_id: string;
}

export interface CRM_COY_20 {
  company_id: string;
}

export interface CRM_COY_21 {
  company_id: string;
  primary_color_hex: string;
  secondary_color_hex: string;
  content: {[key: string]: string | number | boolean | null};
}

export interface CRM_COY_22 {
  company_id: string;
  order_line_quantity_calculation_resolution?: number;
  company_order_number_prefix?: string;
  company_order_number_starting_number?: number;
  include_vat_in_price_business?: number;
  include_vat_in_price_private?: number;
  transfer_external_orders_to_accounting?: number;
  transfer_free_orders_to_accounting?: number;
  archive_order_when_finished?: number;
  calendar_start_hour?: number;
  calendar_end_hour?: number;
  auto_synchronise_accounting?: number;
  auto_send_invoice?: number;
  mark_external_orders_as_paid_automatically?: number;
  sms_name?: string;
  standard_property_type_id_for_unspecified_properties?: number;
  include_transport_in_duration_calculation?: number;
  geo_lock_range?: number | null;
  default_business_customers_due_date?: number;
  default_private_customers_due_date?: number;
  manual_order_confirmation_email?: number;
  split_interday_orders_in_multiple_events?: number;
}

export interface _CRM_COY_22 extends Omit<CRM_COY_22, 'company_id'> {}

export interface CRM_COY_23 {
  company_id: string;
}

export interface CRM_COY_25 {
  company_id: string;
}

export interface CRM_COY_26 {
  company_id: string;
  payment_method_id: number;
  active?: number;
  clear?: number;
  private_customer_available?: number;
  business_customer_available?: number;
  subscription_allowed?: number;
}

export interface _CRM_COY_26 extends Omit<CRM_COY_26, 'company_id'> {}

export interface CRM_COY_27 {
  company_id: string;
}

export interface CRM_COY_31 {
  company_id: string;
}

export interface CRM_COY_32 {
  company_id: string;
  property_type_id: number;
  livable_area?: number;
  floor?: number | null;
  number_of_floors?: number | null;
  number_of_rooms?: number;
  number_of_bathrooms?: number;
  shed_area?: number;
  enabled?: number;
}

export interface _CRM_COY_32 extends Omit<CRM_COY_32, 'company_id'> {}


export interface CRM_PRD_0 {
  company_id: string;
  product_name: string;
  description: string | null;
  price_inc_vat: number;
  product_type_id: number;
  unit_id: number;
  vat_rate_id: number;
  icon_id: number;
  accounting_id: string | number | null;
  accounting_name: string | null;
}

export interface _CRM_PRD_0 extends Omit<CRM_PRD_0, 'company_id'> {}

export interface CRM_PRD_1 extends full_select {
  company_id: string;
  product_id?: number;
  product_type_id?: number;
  affiliate_id?: number | null;
}

export interface CRM_PRD_2 {
  company_id: string;
  product_id: number;
  product_name: string;
  description: string | null;
  price_inc_vat: number;
  vat_rate_id: number;
  icon_id: number;
  accounting_id: string | number | null;
  accounting_name: string | null;
}

export interface CRM_PRD_65 {
  company_id: string;
  product_id: number;
  product_name?: string;
  description?: string ;
  price_inc_vat?: number;
  vat_rate_id?: number;
  icon_id?: number;
  accounting_id?: string | number | null;
  accounting_name?: string | null;
  enable_duration_calculation?: number;
  individual_time_tracking?: number;
  unit_id: number | null;
}

export interface CRM_PRD_66 {
  company_id: string;
  affiliate_id: number;
  product_id: number;
}

export interface CRM_PRD_67 {
  company_id: string;
  price_rule_id: number;
  affiliate_id: number;
  value: number;
}

export interface _CRM_PRD_67 extends Omit<CRM_PRD_67, 'company_id'>{}

export interface CRM_PRD_68 {
  company_id: string;
  affiliate_price_rule_id: number;
}

export interface _CRM_PRD_68 extends Omit<CRM_PRD_68, 'company_id'> {}

export interface _CRM_PRD_65 extends Omit<CRM_PRD_65, 'company_id'> {}

export interface _CRM_PRD_2 extends Omit<CRM_PRD_2, 'company_id'> {}

export interface CRM_PRD_3 {
  company_id: string;
  product_id: number;
}

export interface QuantityTriggerInput {
  range_from: number | null;
  range_to: number | null;
}

export interface TimeTriggerInput {
  weekdays: number[];
  start_time: string | null;
  end_time: string | null;
}

export interface CompleteTriggerInput extends TimeTriggerInput, QuantityTriggerInput {}

export interface CRM_PRD_11 {
  company_id: string;
  price_rule_group_id: number;
  price_rule_name: string | null;
  value: number | null;
  trigger: CompleteTriggerInput;
  embed: number;
}

export interface _CRM_PRD_11 extends Omit<CRM_PRD_11, 'company_id'> {}

export interface CRM_PRD_12 {
  company_id: string;
  price_rule_group_id: number;
}

export interface CRM_PRD_13 {
  company_id: string;
  price_rule_id: number;
  price_rule_name: string | null;
  value: number;
  trigger: CompleteTriggerInput;
  embed: number;
}

export interface _CRM_PRD_13 extends Omit<CRM_PRD_13, 'company_id'> {}

export interface CRM_PRD_14 {
  company_id: string;
  price_rule_id: number;
}

export interface _CRM_PRD_14 extends Omit<CRM_PRD_14, 'company_id'> {}

export interface CRM_PRD_15 {
  company_id: string;
  product_id: number;
  price_rule_group_name: string;
  trigger_type_id: number;
}

export interface CRM_PRD_16 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_17 {
  company_id: string;
  price_rule_group_id: number;
  price_rule_group_name: string;
  trigger_type_id: number;
}

export interface CRM_PRD_18 {
  company_id: string;
  price_rule_group_id: number;
}

export interface CRM_PRD_29 {
  company_id: string;
  product_id: number;
  upsell_product_id: number;
  index: number;
}

export interface _CRM_PRD_29 extends Omit<CRM_PRD_29, 'company_id'> {}

export interface CRM_PRD_30 extends search_input, sorting_input{
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_32 {
  company_id: string;
  product_id: number;
  upsell_product_id: number;
}

export interface _CRM_PRD_32 extends Omit<CRM_PRD_32, 'company_id'> {}

export interface CRM_PRD_34 {
  order_id: number;
}

export interface CRM_PRD_35 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_36 {
  company_id: string;
  product_id: number;
}

export interface _CRM_PRD_36 extends Omit<CRM_PRD_36, 'company_id'> {}

export interface CRM_PRD_37 {
  company_id: string;
  stage_id: number;
  stage_name: string;
  billable: number;
  index: number;
  new_address: number;
  duplicatable: number;
  include_transport: number;
  billable_transport: number;
  use_in_calculation: number;
}

export interface _CRM_PRD_37 extends Omit<CRM_PRD_37, 'company_id'> {}

export interface CRM_PRD_38 {
  company_id: string;
  product_id: number;
  stage_id: number;
}

export interface _CRM_PRD_38 extends Omit<CRM_PRD_38, 'company_id'> {}

export interface CRM_PRD_39 {
  company_id: string;
  task_group_id: number;
  task_name: string;
  index: number;
}

export interface _CRM_PRD_39 extends Omit<CRM_PRD_39, 'company_id'> {}

export interface CRM_PRD_40 {
  company_id: string;
  task_id: number;
  task_name: string;
  index: number;
}

export interface _CRM_PRD_40 extends Omit<CRM_PRD_40, 'company_id'> {}

export interface CRM_PRD_41 {
  company_id: string;
  task_id: number;
}

export interface _CRM_PRD_41 extends Omit<CRM_PRD_41, 'company_id'> {}


export interface CRM_PRD_42 {
  company_id: string;
  stage_id: number;
  task_group_name: string;
  index: number;
  tasks: {
    task_name: string;
    index: number;
  }[];
}

export interface _CRM_PRD_42 extends Omit<CRM_PRD_42, 'company_id'> {}

export interface CRM_PRD_43 {
  company_id: string;
  task_group_id: number;
  task_group_name: string;
  icon_id: number;
  index: number;
  tasks: {
    task_id: number | null;
    task_name: string;
    index: number;
  }[];
}
export interface _CRM_PRD_43 extends Omit<CRM_PRD_43, 'company_id'> {}

export interface CRM_PRD_44 {
  company_id: string;
  task_group_id: number;
}

export interface _CRM_PRD_44 extends Omit<CRM_PRD_44, 'company_id'> {}

export interface CRM_PRD_45 {
  company_id: string;
  product_id: number;
  title: string;
  description: string;
  required: number;
}

export interface _CRM_PRD_45 extends Omit<CRM_PRD_45, 'company_id'> {}

export interface CRM_PRD_46 {
  company_id: string;
  entry_id: number;
  title: string;
  description: string;
  required: number;
}

export interface _CRM_PRD_46 extends Omit<CRM_PRD_46, 'company_id'> {}

export interface CRM_PRD_47 {
  company_id: string;
  entry_id: number;
}

export interface _CRM_PRD_47 extends Omit<CRM_PRD_47, 'company_id'> {}

export interface CRM_PRD_48 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_49 {
  company_id: string;
}


export interface CRM_PRD_50 {
  company_id: string;
  product_id: number;
  specification_text: string;
  specification_description: string;
  radio_selection: number;
  index: number;
  required: number;
  choices: {
    choice_name: string;
    choice_ghost_text: string | null;
    index: number;
  }[];
}

export interface _CRM_PRD_50 extends Omit<CRM_PRD_50, 'company_id'> {}

export interface CRM_PRD_51 {
  company_id: string;
  specification_id: number;
  specification_text: string;
  radio_selection: number;
  index: number;
  required: number;
  choices?: {
    choice_id: number | null;
    choice_name: string;
    choice_ghost_text: string | null;
    index: number;
  }[];
}

export interface _CRM_PRD_51 extends Omit<CRM_PRD_51, 'company_id'> {}

export interface CRM_PRD_52 {
  company_id: string;
  specification_id: number;
}

export interface CRM_PRD_53 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_54 {
  company_id: string;
  specification_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_PRD_54 extends Omit<CRM_PRD_54, 'company_id'> {}

export interface CRM_PRD_55 {
  company_id: string;
  choice_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

export interface _CRM_PRD_55 extends Omit<CRM_PRD_55, 'company_id'> {}

export interface CRM_PRD_56 {
  company_id: string;
  choice_id: number;
}

export interface CRM_PRD_57 extends full_select {
  company_id: string;
  product_id?: number;
  product_type_id?: number;
  search_string?: string | null;
  search_string_columns?: string[];
  affiliate_id?: number | null;
}

export interface CRM_PRD_59 {
  company_id: string;
}

export interface CRM_PRD_60 extends search_input, sorting_input {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_61 {
  company_id: string;
  product_id: number;
  sku: string;
  weight: number;
  stock_quantity: number;
  out_of_stock_availability: number;
}

export interface _CRM_PRD_61 extends Omit<CRM_PRD_61, 'company_id'> {}

export interface CRM_PRD_62 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_63 {
  company_id: string;
}

export interface CRM_PRD_64 {
  company_id: string;
  product_id: number;
  price_rule_ids: number[];
  quantity: number | null;
  start_time: Date | null;
  end_time: Date | null;
}

export interface CRM_PRD_69 {
  company_id: string;
  affiliate_id: number;
  product_id: number;
  price_inc_vat: number;
}

export interface CRM_PRD_70 {
  company_id: string;
  affiliate_id?: number | null;
}

export interface CRM_PRD_71 {
  company_id: string;
  product_package_name: string;
  description: string;
  icon_id: number;
}

export interface CRM_PRD_72 {
  company_id: string;
  product_package_id: number;
}

export interface CRM_PRD_73 {
  company_id: string;
  product_package_id: number;
  product_package_name: string;
  description: string;
  icon_id: number;
}

export interface CRM_PRD_74 {
  company_id: string;
  product_package_id: number;
  product_id: number;
  default_quantity: number;
  main_product: number; // 0 or 1
}

export interface CRM_PRD_75 {
  company_id: string;
  product_package_id: number;
  product_id: number;
}

export interface CRM_PRD_76 {
  company_id: string;
  product_package_id: number;
  product_id: number;
  default_quantity: number;
  main_product: number; // 0 or 1
}

export interface CRM_PRD_77 {
  company_id: string;
  product_id: number;
  quantity: number | null;
  price_rule_ids: number[];
  calculate_duration: number;
  preview_price_rule_ids: number[];
  start_time: Date | null;
  end_time: Date | null;
  use_stages: number;
  stages: {stage_id: number, address: {[key: string]: any}}[]
  order_lines?: {product_id: number | null, price_inc_vat: number; quantity: number, custom_product_name: string | null, vat_rate_id: number | null}[]
  order_response?: number;
  affiliate_id?: number | null;
  partner_contact_id?: number | null;
  override_quantity?: number | null;
  crew_size?: number | null;
  manual_hours?: number | null;
}

export interface _CRM_PRD_77 extends Omit<CRM_PRD_77, 'company_id'> {}

export interface CRM_PRD_78 {
  company_id: string;
  search_term?: string;
}

export interface CRM_PRD_82 {
  company_id: string;
  product_id: number;
}

export interface CRM_PRD_83 {
  company_id: string;
  product_id: number;
  calculation_type_id?: number | null;
  value_source_id?: number | null;
  factor?: number | null;
  default_value?: number | null;
  prefixed_calculation_type_id?: number | null;
  index?: number | null;
}

export interface _CRM_PRD_83 extends Omit<CRM_PRD_83, 'company_id'> {}

export interface CRM_PRD_84 {
  company_id: string;
  product_id: number;
  calculation_id: number;
  calculation_type_id?: number;
  value_source_id?: number | null;
  factor?: number | null;
  default_value?: number | null;
  prefixed_calculation_type_id?: number | null;
  index?: number | null;
}

export interface _CRM_PRD_84 extends Omit<CRM_PRD_84, 'company_id'> {}

export interface CRM_PRD_85 {
  company_id: string;
  product_id: number;
  calculation_id: number;
}

export interface _CRM_PRD_85 extends Omit<CRM_PRD_85, 'company_id'> {}

export interface CRM_PRD_86 {
  company_id: string;
  product_id: number;
  addresses: UnitDetails[];
  order_data: {
    crew_size: number | null;
  }
}

export interface _CRM_PRD_86 extends Omit<CRM_PRD_86, 'company_id'> {}

export interface CRM_PRD_87 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_PRD_88 {
  company_id: string;
  product_id: number;
  affiliate_id: number;
}

export interface CRM_EMB_14 {
  company_id: string;
  embed_product_id: number;
  schedule_repeat_type_id: number;
  weekdays: number[] | null | undefined;
  every: number;
  date: number | null;
  nth_weekday: number | null;
  schedule_template_name: string;
}

export interface _CRM_EMB_14 extends Omit<CRM_EMB_14, 'company_id'> {}

export interface CRM_EMB_15 {
  company_id: string;
  schedule_template_id: number;
}

export interface CRM_EMB_16 {
  company_id: string;
  embed_product_id: number;
}

export interface CRM_EMB_17 {
  company_id: string;
  schedule_template_id: number | undefined;
  schedule_repeat_type_id: number | undefined;
  weekdays: number[] | null | undefined;
  every: number | undefined;
  date: number | null| undefined;
  nth_weekday: number | null | undefined;
  schedule_template_name: string | undefined;
}

export interface _CRM_EMB_17 extends Omit<CRM_EMB_17, 'company_id'> {}

export interface CRM_PCG_0 {
  company_id: string;
  product_category_name: string;
}

export interface CRM_PCG_1 extends pagination_input, sorting_input, search_input {
  company_id: string;
}

export interface CRM_PCG_2 {
  company_id: string;
  product_category_id: number;
}

export interface CRM_STG_2 {
  company_id: string;
  order_line_stage_id: number;
  latitude: number;
  longitude: number;
  transport_started_at: Date;
  transport_finished_at: Date;
  started_at: Date;
  finished_at: Date;
  tasks: {order_line_task_id: number, status: number}[];
}

export interface CRM_STG_4 {
  company_id: string;
  order_line_stage_id: number;
}

export interface CRM_STG_5 {
  company_id: string;
  order_line_stage_id: number;
  incident_type_id: number;
  description: string;
  internal: number;
}

export interface CRM_STG_6 {
  company_id: string;
  order_id: number;
}

export interface CRM_STG_7 {
  company_id: string;
  order_line_task_id: number;
  status: number;
}

export interface CRM_STG_10 {
  company_id: string;
  incident_id: number;
  image: File;
}

export interface CRM_STG_11 {
  company_id: string;
  incident_id: number;
}

export interface CRM_STG_12 {
  company_id: string;
  order_line_stage_choice_id: number;
  value: number;
  input: string
}

export interface CRM_STG_15 {
  order_line_choice_id: number;
  value: number;
  input: string | null;
}

export interface CRM_STG_16 {
  company_id: string;
  order_line_stage_id: number;
  pause: number;
}
export interface CRM_PAY_0 {
  order_id: number;
  payment_method_id: number;
  payment_id?: number;
}

export interface CRM_PAY_2 {
  company_id: string;
  order_id: number;
  refund_reason: string;
  order_lines: {order_line_id: number, quantity: number}[];
}

export interface CRM_PAY_6 {
  company_id: string;
  private_customer: number;
}

export interface CRM_PAY_7 {
  company_id: string;
  order_id: number;
}

export interface CRM_PAY_8 {
  order_id: number;
}

export interface CRM_PAY_17 {
  // order_id: number;
  payment_method_id?: number;
  payment_id?: number;
}

export interface CRM_PAY_20 {
  payment_id: number;
  order_id: number;
}
export interface CRM_PAY_21 {
  order_id: number;
  payment_id: number;
  payment_type_enum: string;
  first_name: string;
  last_name: string;
  ssn: string;
  email: string;
  phone: string;
  profile_no?: number;
  number_of_installments?: number;
  address: {
    street: string,
    streetNumber: string,
    postalCode: string,
    postalPlace: string,
  }
}

export interface CRM_PAY_22 {
  order_id: number;
  payment_id: number;
}

export interface CRM_PAY_30 {
  company_id: string;
  order_id: number;
  template?: boolean;
}

export interface CRM_PAY_35 {
  order_id: number;
  access_level?: number;
  service_recipient?: number;
  payment_id?: number;
  work_order_id?: number;
}

export interface CRM_PAY_39 {
  order_id: number;
  language?: string;
}

export interface CRM_PAY_40{
  payment_id?: number;
}

export interface CRM_PAY_47{
  payment_id?: number;
}

export interface CRM_CUS_0 extends search_input {
  company_id: string;
}

export interface CRM_CUS_1 {
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  company_id: string;
}

export interface _CRM_CUS_1 extends Omit<CRM_CUS_1, 'company_id'> {}

export interface CRM_CUS_4 {
  company_id: string;
  customer_name: string;
  organisation_number: string;
  phone: string;
  email: string;
  invoice_email: string;
  address: UnitDetails;
  accounting_id: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  invitee?: {
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
  }
}

export interface _CRM_CUS_4 extends Omit<CRM_CUS_4, 'company_id'> {}

export interface CRM_CUS_5 {
  company_id: string;
  customer_id: number;
  customer_name?: string;
  organisation_number?: string;
  phone?: string;
  email?: string;
  address?: UnitDetails;
  invoice_address?: UnitDetails | null;
  postal_code?: string;
  city?: string;
  active?: number;
  accounting_id?: string;
  invoice_email?: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  consolidated_invoice_setting_id?: number;
}

export interface _CRM_CUS_5 extends Omit<CRM_CUS_5, 'company_id'> {}

export interface CRM_CUS_12 {
  company_id: string;
  affiliate_id: number;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  active?: number;
  accounting_id?: string | null;
  address?: UnitDetails | null;
  invoice_address?: UnitDetails | null;
  invoice_due_date_days?: number;
}

export interface _CRM_CUS_12 extends Omit<CRM_CUS_12, 'company_id'> {}

export interface CRM_CUS_6 extends pagination_input, sorting_input, search_input {
  company_id: string;
  active?: number | null;
}

export interface _CRM_CUS_6 extends Omit<CRM_CUS_6, 'company_id'> {}

export interface CRM_CUS_7 {
  company_id: string;
  customer_id: number;
}

export interface CRM_CUS_8 {
  company_id: string;
  customer_id: number;
}

export interface _CRM_CUS_10 extends Omit<CRM_CUS_10, 'company_id'> {}

export interface CRM_CUS_10 extends pagination_input, sorting_input, search_input {
  company_id: string;
  active?: number | null;
  customer_id?: number | null;
}

export interface CRM_CUS_14 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_CUS_15 extends CRM_CUS_1 {}

export interface _CRM_CUS_15 extends Omit<CRM_CUS_15, 'company_id'> {}

export interface CRM_CUS_16 {
  company_id: string;
}

// export interface CRM_CUS_17 {
//   company_id: string;
//   private_customer_id?: number;
//   business_customer_id?: number;
//   accounting_id: string | null;
// }

export interface CRM_CUS_18 {
  company_id: string;
  customer_id: number;
}

export interface CRM_CUS_19 {
  company_id: string;
  user_id: string;

}

export interface CRM_REP_0 {
  company_id: string;
  date_from: string | undefined;
  date_to: string | undefined;
}

export interface CRM_REP_1 {
  company_id: string;
  date_from: string;
  date_to: string;
}

export interface CRM_ADR_1 {
  address: string;
}

export interface CRM_ADR_3 {
  company_id: string;
  internal_id: string;
}

export interface _CRM_ADD_3 extends Omit<CRM_ADR_3, 'company_id'> {}

export interface CRM_ADR_4 {
  company_id: string;
  internal_id: string;
  section_id: string;
}

export interface CRM_ADR_5 {
  street: string;
  street_number: number;
  postal_code: string;
  street_letter?: string | null
}

export interface CRM_ADR_6 {
  address: string
}

export interface CRM_ADR_7 {
  unit_id: string;
}

export interface CRM_ADR_8 extends UnitDetails {
  company_id: string;
}

export interface CRM_ADR_9 {
  address_id: number;
}

export interface CRM_ADR_10 {
  company_id: string;
  address: AddressInput
  access_object_type: string;
  object_id: number | string;
}

export interface _CRM_ADR_10 extends Omit<CRM_ADR_10, 'company_id'> {}


export interface CRM_ADR_12 {
  company_id: string;
  affiliate_id: number;
}

export interface CRM_PRT_0 {
  company_id: string;
  partner_company_id: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
}

export interface _CRM_PRT_0 extends Omit<CRM_PRT_0, 'company_id'> {}

export interface CRM_PRT_1 {
  company_id: string;
  company_name: string;
  organisation_number: string;
  address: string;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
}

export interface _CRM_PRT_1 extends Omit<CRM_PRT_1, 'company_id'> {}

export interface CRM_PRT_2 extends full_select {
  company_id: string;
  partner_invitation_status?: number;
}

export interface _CRM_PRT_2 extends Omit<CRM_PRT_2, 'company_id'> {}

export interface CRM_PRT_3  extends full_select {
  company_id: string;
  paper_company_id: string;
}

export interface _CRM_PRT_3 extends Omit<CRM_PRT_3, 'company_id'> {}

export interface PartnerContactAttributeInput {
  attribute_id: number;
  attribute_value: number | null;
  attribute_text: string | null;
}

export interface TempPartnerEmployeeAttributeInput extends PartnerContactAttributeInput {
  attribute_name: string;
  attribute_selection_type_id: number;
}

export interface CRM_PRT_4 {
  company_id: string;
  paper_company_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  role_description: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_4 extends Omit<CRM_PRT_4, 'company_id'> {}

export interface CRM_PRT_5 {
  company_id: string;
  paper_company_id: string;
  paper_employee_id: string;
}

export interface _CRM_PRT_5 extends Omit<CRM_PRT_5, 'company_id'> {}

export interface CRM_PRT_6 {
  company_id: string;
  paper_company_id: string;
  paper_employee_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  role_description: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_6 extends Omit<CRM_PRT_6, 'company_id'> {}

export interface CRM_PRT_7 {
  company_id: string;
  paper_company_id: string;
  company_name: string;
  organisation_number: string;
  address: string;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
  hide_payment_data: number
}

export interface _CRM_PRT_7 extends Omit<CRM_PRT_7, 'company_id'> {}

export interface CRM_PRT_8 extends full_select {
  company_id: string;
}

export interface CRM_PRT_9 {
  company_id: string;
  partner_id: number
}

export interface CRM_PRT_10 {
  company_id: string;
}

export interface CRM_PRT_11 {
  company_id: string;
  attribute_name: string;
  attribute_selection_type_id: number;
}

export interface _CRM_PRT_11 extends Omit<CRM_PRT_11, 'company_id'> {}

export interface CRM_PRT_12 {
  company_id: string;
  attribute_id: number;
  attribute_name: string;
}

export interface _CRM_PRT_12 extends Omit<CRM_PRT_12, 'company_id'> {}

export interface CRM_PRT_13 {
  company_id: string;
  attribute_id: number;
}

export interface CRM_PRT_15 {
  company_id: string;
  search_term?: string | null;
}

export interface CRM_PRT_16 {
  company_id: string;
  partner_id: number;
}

export interface CRM_PRT_17 {
  company_id: string;
  address: UnitDetails;
  company_name: string;
  email: string;
  organisation_number: string;
  phone: string;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
  consolidated_invoice_setting_id?: number;
  invoice_email?: string;
}

export interface _CRM_PRT_17 extends Omit<CRM_PRT_17, 'company_id'> {}

export interface CRM_PRT_18 {
  company_id: string;
  partner_id: number;
  invoice_send_type_id?: number;
  invoice_due_date_days?: number;
  preferred_payment_method_id?: number;
  hide_payment_data?: number;
  phone?: string;
  email?: string;
  consolidated_invoice_setting_id?: number;
  invoice_email?: string;
}

export interface InvoiceConsolidationSetting {
  consolidated_invoice_setting_id: number;
  consolidated_invoice_setting_name: string;
}

export interface _CRM_PRT_18 extends Omit<CRM_PRT_18, 'company_id'> {}

export interface CRM_PRT_19 {
  company_id: string;
  partner_contact_id: number;
  attributes: PartnerContactAttributeInput[];
}

export interface CRM_PRT_20 {
  company_id: string;
  partner_id: number;
  company_name?: string;
  phone?: string;
  email?: string;
  address?: UnitDetails;
}

export interface _CRM_PRT_20 extends Omit<CRM_PRT_20, 'company_id'> {}

export interface CRM_PRT_21 {
  company_id: string;
  partner_id: number;
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  user_id?: string;
  role_description?: string;
  attributes: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_21 extends Omit<CRM_PRT_21, 'company_id'> {}

export interface CRM_PRT_22 {
  company_id: string;
  partner_contact_id: number;
}

export interface CRM_PRT_23 {
  company_id: string;
  partner_contact_id: number;
  role_description?: string;
  attributes?: PartnerContactAttributeInput[];
}

export interface _CRM_PRT_23 extends Omit<CRM_PRT_23, 'company_id'> {}

export interface CRM_INT_7 {
  company_id: string;
  employee_token: string;
  test_connection_only?: number;
}

export interface _CRM_INT_7 extends Omit<CRM_INT_7, 'company_id'> {}

export interface CRM_INT_8 {
  company_id: string;
}

export interface CRM_INT_9 {
  company_id: string;
  accounts: {entry_id: number; accounting_account_id: string;}[]
}

export interface _CRM_INT_9 extends Omit<CRM_INT_9, 'company_id'> {}

export interface CRM_INT_10 {
  company_id: string;
  search_term: string;
}

export interface _CRM_INT_10 extends Omit<CRM_INT_10, 'company_id'> {}

export interface CRM_INT_11 {
  company_id: string;
  start_date: Date;
}

export interface CRM_INT_12 {
  company_id: string;
}

export interface CRM_INT_13 {
  company_id: string;
  payment_methods: {payment_method_id: number; accounting_payment_method_id: string; accounting_payment_method_name: string}[]
}

export interface CRM_INT_14 {
  company_id: string;
}

export interface _CRM_INT_13 extends Omit<CRM_INT_13, 'company_id'> {}

export interface _CRM_INT_11 extends Omit<CRM_INT_11, 'company_id'> {}

export interface CRM_INT_15 {
  company_id: string;
  integration_application_type_id?: number;
}

export interface CRM_INT_16 {
  company_id: string;
  sales_accounts_only?: number;
  receivable_accounts_only?: number;
}

export interface CRM_INT_17 {
  company_id: string;
  client_key: string;
}

export interface CRM_INT_18 {
  company_id: string;
  accounts: {account_id: number; accounting_account_id: string;}[]
  payment_methods: {payment_method_id: number; accounting_payment_method_id: string; accounting_payment_method_name: string}[]
}

export interface _CRM_INT_18 extends Omit<CRM_INT_18, 'company_id'> {}


export interface EventChecklistItemInput {
  item_id: number | null;
  item_name: string;
  item_status: number;
}

export interface CRM_EVT_0 {
  company_id: string;
  event_type_id: number;
  execution_at: Date;
  execution_to: Date;
  event_name?: string | null;
  event_description?: string | null;
  order_id?: number | null;
  address?: UnitDetails | null;
  checklist_items?: EventChecklistItemInput[] | null;
  user_ids?: string[];
  resource_ids?: number[];
  all_day?: number;
  approved: 1;
}

export interface _CRM_EVT_0 extends Omit<CRM_EVT_0, 'company_id'> {}

export interface CRM_EVT_1 extends date_input, pagination_input {
  company_id: string;
  order_status_ids?: number[];
  payment_status_ids?: number[];
  user_ids?: string[];
  event_type_ids?: number[];
  resource_ids?: number[];
}

export interface _CRM_EVT_1 extends Omit<CRM_EVT_1, 'company_id'> {}

export interface CRM_EVT_2 {
  company_id: string;
  event_id: number;
  event_type_id?: number;
  execution_at?: Date;
  execution_to?: Date;
  event_name?: string | null;
  event_description?: string | null;
  address?: UnitDetails | null | undefined;
  approved?: 1;
  user_ids?: string[];
  checklist_items?: EventChecklistItemInput[] | null;
  resource_ids?: number[];
  all_day?: number;
}

export interface _CRM_EVT_2 extends Omit<CRM_EVT_2, 'company_id'> {}

export interface CRM_EVT_3 {
  company_id: string;
  event_id: number;
}

export interface CRM_EVT_5 {
  company_id: string;
  event_id: number;
  note_text: string;
}

export interface _CRM_EVT_5 extends Omit<CRM_EVT_5, 'company_id'> {}

export interface CRM_EVT_6 {
  company_id: string;
  event_id: number;
  note_id: number;
  note_text: string;
}

export interface _CRM_EVT_6 extends Omit<CRM_EVT_6, 'company_id'> {}

export interface CRM_EVT_7 {
  company_id: string;
  event_id: number;
  note_id: number;
}

export interface _CRM_EVT_7 extends Omit<CRM_EVT_7, 'company_id'> {}

export interface CRM_EVT_8 {
  company_id: string;
  event_id: number;
  user_id: string;
}

export interface _CRM_EVT_8 extends Omit<CRM_EVT_8, 'company_id'> {}

export interface CRM_EVT_9 {
  company_id: string;
  event_id: number;
  user_id: string;
}

export interface _CRM_EVT_9 extends Omit<CRM_EVT_9, 'company_id'> {}

export interface CRM_EVT_12 {
  company_id: string;
  datetime_from: Date;
  datetime_to: Date;
  user_ids: string[];
  resource_ids: number[];
  excluded_event_ids: number[];
}

export interface _CRM_EVT_12 extends Omit<CRM_EVT_12, 'company_id'> {}

export interface CRM_EVT_17 {
  company_id: string;
  order_number?: string;
  product_name?: string;
  customer_name?: string;
  customer_phone?: string;
  address?: string;
  comment?: string;
}

export interface _CRM_EVT_17 extends Omit<CRM_EVT_17, 'company_id'> {}


export interface CRM_TTR_0 {
  company_id: string;
  user_id: string;
  started_at: Date;
  stopped_at: Date;
  description: string;
}

export interface CRM_TTR_1 extends date_input {
  company_id: string;
}

export interface CRM_TTR_2 {
  company_id: string;
  entry_id: number;
  started_at?: Date;
  stopped_at?: Date;
  description?: string;
}

export interface CRM_TTR_3 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_4 {
  company_id: string;
  started_at: Date;
  stopped_at: Date;
  description: string;
  latitude?: number;
  longitude?: number;
}

export interface CRM_TTR_5 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_6 {
  company_id: string;
  entry_id: number;
  started_at?: Date;
  stopped_at?: Date;
  description?: string;
}

export interface CRM_TTR_7 extends date_input {
  company_id: string;
}

export interface CRM_TTR_8 extends date_input {
  company_id: string;
}

export interface CRM_TTR_8 {
  company_id: string;
  entry_id: number;
}

export interface CRM_TTR_9 extends date_input {
  company_id: string;
}


export interface CRM_RSC_0 {
  company_id: string;
  resource_name: string;
  resource_description: string;
  resource_type_id: number;
  resource_data?: {[key: string]: string | number | null} | null;
}

export interface _CRM_RSC_0 extends Omit<CRM_RSC_0, 'company_id'> {}

export interface CRM_RSC_1 {
  company_id: string;
  resource_id: number;
  resource_name?: string;
  resource_description?: string | null;
  resource_data?: {[key: string]: string | number | null | Date} | null;
}

export interface _CRM_RSC_1 extends Omit<CRM_RSC_1, 'company_id'> {}

export interface CRM_RSC_2 extends search_input, pagination_input, sorting_input {
  company_id: string;
  resource_type_ids?: number[] | null;
}

export interface _CRM_RSC_2 extends Omit<CRM_RSC_2, 'company_id'> {}

export interface CRM_RSC_3 {
  company_id: string;
  resource_id: number;
}

export interface CRM_RSC_4 extends search_input {}

export interface CRM_RSC_5 {
  company_id: string;
  resource_id: number;
  image: File;
}

export interface _CRM_RSC_5 extends Omit<CRM_RSC_5, 'company_id'> {}

export interface CRM_RSC_6 {
  company_id: string;
  resource_id: number;
}


export interface _CRM_RSC_7 extends Omit<CRM_RSC_7, 'company_id'> {}

export interface CRM_RSC_7 {
  company_id: string,
  date_from: Date,
  date_to: Date
}

export interface _CRM_RSC_6 extends Omit<CRM_RSC_6, 'company_id'> {}

export interface CRM_APP_0 {
  company_id: string;
  application_id: number;
}

export interface CRM_APP_1 {
  company_id: string;
  application_type_id?: number;
}

export interface CRM_APP_2 {
  company_id: string;
  application_id: number;
}

export interface CRM_SER_8 {
  datetime: Date;
}

export interface CRM_SER_12 {
  registration_number: string;
}

export interface CRM_SER_13 {
  status_code?: 200 | 400 | 401 | 403 | 404 | 405 | 500 | 503;
  asset_id?: string;
}

export interface CRM_SER_15 {
  organisation_number: string;
}

export interface GEO_ADR_0 {
  sok: string;
  fuzzy?: boolean;
  sokemodus?: "AND" | "OR";
  adressenavn?: string;
  adressetekst?: string;
  adressetilleggsnavn?: string;
  adressekode?: string;
  nummer?: number;
  bokstav?: string | null;
  kommunenummer?: string;
  kommunenavn?: string;
  gardsnummer?: number;
  bruksnummer?: number;
  festenummer?: number;
  undernummer?: number;
  bruksenhetnummer?: string;
  objtype?: "Vegadresse" | "Matrikkeladresse";
  poststed?: string;
  postnummer?: string;
  filter?: string;
  utkoordsys?: number;
  treffPerSide?: number;
  side?: number;
  asciiKompatibel?: boolean;
}


// REPORTINATOR

export interface REP_TMP_0 {
  company_id: string;
  template_name: string;
  template_description: string;
  template_type_id?: number;
}

export interface _REP_TMP_0 extends Omit<REP_TMP_0, 'company_id'> {}

export interface REP_TMP_1 extends full_select {
  company_id: string;
}

export interface _REP_TMP_1 extends Omit<REP_TMP_1, 'company_id'> {}

export interface REP_TMP_2 {
  company_id: string;
  template_id: number;
  template_name?: string;
  template_description?: string;
}

export interface _REP_TMP_2 extends Omit<REP_TMP_2, 'company_id'> {}

export interface REP_TMP_3 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_4 {
  company_id: string;
  template_id: number;
  element_id: number;
  parent_relation_id: number | null;
  index?: number;
}

export interface _REP_TMP_4 extends Omit<REP_TMP_4, 'company_id'> {}

export interface REP_TMP_5 {
  company_id: string;
  template_id: number;
  relation_id: number;
}

export interface _REP_TMP_5 extends Omit<REP_TMP_5, 'company_id'> {}

export interface REP_TMP_6 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_7 {
  company_id: string;
  template_id: number;
  location_ids: number[];
}

export interface _REP_TMP_7 extends Omit<REP_TMP_7, 'company_id'> {}

export interface REP_TMP_8 {
  company_id: string;
  template_id: number;
}

export interface REP_TMP_9 {
  company_id: string;
  relation_id: number;
  parent_relation_id?: number | null;
  index?: number;
  allow_page_break?: boolean;
  collapsed?: boolean;
}

export interface _REP_TMP_9 extends Omit<REP_TMP_9, 'company_id'> {}

export interface REP_ELM_0 {
  company_id: string;
  element_name: string;
  element_type_id: number;
  assessment_type_id: number;
  show_in_navigation: boolean;
  attribute_ids: number[];
}

export interface _REP_ELM_0 extends Omit<REP_ELM_0, 'company_id'> {}

export interface REP_ELM_1 extends full_select {
  company_id: string;
}

export interface _REP_ELM_1 extends Omit<REP_ELM_1, 'company_id'> {}

export interface REP_ELM_2 {
  company_id: string;
  element_id: number;
  element_name?: string;
  assessment_type_id?: number;
  show_in_navigation?: boolean;
}

export interface _REP_ELM_2 extends Omit<REP_ELM_2, 'company_id'> {}

export interface REP_ELM_3 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_4 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_5 {
  company_id: string;
  element_id: number;
  attribute_id: number;
  default_value?: number | string | null
}

export interface _REP_ELM_5 extends Omit<REP_ELM_5, 'company_id'> {}

export interface REP_ELM_6 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_6 extends Omit<REP_ELM_6, 'company_id'> {}

export interface REP_ELM_7 {
  company_id: string;
  element_id: number;
  attribute_id: number;
  phrase_title: string;
  phrase_text: string;
  rules: NestedPhraseRule[];
}

export interface _REP_ELM_7 extends Omit<REP_ELM_7, 'company_id'> {}

export interface REP_ELM_8 {
  company_id: string;
  phrase_id: number;
  phrase_title?: string;
  phrase_text?: string;
  rules?: NestedPhraseRule[];
  deleted_rule_ids?: number[];
}

export interface _REP_ELM_8 extends Omit<REP_ELM_8, 'company_id'> {}

export interface REP_ELM_9 {
  company_id: string;
  phrase_id: number;
}

export interface REP_ELM_10 {
  company_id: string;
  phrase_id: number;
  value: number;
  attribute_id: number | null;
  secondary_value?: number | null;
  use_assessment_value: boolean;
  include: boolean;
  index?: number;
  phrase_filter_type_id: number;
}

export interface NestedPhraseRule extends Omit<REP_ELM_10, 'company_id' | 'phrase_id'> {}

export interface _REP_ELM_10 extends Omit<REP_ELM_10, 'company_id'> {}

export interface REP_ELM_11 {
  company_id: string;
  phrase_id: number;
  rule_id: number;
  value?: number;
  attribute_id?: number | null;
  secondary_value?: number | null;
  use_assessment_value?: boolean;
  include?: boolean;
  index?: number;
  phrase_filter_type_id?: number;
}

export interface _REP_ELM_11 extends Omit<REP_ELM_11, 'company_id'> {}

export interface REP_ELM_12 {
  company_id: string;
  phrase_id: number;
  rule_id: number;
}

export interface _REP_ELM_12 extends Omit<REP_ELM_12, 'company_id'> {}

export interface REP_OBJ_0 {
  element_id?: number;
}

export interface REP_OBJ_2 {
  user_creatable_only?: boolean;
}

export interface REP_ELM_17 {
  company_id: string;
  element_id: number;
  child_element_id: number;
  index?: number;
}

export interface _REP_ELM_17 extends Omit<REP_ELM_17, 'company_id'> {}

export interface REP_ELM_18 {
  company_id: string;
  element_id: number;
}

export interface REP_ELM_19 {
  company_id: string;
  element_id: number;
  relation_id: number;
  index?: number;
}

export interface _REP_ELM_19 extends Omit<REP_ELM_19, 'company_id'> {}

export interface REP_ELM_20 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_20 extends Omit<REP_ELM_20, 'company_id'> {}

export interface REP_ELM_21 {
  company_id: string;
  element_id: number;
  relation_id: number;
  default_value?: number | string | null;
}

export interface _REP_ELM_21 extends Omit<REP_ELM_21, 'company_id'> {}

export interface REP_ELM_22 {
  company_id: string;
  element_id: number;
  relation_id: number;
}

export interface _REP_ELM_22 extends Omit<REP_ELM_22, 'company_id'> {}

export interface REP_REP_0 {
  company_id: string;
  template_id: number;
  revision_name?: string;
}

export interface _REP_REP_0 extends Omit<REP_REP_0, 'company_id'> {}

export interface REP_REP_1 extends full_select {
  company_id: string;
}

export interface _REP_REP_1 extends Omit<REP_REP_1, 'company_id'> {}

export interface REP_REP_2 {
  company_id: string;
  report_id: number;
  current_revision_id?: number;
}

export interface _REP_REP_2 extends Omit<REP_REP_2, 'company_id'> {}

export interface REP_REP_3 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_4 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_5 {
  company_id: string;
  report_id: number;
  revision_name?: string;
}

export interface _REP_REP_5 extends Omit<REP_REP_5, 'company_id'> {}

export interface REP_REP_6 {
  company_id: string;
  report_id: number;
}

export interface REP_REP_7 {
  company_id: string;
  revision_id: number,
  revision_name?: string;
}

export interface _REP_REP_7 extends Omit<REP_REP_7, 'company_id'> {}

export interface REP_REP_8 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_9 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_10 {
  company_id: string;
  revision_id: number;
  element_id: number;
  parent_report_element_id: number | null;
  index?: number;
}

export interface _REP_REP_10 extends Omit<REP_REP_10, 'company_id'> {}

export interface REP_REP_11 {
  company_id: string;
  revision_id: number;
}

export interface REP_REP_12 {
  company_id: string;
  report_element_id: number;
  parent_report_element_id?: number | null;
  index?: number;
  collapsed?: boolean;
}

export interface _REP_REP_12 extends Omit<REP_REP_12, 'company_id'> {}

export interface REP_REP_13 {
  company_id: string;
  report_element_id: number;
}

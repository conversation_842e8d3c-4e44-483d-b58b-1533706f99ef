export interface EmployeeAttributesResponse {
  user_id: string;
  company_id: string;
  add_product: number;
  edit_quantity: number;
  make_external_notes: number;
  make_external_reports: number;
  send_to_payment: number;
  edit_discount: number;
  view_prices: number;
  receive_order_accepted_notification: number;
  receive_order_finished_notification: number;
  payment_page_access: number;
  set_payment_to_external: number;
  receive_accounting_integration_failed_notification: number;
  view_all_events_as_crew: number;
  view_all_orders_as_crew: number;
  unpaid_order_notification: number;
  failed_schedule_order_creation_notification: number;
  receive_upcoming_order_not_accepted_notification: number;
  receive_sub_contractor_order_notification: number;
  checkin_geo_lock: number;
  receive_order_rating_notification: number;
  receive_new_embed_order_notification: number;
}


export interface UserPropertyResponse {
  property_id: number;
  property_name: string;
  property_type_name: string;
  property_type_id: number;
  is_custom: number;
}

export interface UserPropertyTypeResponse {
  property_type_id: number;
  property_type_name: string;
  is_custom: number;
  multi_select: number;
  user_properties: UserPropertyResponse[];
}

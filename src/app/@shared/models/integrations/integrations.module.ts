export interface CompanyAccountResponse {
  entry_id: number;
  account_id: number;
  accounting_account_id: string;
  accounting_account_name: string;
  account_name: string;
  account_type_name: string;
  account_type_id: number;
}

export interface TripletexAccountResponse {
  account_id: string;
  account_number: string;
  account_name: string;
}

export interface TripletexPaymentMethodResponse {
  id: number;
  description: string;
  display_name: string;
}

export interface CompanyPaymentMethodResponse {
  payment_method_id: number;
  payment_method_name: string;
  active: number;
  enabled: number;
  accounting_payment_method_id: number;
  accounting_payment_method_name: string;
  private_customer_available: number;
  business_customer_available: number;
  manually_selectable: number;
  subscription_allowed: number;
  subscription_available: number;
}

export interface IntegrationApplicationResponse {
  integration_application_id: number;
  integration_application_name: string;
  integration_application_type_id: number;
  integration_application_type_name: string;
  enabled: number;
}

export interface PogoAccountResponse {
  account_id: number;
  account_code: number;
  account_name: string;
  display_name: string;
}

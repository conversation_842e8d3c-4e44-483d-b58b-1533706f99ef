import {AddressCompactResponse} from "../address/address.module";
import {CustomerAccountingResponse} from "../customer/customer.module";


export interface AffiliateResponse {
  affiliate_id: number;
  affiliate_company_id: string,
  user_id: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  invoice_email: string;
  organisation_number: string | null;
  accounting_id: string | null;
  address: AddressCompactResponse | null;
  invoice_send_type_id: number;
  invoice_send_type_name: string;
  invoice_due_date_days: number;
  consolidated_invoice_setting_id: number;
  consolidated_invoice_setting_name: string;
  is_customer: number;
  is_partner: number;
  is_subcontractor: number;
  hide_payment_data: number;
  accounting_data: CustomerAccountingResponse | null;
  logo_url: string | null;
  disable_sms: number;
  disable_email: number;
}

export interface AffiliateContactResponse {
  affiliate_contact_id: number;
  user_id: string;
  affiliate_id: number;
  first_name: string | null;
  last_name: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  role_description: string | null;
}

export interface AffiliateSearchResponse {
  affiliate_id: number,
  affiliate_name: string | null,
  affiliate_contact_id: number | null,
  affiliate_contact_name: string,
  icon_id: number
}

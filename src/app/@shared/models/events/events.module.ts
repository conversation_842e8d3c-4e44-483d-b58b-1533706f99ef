import {OrderCompactResponse} from "../order/order.module";
import {AddressCompactResponse} from "../address/address.module";
import {ResourceResponse} from "../resources/resources.module";
import {InternalUserResponse} from "../user/user.module";

export interface EventNoteResponse {
  note_id: number;
  note_text: string;
}

export interface EventResponse {
  event_id: number;
  event_name: string;
  event_type_id: number;
  event_type_name: string;
  event_description: string | null;
  order: OrderCompactResponse | null;
  execution_at: Date;
  execution_to: Date;
  duration_hours: number;
  address: AddressCompactResponse | null;
  approved: number;
  all_day: number;
  resources: ResourceResponse[];
  notes: EventNoteResponse[];
  users: InternalUserResponse[];
}

export interface EventTypeResponse {
  event_type_id: number;
  event_type_name: string;
}

export interface EventAvailabilityResponse {
  users: [
    {
      user: InternalUserResponse;
      events: EventResponse[];
    }
  ],
  resources: [
    {
      resource: ResourceResponse;
      events: EventResponse[];
    }
  ]
}

export interface EventCountResponse {
  'count': number;
}



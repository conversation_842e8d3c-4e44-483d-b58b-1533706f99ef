import {PriceRuleResponse} from "../order/order.module";
import {VatRateResponse} from "../payment/payment.module";
import {CompleteTriggerInput} from "../input/input.service";

export interface Product {
  product_id: number;
  product_name: string;
  product_category_name: string;
  price_inc_vat: number;
  created_at: Date;
  updated_at: Date | null;
}

export interface PartnerPriceRuleResponse {
  price_rule_id: number;
  price_rule_name: string;
  value: number;
  calculation_type_id: number;
  calculation_type_name: string;
  trigger: CompleteTriggerInput;
  trigger_type_id: number;
  trigger_type_name: string;
  price_rule_group_id: number;
  partner_price_rule_id: number;
  order_line_name: string;
  total_price_adjustment: number;
  manual_trigger: number;
  fixed_price: number;
  embed: number;
  altered: number;
  original_value: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// CRM-PRD-54, CRM-PRD-55
export interface SpecificationChoiceResponse {
  choice_id: number;
  choice_name: string;
  choice_ghost_text: string | null;
  index: number;
}

// CRM-PCG-0, CRM-PCG-1
export interface ProductCategoryResponse {
  product_category_id: number;
  product_category_name: string;
  icon_id: number;
  company_id: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// CRM-PRD-50, CRM-PRD-51, CRM-PRD-53
export interface SpecificationResponse {
  specification_id: number;
  specification_text: string;
  index: number;
  radio_selection: number;
  specification_choices: SpecificationChoiceResponse[];
  required : number;
}

// CRM-PRD-39, CRM-PRD-40, CRM-STG-9
export interface TaskResponse {
  task_id: number;
  task_name: string;
  index: number;
}

// CRM-COY-13, CRM-PRD-10, CRM-STG-13
// export interface StageTypeResponse {
//   stage_type_id: number;
//   stage_type_name: string;
//   has_address: number;
//   address_type_id: number;
// }

export interface  ProductStageResponse {
  stage_id: number;
  stage_name: string;
  stage_type_id: number;
  stage_type_name: string;
  product_id: number;
  address_type_id: number;
  billable: number;
  new_address: number;
  duplicatable: number;
  include_transport: number;
  billable_transport: number;
  use_in_calculation: number;
  index: number;
  task_groups: TaskGroupResponse[];
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface TaskGroupResponse {
  task_group_id: number;
  stage_id: number;
  task_group_name: string;
  index: number;
  icon_id: number;
  tasks: TaskResponse[];
}

export interface ProductBaseResponse {
    product_id: number;
  product_name: string;
  product_category_name: string;
  product_category_icon_id: number;
  description: string;
  icon_id: number;
  company_id: string;
  price_inc_vat: number;
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  payment_type_id: number;
  payment_type_name: string;
  payment_type_description: string;
  is_taskable: number;
  vat_rate_id: number;
  vat_rate: number;
  weight: number;
  sku: string;
  // num_addresses: number
  stock_quantity: number;
  out_of_stock_availability: number;
  product_type_id: number;
  product_type_name: string;
  updated_by: string;
  accounting_id: string | number | null;
  accounting_name: string | null;
  enable_duration_calculation: number;
  individual_time_tracking: number;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}


export interface ProductResponse {
  product_id: number;
  product_name: string;
  product_category_name: string;
  description: string;
  icon_id: number;
  company_id: string;
  price_inc_vat: number;
  price_rule_groups: PriceRuleGroupResponse[];
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  payment_type_id: number;
  payment_type_name: string;
  payment_type_description: string;
  is_taskable: number;
  vat_rate_id: number;
  vat_rate: number;
  weight: number;
  sku: string;
  allow_recurring: number
  // num_addresses: number
  stock_quantity: number;
  out_of_stock_availability: number;
  product_type_id: number;
  product_type_name: string;
  accounting_id: string | number | null;
  accounting_name: string | null;
  enable_duration_calculation: number;
  individual_time_tracking: number;
  product_information: ProductInformationResponse[];
  upsell_products: UpsellProductResponse[];
  specifications: SpecificationResponse[];
  stages: ProductStageResponse[];
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// export class ExtendedPriceRuleResponse implements PriceRuleResponse {
//   price_rule_id: number;
//   price_rule_name: string;
//   value: number;
//   calculation_type_id: number;
//   calculation_type_name: string;
//   trigger: CompleteTriggerInput;
//   trigger_type_id: number;
//   trigger_type_name: string;
//   price_rule_group_id: number;
//   order_line_name: string;
//   total_price_adjustment: number;
//   manual_trigger: number;
//   fixed_price: number;
//   embed: number;
//   updated_by: string;
//   created_at: Date;
//   updated_at: Date | null;
//   deleted_at: Date | null;
//
//   // Add the days property
//   days: { name: string; value: number; abbreviation: string; active: boolean }[] = [];
// }


export interface CustomProductResponse {
  company_id: string;
  order_id: number
  product_name: string;
  price_inc_vat: number;
}

export interface ProductInformationResponse {
  entry_id: number;
  title: string;
  description: string;
  required: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
  require_ack_on_important_information: number;
}

export interface PriceRuleCalculationTypeResponse {
  calculation_type_id: number;
  calculation_type_name: string;
}

export interface PriceRuleGroupResponse {
  product_id: number;
  price_rule_group_id: number;
  price_rule_group_name: string;
  price_rules: PriceRuleResponse[];
  trigger_type_id: number;
  trigger_type_name: string;
  trigger_type_description: string,
  manual_trigger: number;
  fixed_price: number;
  calculation_type_id: number;
  total_price_adjustment: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

export interface ProductAttributeChoicesResponse {
  product_categories: ProductCategoryResponse[];
  product_types: ProductTypeResponse[];
  unit_types: UnitTypeResponse[];
  vat_types: VatRateResponse[];
}

export interface UpsellProductResponse {
  product_id: number;
  product_name: string;
  product_price_inc_vat: number;
  product_price_ex_vat: number;
  icon_id: number;
  product_description: string;
  set_quantity: number;
  product_type_id: number;
  product_type_name: string;
  index: number;
  stock_quantity: number;
  out_of_stock_availability: number;
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
  payment_type_id: number;
  payment_type_name: string;
}

export interface ProductTypeResponse {
  product_type_id: number;
  product_type_name: string;
  product_type_description: string;
  product_type_long_description: string;
  product_type_examples: string;
}

export interface PropertyTypeResponse {
  property_type_id: number;
  property_type_name: string;
}

export interface TriggerTypeResponse {
  trigger_type_id: number;
  trigger_type_name: string;
  trigger_type_description: string;
  total_price_adjustment: number;
  fixed_price: number,
  calculation_type_id: number,
  manual_trigger: number,
  deleted_at: Date | null;
}

export interface UnitTypeResponse {
  unit_id: number;
  unit_name: string;
  unit_abbreviation: string;
}

export interface SimulatedOrderDataResponse {
  quantity: number;
  product_price: number;
  total_price: number;
  order_line_name: string;
}

export interface PartnerPriceRuleGroupResponse {
  product_id: number;
  price_rule_group_id: number;
  price_rule_group_name: string;
  price_rules: PartnerPriceRuleResponse[];
  trigger_type_id: number;
  trigger_type_name: string;
  trigger_type_description: string,
  manual_trigger: number;
  fixed_price: number;
  calculation_type_id: number;
  total_price_adjustment: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}


export interface EditBasePriceResponse {
  price_inc_vat: number;
}

export interface ProductPackageProductResponse extends ProductBaseResponse {
  default_quantity: number;
  main_product: number; // 1 / 0
}

export interface ProductPackageResponse {
  product_package_id: number;
  product_package_name: string;
  description: string;
  icon_id: number;
  products: ProductPackageProductResponse[];
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}


export interface PriceRulePreviewResponse {
  price_rule_id: number;
  value: number;
  value_ex_vat: number;
  calculation_value: number;
  calculation_type_id: number;
  total_price_adjustment: number;
  fixed_price: number;
  price_rule_name: string;
  price_rule_group_name: string;
  manual: number;
}

export interface CalculatedOrderResponse {
  sales_price_ex_vat: number;
  sales_price: number;
  order_lines: CalculatedOrderLineResponse[];
  duration: number;
  job_duration: number;
  transport_duration: number;
}

export interface CalculatedOrderLineResponse {
  order_line_name: string;
  price_rule_names: string;
  product_id: number;
  unit_abbreviation: string;
  unit_id: number;
  is_taskable: number;
  product_price: number;
  product_price_ex_vat: number;
  quantity: number;
  total_price: number;
  total_price_ex_vat: number;
  price_rules: PriceRulePreviewResponse[];
  preview_price_rules: PriceRulePreviewResponse[];
}

export interface AccountingProductResponse {
  product_id: number | string;
  product_name: string;
  vat_rate_id: number | null;
}


export interface ProductQuantityCalculationTypeResponse {
  calculation_type_id: number;
  calculation_type_sign: string;
  calculation_type_description: string;
  arithmetic: number;
}

export interface ProductQuantityCalculationValueSourceResponse {
  value_source_id: number;
  value_source_name: string;
  value_source_description: string;
  data_source: string;
  data_source_key: string;
  if_case: number;
  is_boolean: number;
  is_unit: number;
}

export interface ProductQuantityCalculationResponse {
  calculation_id: number;
  factor: number | null;
  default_value: number | null;
  calculation_type_id: number;
  calculation_type_sign: string;
  calculation_type_description: string;
  prefixed_calculation_type_id: number;
  prefixed_calculation_type_sign: string;
  prefixed_calculation_type_description: string;
  value_source_id: number;
  value_source_name: string;
  value_source_description: string;
  value_source_is_boolean: number;
  value_source_is_unit: number;
  value_source_if_case: number;
  value_source_data_source: string;
  value_source_data_source_key: string;
  index: number;
}

export interface ProductQuantityCalculationTestResponse {
  hours: number;
}

export interface PriceRuleGroupObservable {
  price_rule_groups: PriceRuleGroupResponse[];
  trigger1Active: boolean;
  trigger2Active: boolean;
  trigger3Active: boolean;
  trigger4Active: boolean;
  trigger6Active: boolean;
  trigger1Empty: boolean;
  trigger2Empty: boolean;
  trigger3Empty: boolean;
  trigger4Empty: boolean;
  trigger6Empty: boolean;
}

export interface ResourceResponse {
  resource_id: number;
  resource_name: string;
  resource_type_id: number;
  resource_type_name: string;
  resource_description: string;
  resource_data: { [key: string]: string | number | Date | null}
  resource_image_url: string;
  created_at: Date;
  updated_at: Date;
}

export interface ResourceTypeResponse {
  resource_type_id: number;
  resource_type_name: string;
  data_fields: ResourceTypeDataFieldResponse[];
}

export interface ResourceTypeDataFieldResponse {
  key: string;
  field_name: string;
  field_type: string;
  index: number;
  required?: boolean; // Add optional required property
  max_length?: number; // Add optional max_length property
  min_length?: number; // Add optional min_length property
}

export interface EmployeeSalesResponse {
  "user": {
    "user_id": string,
    "first_name":  string,
    "last_name":  string,
    "full_name":  string,
    "email":  string,
    "phone":  string,
    "profile_image_url":  string,
  },
  "num_sales": number,
  "total_sales_inc_vat": number,
  "total_sales_ex_vat": number
}

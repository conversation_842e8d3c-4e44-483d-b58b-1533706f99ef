export interface VehicleRegistrationDataResponse {
  registration_number: string | null;
  vin: string | null;
  model_name: string | null;
  make: string | null;
  curb_weight: number | null;
  total_weight: number | null;
  total_payload: number | null;
  number_of_axles: number | null;
  total_payload_volume: number | null;
  number_of_seats: number | null;
  transmission: string | null;
  fuel_type: string | null;
  last_inspection_date: Date | null;
  next_inspection_date: Date | null;
}

export interface AttributeSelectionTypeResponse {
  attribute_selection_type_id: number;
  attribute_selection_type_name: string;
}

export interface PeppolEHFCheckResponse {
  can_receive_ehf: number;
}

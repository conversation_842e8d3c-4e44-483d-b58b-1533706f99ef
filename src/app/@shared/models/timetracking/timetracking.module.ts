import {InternalUserResponse} from "../user/user.module";

export interface TimeTrackingResponse {
  started_at: Date;
  stopped_at: Date ;
  ongoing: number;
  duration_in_seconds: number;
  description: string;
  entry_id: number;
}

export interface CompanyTimeTrackingResponse {
  user: InternalUserResponse;
  time_trackings: TimeTrackingResponse[];
  total_duration: number;
}

export interface TimeTrackingOccurrenceResponse {
  [key: string]: number
}

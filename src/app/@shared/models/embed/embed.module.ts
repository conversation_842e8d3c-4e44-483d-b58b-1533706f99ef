import {ProductInformationResponse, ProductStageResponse, SpecificationResponse} from "../product/product.module";

export interface EmbedResponse {
  embed_id: number;
  embed_name: string;
  primary_color: string;
  button_background_color: string;
  button_border_color: string;
  button_hover_background_color: string;
  button_hover_border_color: string;
  button_border_radius: number;
  button_text_color: string;
  button_hover_text_color: string;
  button_text: string;
  show_prices_for_customers: number;
  require_otp: number;
  show_upsell: number;
  google_tracking_id: string;
  google_conversion_send_to: string;
  embed_products: EmbedProductResponse[];
}


export interface EmbedProductResponse {
  embed_product_id: number;
  product_id: number;
  product_name: string;
  description: string;
  index: number;
  price_inc_vat: number;
  icon_id: number;
  stages: ProductStageResponse[];
  information: ProductInformationResponse[];
  unit_name: string;
  unit_id: number;
  allow_recurring: number;
  unit_abbreviation: string;
  specifications: SpecificationResponse[];
}

export interface EmbedProductScheduleTemplateResponse {
  schedule_template_id: number;
  schedule_template_name: string;
  schedule_repeat_type_id: number;
  schedule_repeat_type_name: string;
  weekdays: number[] | null;
  every: number;
  date: number;
  nth_weekday: number;
}

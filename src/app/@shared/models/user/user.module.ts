// USM-SER-0, USM-SER-4, USM-USR-2, USM-USR-3, USM-USR-16
export interface UserResponse {
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  email: string | null;
  full_name: string;
  toc_accepted_at: Date;
  active: number;
  role_id: number;
  role_name: string;
  profile_image_url: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// USM-ENT-0, USM-ENT-2, USM-SER-2, USM-USR-6, USM-USR-11, CRM-EMP-0, CRM-EMP-1
export interface UserEntityRelationWithUserDataResponse {
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  full_name: string;
  phone: string | null;
  email: string | null;
  active: number;
  role_id: number;
  role_name: string;
  identificator: string;
  taskable: number;
  profile_image_url: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// USM-ENT-3
export interface UserEntityRelationWithoutUserDataResponse {
  entity_id: string;
  entity_name: string;
  role_id: number;
  role_name: string;
  identificator: string;
  taskable: number;
  updated_by: string;
  created_at: Date;
  updated_at: Date | null;
  deleted_at: Date | null;
}

// USM-USR-20
export interface UserSimpleResponse {
  user_id: string;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  email: string | null;
  active: number;
}

// USM-ENT-2
export interface UserEntityRelationInput {
  user_id: string;
  entity_id: string;
  role_id: number;
  identificator: string;
  taskable: number;
}

// USM-USM-2, USM-USR-7
export interface UserDataInput {
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
}

// USM-USR-6
export interface UserInvitationInput {
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  role_id: number;
  identificator: string;
  taskable: number;
  entity_id: string;
  entity_name: string;
  entity_email: string;
  entity_phone: string;
  overwrite_deleted: 0 | 1;
}

// USM-USR-0
export interface UserLoginInput {
  email: string;
  phone: string;
  password: string;
}

// USM-USR-8
export interface UserLoginOTPInput {
  phone: string;
  otp: number;
}

// USM-USR-19
export interface UserInvitationRegistrationInput {
  invitation_token: string;
  password: string;
}

// USM-USR-18
export interface UserPasswordResetInput {
  old_password: string;
  new_password: string;
}

// USM-USR-13
export interface UserPasswordResetRequestInput {
  email: string;
}

// USM-USR-14
export interface UserPasswordResetRequestTokenInput {
  reset_token: string;
  new_password: string;
}


export interface UserModelResponse {
  asset_id: string
  data: {
    user_id: string;
    first_name: string | null;
    last_name: string | null;
    phone: string | null;
    email: string | null;
    full_name: string;
    toc_accepted_at: Date;
    active: number;
    role_id: number;
    role_name: string;
    profile_image_url: string;
    created_at: Date;
    updated_at: Date | null;
    deleted_at: Date | null;
  }
  reason: string
  status: string
  total_items:  number
}

export interface UserOptionsResponse {
  language: string;
  calendar_view_mode: string;
  calendar_show_weekends: number;
  calendar_view_port_from: string;
  calendar_view_port_to: string;
  calendar_order_statuses: number[];
}

export interface InternalUserResponse {
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  profile_image_url: string;
}


export interface InvitationTokenVerificationResponse {
  status_id: number;
  status_name: string;
}

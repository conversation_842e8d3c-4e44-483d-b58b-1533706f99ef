import {UnitDetails} from "../input/input.service";
import {AddressCompactResponse} from "../address/address.module";

export interface PartnerCompanyCompactResponse {
  company_id: string;
  partner_id: number;
  company_name: string;
  organisation_number: string;
  address: AddressCompactResponse | null;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
  invoice_email: string;
  invoice_due_date_days: number;
  invitation_answer: number;
  invitation_answered_at: Date | null;
  invitation_answered_text: string;
  hide_payment_data: number;
  ownership: number;
  consolidated_invoice_setting_id: number;
  consolidated_invoice_setting_name: string;
  created_at: Date;
  updated_at: Date | null;
  updated_by: string;
  deleted_at: Date | null;
}

export interface PartnerCompanyResponse extends PartnerCompanyCompactResponse {
  attributes: PartnerAttributeResponse[];
  partner_contacts: PartnerContactResponse[];
  partner_company_id: string;
}

export interface PartnerContactResponse {
  partner_contact_id: number;
  partner_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  user_id: string;
  role_description: string;
  attributes: PartnerAttributeResponse[];
}

export interface PaperCompanyResponse {
  paper_company_id: string;
  partner_id: number;
  company_name: string;
  organisation_number: string;
  address: string;
  postal_code: string;
  city: string;
  phone: string;
  email: string;
  created_at: Date;
  updated_at: Date | null;
  updated_by: string;
  deleted_at: Date | null;
}

export interface PaperCompanyEmployeeResponse {
  employee_id: string;
  partner_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  role_description: string;
  company_id: string;
  company_name: string;
  attributes: PartnerAttributeResponse[];
  created_at: Date;
  updated_at: Date | null;
  updated_by: string;
  deleted_at: Date | null;
}

export interface PartnerAttributeResponse {
  entry_id: number;
  attribute_id: number;
  attribute_name: string;
  attribute_selection_type_id: number;
  attribute_selection_type_name: string;
  attribute_value: number;
  attribute_text: string;
}

export interface CompanyPartnerAttributeResponse {
  attribute_id: number;
  attribute_name: string;
  attribute_selection_type_id: number;
  attribute_selection_type_name: string;
}


export interface PartnerSearchResponse {
  partner_id: number;
  partner_name: string;
  partner_contact_id: number | null;
  partner_contact_name: string | null;
  attributes: PartnerAttributeResponse[];
}

import { Injectable } from '@angular/core';
import {CRM_ORD_42, CRM_ORD_46, CRM_ORD_47, CRM_PRD_34} from "../models/input/input.service";
import {EndpointService} from "./endpoints.service";
import {map} from "rxjs/operators";
import {UpsellProductResponse} from "../models/product/product.module";
import {Observable} from "rxjs";
import {GetResponse} from "../models/global/response-wrapper.service";

@Injectable({
  providedIn: 'root'
})
export class UpsellProductService {

  constructor(private endpointService: EndpointService) { }

  getUpsellProducts(params: CRM_PRD_34): Observable<UpsellProductResponse[]> {
    return this.endpointService.crm_prd_34(params).pipe(
      map((response: GetResponse<UpsellProductResponse[]>) => {
        return response.data;
      })
    );
  }

  addUpsellProduct(params : CRM_ORD_42){
    return this.endpointService.crm_ord_42(params).pipe(map((data) => {
      return data.data;
    }))
  }

  removeUpsellProduct(params : CRM_ORD_46){
    return this.endpointService.crm_ord_46(params).pipe(map((data) => {
      return data.data;
    }))
  }

  updateUpsellProduct(params : CRM_ORD_47){
    return this.endpointService.crm_ord_47(params).pipe(map((data) => {
      return data.data;
    }))
  }


}

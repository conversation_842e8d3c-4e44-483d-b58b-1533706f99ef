import { Injectable } from '@angular/core';
import {
  CRM_PAY_0,
  CRM_PAY_17,
  CRM_PAY_20,
  CRM_PAY_21,
  CRM_PAY_22, CRM_PAY_30,
  CRM_PAY_35, CRM_PAY_39, CRM_PAY_40,
  CRM_PAY_47,
  CRM_PAY_6
} from "../models/input/input.service";
import {EndpointService} from "./endpoints.service";
import {map} from "rxjs/operators";
import {StorageService} from "../../@core/@core/services/storage.service";
import {TokenService} from "../../@core/@core/services/token.service";

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  constructor(
    private endpointService: EndpointService,
    private storageService: StorageService,
    private tokenService: TokenService
  ) { }

  //CRM_PAY_6 get all payment methods
  getPaymentMethods(params: CRM_PAY_6) {
    return this.endpointService.crm_pay_6(params).pipe(map((data) => {
      return data.data;
    }))
  }

  //CRM_PAY_20 get all rivert payment methods
  getRivertyPaymentMethods(params: CRM_PAY_20) {
    return this.endpointService.crm_pay_20(params).pipe(map((data) => {
      return data.data;
    }))
  }


  authorizeRivertyPayment(params: CRM_PAY_21) {
    return this.endpointService.crm_pay_21(params).pipe(map((data) => {
      return data.data;
    }))
  }

  voidRivertyPayment(params: CRM_PAY_47) {
    return this.endpointService.crm_pay_47(params).pipe(map((data) => {
      return data.data;
    }))
  }


  fetchRivertyOrder(params: CRM_PAY_22) {
    return this.endpointService.crm_pay_22(params).pipe(map((data) => {
      return data.data;
    }))
  }



  //CRM_PAY_0 create payment
  createPayment(params : CRM_PAY_0) {
    return this.endpointService.crm_pay_0(params).pipe(map((data) => {
      return data.data;
    }))
  }

  createPaymentSubscription(payload: CRM_PAY_17) {
    return this.endpointService.crm_pay_17(payload).pipe(map((data) => {
      return data.data;
    }));
  }

  getPayments(params: CRM_PAY_35){
    // Use the injected TokenService
    console.log('Token in PaymentService.getPayments before API call:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    return this.endpointService.crm_pay_35(params).pipe(map((data) => {
      console.log('Payment data received successfully');
      return data.data;
    }))
  }

  getRepeatingPayments(params: CRM_PAY_39) {
    return this.endpointService.crm_pay_39(params).pipe(map((data) => data.data));
  }

  cancelSubscription(params: CRM_PAY_40){
    return this.endpointService.crm_pay_40(params).pipe(map((data) => {
      return data;
    }))
  }

}

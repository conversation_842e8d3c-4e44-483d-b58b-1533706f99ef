import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ScreenSizeService {
  private isMobileSubject = new BehaviorSubject<boolean>(this.checkIfMobile());
  isMobile$ = this.isMobileSubject.asObservable();

  constructor() {
    fromEvent(window, 'resize')
      .pipe(
        debounceTime(100),
        map(() => this.checkIfMobile()),
        distinctUntilChanged(),
        startWith(this.checkIfMobile())
      )
      .subscribe(isMobile => this.isMobileSubject.next(isMobile));
  }

  private checkIfMobile(): boolean {
    return window.innerWidth < 768; // Bootstrap's default breakpoint for mobile
  }

  get isMobile(): boolean {
    return this.checkIfMobile();
  }
}
import {Injectable, Injector} from "@angular/core";
import {catchError, Observable, of, switchMap, throwError} from "rxjs";
import {LoginResponse} from "../models/authentication/authentication.module";
import {HttpClient, HttpErrorResponse, HttpHeaders, HttpParams} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {TranslateService} from "@ngx-translate/core";
import {
  CRM_COY_1, CRM_COY_10, CRM_COY_18,
  CRM_COY_23,
  CRM_ORD_152, CRM_ORD_153, CRM_ORD_161, CRM_ORD_173, CRM_ORD_174, CRM_ORD_180, CRM_ORD_181, CRM_ORD_182, CRM_ORD_23,
  CRM_ORD_29, CRM_ORD_33, CRM_ORD_37, CRM_ORD_38,
  CRM_ORD_40,
  CRM_ORD_42,
  CRM_ORD_46,
  CRM_ORD_47,
  CRM_ORD_89, CRM_ORD_92, CRM_ORD_98, CRM_ORD_99,
  CRM_PAY_0, CRM_PAY_17, CRM_PAY_20, CRM_PAY_21, CRM_PAY_22, CRM_PAY_30, CRM_PAY_35, CRM_PAY_39, CRM_PAY_40,
  CRM_PAY_47,
  CRM_PAY_6,
  CRM_PRD_30,
  CRM_PRD_34,
  CRM_PRD_48, CRM_STG_15,
  USM_USR_0,
  USM_USR_4,
  USM_USR_8,
  USM_USR_9
} from "../models/input/input.service";
import {map} from "rxjs/operators";
import {convertPayloadDatetime, convertResponseDatetime} from "../../@core/@core/utils/utils.service";
import {
  DeleteResponse,
  GetResponse,
  PaginationResponse,
  PostResponse,
  PutResponse
} from "../models/global/response-wrapper.service";
import {
  ExtendedIncidentResponse,
  IncidentResponse,
  OrderCustomerQuestionChoiceResponse,
  OrderLineSpecificationChoiceResponse, OrderNoteResponse,
  OrderResponse,
  OrderScheduleResponse, WorkOrderResponse
} from "../models/order/order.module";
import {CompanyGeneralSettingsResponse, CompanyPrivacyResponse, CompanyResponse, CompanyTOCResponse} from "../models/company/company.module";
import {ProductInformationResponse, UpsellProductResponse} from "../models/product/product.module";
import {
  OrderPaymentResponse,
  PaymentMethodResponse,
  RivertyPaymentAuthorizeResponse, RivertyPaymentMethodResponse,
  RivertyPaymentStatusResponse,
} from "../models/payment/payment.module";
import {UserResponse} from "../models/user/user.module";
import { TokenService } from "../../@core/@core/services/token.service";
import { Router } from "@angular/router";
import {ToastService} from "../../@core/@core/services/toast.service";


export interface EndpointOptions {
  auth?: boolean,
  content_type?: string | null,
  platform?: boolean,
  raiseErrorToast?: boolean
  headers?: { [key: string]: string };
}

@Injectable({
  providedIn: 'root',
})
export class EndpointService {
  constructor(private http: HttpClient,
              private tokenService: TokenService,
              private translateService: TranslateService,
              private injector: Injector,
              private router: Router,
              private toastService: ToastService) {
  }

  // private refreshTokenSubject: BehaviorSubject<any | null> = new BehaviorSubject<any | null>(null);
  // private refreshTokenInProgress = false;

  // refreshToken(): Observable<string> {
  //   const authService = this.injector.get(AuthService);

  //   if (this.refreshTokenInProgress) {
  //     console.log('Refresh token in progress. Waiting for the new token...');
  //     return this.refreshTokenSubject.pipe(
  //       filter((token) => token !== null),
  //       take(1)
  //     );
  //   }

  //   console.log('Starting refresh token process...');
  //   this.refreshTokenInProgress = true;
  //   this.refreshTokenSubject.next(null);

  //   return authService.refreshToken().pipe(
  //     switchMap((data) => {
  //       const newToken = data.access_token;
  //       console.log('New access token received:', newToken);
  //       this.refreshTokenSubject.next(newToken);
  //       console.log('Refreshed token through pre-interceptor in http service');
  //       return of(newToken); // Return the new token
  //     }),
  //     catchError((error) => {
  //       console.error('Error during token refresh:', error);
  //       // Handle token refresh error here
  //       return throwError(error);
  //     }),
  //     finalize(() => {
  //       console.log('Refresh token process completed.');
  //       this.refreshTokenInProgress = false;
  //     })
  //   );
  // }

  // getDefaultHeaders(auth: boolean = true, content_type: string | null = 'application/json', platform: boolean = false): Observable<HttpHeaders> {
  //   // Accept-Language
  //   let headers = new HttpHeaders({
  //     'Accept-Language': this.translateService.currentLang ? this.translateService.currentLang : 'en',
  //   });

  //   // Content-Type
  //   if (content_type != null) {
  //     headers = headers.append('Content-Type', content_type);
  //   }

  //   // Platform
  //   if (platform) {
  //     headers = headers.append('Platform', 'customer');
  //   }

  //   // Authorization
  //   if (auth) {
  //     const accessToken = this.tokenService.getToken() ;
  //     if (!accessToken) {
  //       throw new Error('No access token found');
  //     }

  //     const decodedToken: { [key: string]: string | number } = jwtDecode(accessToken);
  //     const currentTimestamp = Math.floor(Date.now() / 1000); // Get current Unix timestamp in seconds
  //     const tokenExpiration = decodedToken['exp'];

  //     if (tokenExpiration && Number(tokenExpiration) < currentTimestamp) {
  //       return this.refreshToken().pipe(
  //         switchMap((newToken) => {
  //           headers = headers.append('Authorization', 'Bearer ' + newToken);
  //           return of(headers);
  //         })
  //       );
  //     } else {
  //       headers = headers.append('Authorization', 'Bearer ' + accessToken);
  //       return of(headers);
  //     }
  //   } else {
  //     return of(headers);
  //   }
  // }

  getDefaultHeaders(auth: boolean = true, contentType: string | null = 'application/json', platform: boolean = false, extraHeaders?: { [key: string]: string }, url?: string): Observable<HttpHeaders> {
    // Accept-Language
    let headers = new HttpHeaders({
      'Accept-Language': this.translateService.currentLang ? this.translateService.currentLang : 'no',
    });

    // Content-Type
    if (contentType != null) {
      headers = headers.append('Content-Type', contentType);
    }

    // Platform
    if (platform) {
      headers = headers.append('Platform', 'customer');
    }

    // Authorization
    if (auth) {
      console.log('Endpoint service requesting token for API call');
      try {
        const accessToken = this.tokenService.getToken();
        if (!accessToken) {
          console.error('No access token found in endpoint service');
          console.error('URL being accessed:', url || 'Unknown URL');
          console.error('Stack trace:', new Error().stack);
          throw new Error('No access token found');
        }
        console.log('Token found, adding to headers');
        // Assuming the token is valid, directly append it to the headers
        headers = headers.append('Authorization', 'Bearer ' + accessToken);
      } catch (error) {
        console.error('Error getting token:', error);
        console.error('URL being accessed:', url || 'Unknown URL');
        console.error('Stack trace:', new Error().stack);
        throw error;
      }
    }

    // Append any extra headers provided
    if (extraHeaders) {
      Object.keys(extraHeaders).forEach(key => {
        headers = headers.set(key, extraHeaders[key]);
      });
    }

    return of(headers);
  }

  private handleResponse<T>(observable: Observable<T>): Observable<T> {
    return observable.pipe(
      map((res) => {
        if (res) {
          convertResponseDatetime(res);
        }
        return res;
      })
    )};

  private handlePayload(data: any): any {
    if (data) {
      data = convertPayloadDatetime(data);
    }
    return data;
  }


  post<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.headers, url).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.post<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error.status === 401 || error.status === 418) {
              this.tokenService.removeToken();
              this.router.navigate(['/login']);
            }

            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  get<R>(
    url: string,
    queryParams: { [key: string]: any } = {},
    options: EndpointOptions
  ): Observable<R> {
    return this.getDefaultHeaders(
      options.auth,
      options.content_type,
      options.platform,
      options.headers,
      url
    ).pipe(
      switchMap((headers) => {
        queryParams = this.handlePayload(queryParams);
        let httpParams = new HttpParams();
        if (queryParams) {
          Object.keys(queryParams).forEach(key => {
            const value = queryParams[key];
            if (value !== undefined && value !== null) {
              httpParams = httpParams.set(key, value);
            }
          });
        }

        return this.http.get<R>(url, { headers: headers, params: httpParams }).pipe(
          catchError((error: any) => {
            if (error.status === 401 || error.status === 418) {
              this.tokenService.removeToken();
              this.router.navigate(['/login']);
            }

            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
              // Handle error toast here if needed
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }


  put<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.headers, url).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.put<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error.status === 401 || error.status === 418) {
              this.tokenService.removeToken();
              this.router.navigate(['/login']);
            }

            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  patch<R>(url: string, payload: any, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.headers, url).pipe(
      switchMap((headers) => {
        payload = this.handlePayload(payload);
        return this.http.patch<R>(url, payload, {headers: headers}).pipe(
          catchError((error: any) => {
            if (error.status === 401 || error.status === 418) {
              this.tokenService.removeToken();
              this.router.navigate(['/login']);
            }

            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }

  delete<R>(url: string, queryParams: { [key: string]: any } | undefined, options: EndpointOptions): Observable<R> {
    return this.getDefaultHeaders(options.auth, options.content_type, options.platform, options.headers, url).pipe(
      switchMap((headers) => {
        queryParams = this.handlePayload(queryParams);
        let httpParams = new HttpParams();
        if (queryParams) {
          Object.keys(queryParams).forEach(key => {
            const value = queryParams?.[key];
            if (value !== undefined && value !== null) {
              httpParams = httpParams.set(key, value);
            }
          });
        }
        return this.http.delete<R>(url, { headers: headers, params: httpParams }).pipe(
          catchError((error: any) => {
            if (error.status === 401 || error.status === 418) {
              this.tokenService.removeToken();
              this.router.navigate(['/login']);
            }

            if (error instanceof HttpErrorResponse && options.raiseErrorToast) {
            }
            return throwError(error);
          }),
          switchMap((response: R) => {
            return this.handleResponse<R>(of(response));
          })
        );
      })
    );
  }


  usm_usr_0(payload: USM_USR_0, raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let options: EndpointOptions = {
      auth: false,
      content_type: null,
      platform: true,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/login', payload, options)
  }

  usm_usr_4(payload: USM_USR_4, raiseErrorToast: boolean = true): Observable<UserResponse> {
    let options: EndpointOptions = {
      auth: true,
      raiseErrorToast: raiseErrorToast
    }
    return this.put(environment.usermanagementApiUrl + 'users/', payload, options)
  }

  usm_usr_8(payload: USM_USR_8, raiseErrorToast: boolean = true): Observable<LoginResponse> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast,
      platform: true
    }
    return this.post(environment.usermanagementApiUrl + 'users/login/otp', payload, options)
  }

  usm_usr_9(payload: USM_USR_9, raiseErrorToast: boolean = true): Observable<GetResponse<null>> {
    let options: EndpointOptions = {
      auth: false,
      raiseErrorToast: raiseErrorToast
    }
    return this.post(environment.usermanagementApiUrl + 'users/login/otp/initiate', payload, options)
  }

//   usm_usr_12(raiseErrorToast: boolean = true): Observable<LoginResponse> {
//   let headers = new HttpHeaders({
//     'Content-Type': 'application/json',
//     'Accept-Language': this.translateService.currentLang,
//     'Authorization': 'Bearer ' + this.jwtService.getRefreshToken(),
//     'Platform': 'customer'
//   })
//   let options = {headers: headers}
//   return this.http.post<LoginResponse>(environment.usermanagementApiUrl + 'users/refresh-token', {}, options).pipe(
//     catchError((error: any) => {
//       if (error instanceof HttpErrorResponse && raiseErrorToast) {
//         // this.toastService.errorToast(error.error.asset_id);
//       }
//       return throwError(error);
//     }),
//   );
// }


  /**
   *
   * Endpoint definitions
   * CRM
   * Orders
   *
   */

  crm_ord_23(payload: CRM_ORD_23, raiseErrorToast: boolean = true): Observable<PostResponse<OrderNoteResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-notes/customer', payload, {raiseErrorToast: raiseErrorToast})
  }


  crm_ord_29(params: CRM_ORD_29, raiseErrorToast: boolean = true): Observable<PaginationResponse<OrderResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/as-user', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_33(payload: CRM_ORD_33, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/confirm/customer', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_37(payload: CRM_ORD_37, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.put<PutResponse<OrderResponse>>(
      environment.crmApiUrl + 'orders/feedback', payload,{raiseErrorToast: raiseErrorToast})
  }

  crm_ord_38(payload: CRM_ORD_38): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(environment.crmApiUrl + 'orders/authenticate/order-token', payload)
  }

  crm_ord_40(payload: CRM_ORD_40, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/accept/customer', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_42(payload: CRM_ORD_42, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    return this.post(environment.crmApiUrl + 'orders/order-lines/customer', payload, {raiseErrorToast: raiseErrorToast})
  }


  // crm_ord_43(params: CRM_ORD_43, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyResponse>> {
  //   return this.get(environment.crmApiUrl + 'orders/company-data/customer', params, {raiseErrorToast: raiseErrorToast})
  // }

  crm_ord_46(params: CRM_ORD_46, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/order-lines/customer', params, {raiseErrorToast: raiseErrorToast})
  }


  crm_ord_47(payload: CRM_ORD_47, raiseErrorToast: boolean = true): Observable<PutResponse<OrderResponse>> {
    return this.patch(environment.crmApiUrl + 'orders/order-lines/customer', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_89(params?: CRM_ORD_89, raiseErrorToast: boolean = true): Observable<GetResponse<OrderScheduleResponse[]>> {
    if (params) {
      return this.get<GetResponse<OrderScheduleResponse[]>>(environment.crmApiUrl + 'orders/schedules/customer', params, {raiseErrorToast: raiseErrorToast})
    }
    else {
      return this.get<GetResponse<OrderScheduleResponse[]>>(environment.crmApiUrl + 'orders/schedules/customer', params, {raiseErrorToast: raiseErrorToast})
    }
  }

  crm_ord_92(payload: CRM_ORD_92, raiseErrorToast: boolean = true): Observable<PutResponse<OrderScheduleResponse>> {
    return this.put(environment.crmApiUrl + 'orders/schedules/payment-method/customer', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_ord_152(payload: CRM_ORD_152, raiseErrorToast: boolean = true): Observable<PutResponse<OrderCustomerQuestionChoiceResponse>> {
    return this.put<PutResponse<OrderCustomerQuestionChoiceResponse>>(environment.crmApiUrl + 'orders/work-orders/customer-questions/set-choice/customer', payload,{raiseErrorToast: raiseErrorToast});
  }

  crm_ord_153(payload: CRM_ORD_153, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse[]>> {
    return this.get<PutResponse<WorkOrderResponse[]>>(environment.crmApiUrl + 'orders/work-orders/order/as-user', payload,{raiseErrorToast: raiseErrorToast});
  }

  crm_ord_161(payload: CRM_ORD_161, raiseErrorToast: boolean = true): Observable<PutResponse<WorkOrderResponse[]>> {
    return this.get<PutResponse<WorkOrderResponse[]>>(environment.crmApiUrl + 'orders/work-orders/order/as-user', payload,{raiseErrorToast: raiseErrorToast});
  }


  crm_ord_173(payload: CRM_ORD_173, raiseErrorToast: boolean = true): Observable<PostResponse<OrderResponse>> {
    // if (payload.work_order_id) {
    //   throw new Error('Cannot use work_order_id for crm_ord_98, use crm_ord_98_wo instead');
    // }
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { 'order_id': payload.order_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('attachment', payload.attachment);

        return this.http.post<PostResponse<OrderResponse>>(environment.crmApiUrl + 'orders/attachments/customer', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.showError(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: PostResponse<OrderResponse>) => {
            return this.handleResponse<PostResponse<OrderResponse>>(of(response));
          })
        );

      })
    );
  }

  crm_ord_174(payload: CRM_ORD_174, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + 'orders/attachments/customer', payload, {raiseErrorToast: raiseErrorToast})
  }

  crm_ord_175(params: { [key: string]: any }, raiseErrorToast: boolean = true): Observable<Blob> {
    return this.getDefaultHeaders(true, 'application/octet-stream').pipe(
      switchMap((headers) => {
        headers.set('Accept', 'application/octet-stream');
        let httpParams = new HttpParams();
        Object.keys(params).forEach(key => {
          const value = params[key];
          if (value !== undefined && value !== null) {
            httpParams = httpParams.set(key, value);
          }
        });
        let options = { headers: headers, params: httpParams, responseType: 'blob' as 'json' };
        return this.http.get<Blob>(environment.crmApiUrl + 'orders/attachments/customer', options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.showError(error.error.asset_id);
            }
            return throwError(error);
          }),
        );
      })
    );
  }

  crm_ord_180(payload: CRM_ORD_180): Observable<PostResponse<IncidentResponse>> {
    return this.post(environment.crmApiUrl + 'orders/work-orders/incidents/customer', payload, {raiseErrorToast: true})
  }

  crm_ord_181(payload: CRM_ORD_181, raiseErrorToast: boolean = true): Observable<PostResponse<IncidentResponse>> {
    return this.getDefaultHeaders(true, null).pipe(
      switchMap((headers) => {
        let params = { incident_id: payload.incident_id };
        let options = { headers: headers, params: params };

        let formData = new FormData();
        formData.append('image', payload.image);
        formData.append('incident_id', payload.incident_id.toString());


        return this.http.post<PostResponse<IncidentResponse>>(environment.crmApiUrl + 'orders/work-orders/incidents/image/customer', formData, options).pipe(
          catchError((error: any) => {
            if (error instanceof HttpErrorResponse && raiseErrorToast) {
              this.toastService.showError(error.error.asset_id);
            }
            return throwError(error);
          }),
          switchMap((response: PostResponse<IncidentResponse>) => {
            return this.handleResponse<PostResponse<IncidentResponse>>(of(response));
          })
        );

      })
    );
  }

  crm_ord_182(payload: CRM_ORD_182, raiseErrorToast: boolean = true): Observable<GetResponse<ExtendedIncidentResponse[]>> {
    return this.get(environment.crmApiUrl + 'orders/incidents/customer', payload, {raiseErrorToast: raiseErrorToast})
  }


  // crm_ord_29(params: CRM_ORD_29): Observable<PaginationResponse<OrderResponse[]>> {
  //   return this.get<PaginationResponse<OrderResponse[]>>(
  //     environment.crmApiUrl + 'orders/as-user', params)
  // }


  /**
   *
   * Endpoint definitions
   * CRM
   * Product
   *
   */

  crm_prd_30(params: CRM_PRD_30, raiseErrorToast: boolean = true): Observable<GetResponse<UpsellProductResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/upsell', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_34(params: CRM_PRD_34, raiseErrorToast: boolean = true): Observable<GetResponse<UpsellProductResponse[]>> {
    return this.get<GetResponse<UpsellProductResponse[]>>(environment.crmApiUrl + 'products/upsell/customer', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_prd_48(params: CRM_PRD_48, raiseErrorToast: boolean = true): Observable<GetResponse<ProductInformationResponse[]>> {
    return this.get(environment.crmApiUrl + 'products/information', params, {raiseErrorToast: raiseErrorToast});
  }



  /**
   *
   * Endpoint definitions
   * CRM
   * Company
   *
   */


  crm_coy_1(params: CRM_COY_1, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyResponse>> {
    return this.get(environment.crmApiUrl + 'companies/', params, {raiseErrorToast: raiseErrorToast})
  }


  crm_coy_18(params: CRM_COY_18, raiseErrorToast: boolean = true):  Observable<GetResponse<CompanyPrivacyResponse>> {
    return this.get<GetResponse<CompanyPrivacyResponse>>(
      environment.crmApiUrl + 'companies/privacy-policy', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_coy_10(params: CRM_COY_10, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyTOCResponse>> {
    return this.get<GetResponse<CompanyTOCResponse>>(environment.crmApiUrl + 'companies/terms-and-conditions', params, {raiseErrorToast: raiseErrorToast, auth: false});
  }

  crm_coy_23(params: CRM_COY_23, raiseErrorToast: boolean = true): Observable<GetResponse<CompanyGeneralSettingsResponse>> {
    return this.get(environment.crmApiUrl + 'companies/settings/general', params, {raiseErrorToast: raiseErrorToast})
  }

  /**
   *
   * Endpoint definitions
   * CRM
   * Payment
   *
   */


  crm_pay_0(payload: CRM_PAY_0, raiseErrorToast: boolean = true): Observable<PostResponse<any>> {
    return this.post<PostResponse<OrderPaymentResponse>>(environment.crmApiUrl + 'payment/create', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_6(params: CRM_PAY_6, raiseErrorToast: boolean = true): Observable<GetResponse<PaymentMethodResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/payment-methods', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_17(payload: CRM_PAY_17, raiseErrorToast: boolean = true): Observable<PostResponse<any>> {
    return this.post<PostResponse<OrderPaymentResponse>>(environment.crmApiUrl + 'payment/subscriptions', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_20(payload: CRM_PAY_20, raiseErrorToast: boolean = true): Observable<GetResponse<RivertyPaymentMethodResponse>> {
    return this.get<GetResponse<RivertyPaymentMethodResponse>>(environment.crmApiUrl + 'payment/riverty/payment-methods', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_21(params: CRM_PAY_21, raiseErrorToast: boolean = true): Observable<PostResponse<RivertyPaymentAuthorizeResponse>> {
    return this.post<PostResponse<RivertyPaymentAuthorizeResponse>>(environment.crmApiUrl + 'payment/riverty/authorize', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_22(payload: CRM_PAY_22, raiseErrorToast: boolean = true): Observable<GetResponse<RivertyPaymentStatusResponse>> {
    return this.get<GetResponse<RivertyPaymentStatusResponse>>(environment.crmApiUrl + 'payment/riverty/order-update', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_30(payload: CRM_PAY_30, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse[]>> {
    return this.get(environment.crmApiUrl + 'payment/order/all', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_35(payload: CRM_PAY_35, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse[]>> {
    return this.get<GetResponse<OrderPaymentResponse[]>>(environment.crmApiUrl + 'payment/order-payments/as-user', payload, {raiseErrorToast: raiseErrorToast});
  }

  crm_pay_39(payload: CRM_PAY_39, raiseErrorToast: boolean = true): Observable<GetResponse<OrderPaymentResponse[]>> {
    const languageCode = localStorage.getItem('language') || 'no';
    const headers = {'Accept-Language': languageCode,};
    return this.get<GetResponse<OrderPaymentResponse[]>>(environment.crmApiUrl + 'payment/order/repeating-payments/as-user', payload, { headers, raiseErrorToast });
  }

  crm_pay_40(params: CRM_PAY_40, raiseErrorToast: boolean = true): Observable<DeleteResponse> {
    return this.delete(environment.crmApiUrl + '/payment/subscriptions', params, {raiseErrorToast: raiseErrorToast})
  }

  crm_pay_47(params: CRM_PAY_47, raiseErrorToast: boolean = true): Observable<PostResponse<any>> {
    return this.post<PostResponse<RivertyPaymentAuthorizeResponse>>(environment.crmApiUrl + 'payment/riverty/void', params, {raiseErrorToast: raiseErrorToast});
  }

  crm_stg_15(payload: CRM_STG_15, raiseErrorToast: boolean = true): Observable<PutResponse<OrderLineSpecificationChoiceResponse>> {
    return this.put<PutResponse<OrderLineSpecificationChoiceResponse>>(environment.crmApiUrl + 'stages/stage-specifications/set-choice/customer', payload,{raiseErrorToast: raiseErrorToast});
  }


  usm_usr_3(): Observable<GetResponse<UserResponse>> {
    return this.get<GetResponse<UserResponse>>(environment.usermanagementApiUrl + 'users/', undefined, { raiseErrorToast: true });
  }

}

import { Injectable } from '@angular/core';
import {BehaviorSubject, catchError, Observable, throwError} from 'rxjs';
import {filter, map} from 'rxjs/operators';
import { IncidentResponse, OrderResponse, OrderScheduleResponse } from '../models/order/order.module';
import { EndpointService } from './endpoints.service';
import {StorageService} from "../../@core/@core/services/storage.service";
import { TokenService } from "../../@core/@core/services/token.service";
import { CookieService } from "ngx-cookie-service";
import {
  _CRM_ORD_152, CRM_ORD_100, CRM_ORD_152, CRM_ORD_153, CRM_ORD_161, CRM_ORD_173, CRM_ORD_174, CRM_ORD_175, CRM_ORD_180, CRM_ORD_181, CRM_ORD_182, CRM_ORD_23,
  CRM_ORD_29, CRM_ORD_33, CRM_ORD_37,
  CRM_ORD_40,
  CRM_ORD_47,
  CRM_ORD_92, CRM_ORD_98, CRM_ORD_99,
} from "../models/input/input.service";
import {CompanyService} from "./company.service";
import {Router} from "@angular/router";

@Injectable({
  providedIn: 'root'
})
export class OrderService {

  private emptyOrder: OrderResponse = {} as OrderResponse;
  private orderSource: BehaviorSubject<OrderResponse> = new BehaviorSubject<OrderResponse>(this.emptyOrder);
  public order$: Observable<OrderResponse> = this.orderSource.asObservable();

  private currentOrder: OrderResponse | null = null;

  private scheduleSource: BehaviorSubject<OrderScheduleResponse> = new BehaviorSubject<any>(null)
  schedule$: Observable<OrderScheduleResponse> = this.scheduleSource.asObservable().pipe(filter((value) => value !== null))

  constructor(
    private endpointService: EndpointService,
    private storageService: StorageService,
    private companyService: CompanyService,
    private route: Router,
    private tokenService: TokenService
  ) { }




  addOrderNoteToOrder(params: CRM_ORD_23) {
    return this.endpointService.crm_ord_23(params).pipe(map((data) => {
      return data.data;
    }))
  }

  getOrdersAsUser(params: CRM_ORD_29){
    return this.endpointService.crm_ord_29(params).pipe(map((data) => {
      return data;
    }))
  }

  // getSubscriptions() {
  //   return this.endpointService.crm_ord_89().pipe(map((data) => {
  //     return data.data;
  //   }))
  // }

  // getOrderScheduleById(id: number) {
  //   let params: CRM_ORD_89 = {
  //     order_schedule_id: id
  //   }
  //
  //   // return this.endpointService.crm_ord_89(params).pipe(map((data) => {
  //   //   return data.data;
  //   // }))
  // }

  updateOrderSchedulePaymentMethod(payload: CRM_ORD_92) {
    return this.endpointService.crm_ord_92(payload).pipe(map((data) => {
      return data.data
    }))
  }

  getOrderById(orderId: number) {
    let params: CRM_ORD_29 = {
      order_id: orderId
    }
    return this.endpointService.crm_ord_29(params).pipe(map((data) => {
      if (data.data.length > 0) {
        return data.data[0];
      }
      else {
        throw new Error('Order not found');
      }
    }))
  }

  // updateSpecificationChoice(params : CRM_STG_15){
  //   return this.endpointService.crm_stg_15(params).pipe(map((data) => {
  //     return data.data;
  //   }))
  // }

  acceptOrderAsCustomer(params: CRM_ORD_40) {
    return this.endpointService.crm_ord_40(params)
      .pipe(
        map((response) => response.data)
      );
  }

  patchOrderLine(params : CRM_ORD_47){
    return this.endpointService.crm_ord_47(params).pipe(map((data) => {
      return data.data;
    }))
  }

  //CRM_ORD_37
  setOrderFeedback(params: CRM_ORD_37) {
    return this.endpointService.crm_ord_37(params).pipe(map((data) => {
      return data.data;
    }))
  }

  confirmOrderAsCustomer(params: CRM_ORD_33) {
    return this.endpointService.crm_ord_33(params)
      .pipe(
        map((response) => response.data)
      );
  }


  updateQuestionChoice(params : _CRM_ORD_152){
    let payload: CRM_ORD_152 = {
      company_id: this.storageService.getSelectedCompanyId(),
      ...params
    }
    return this.endpointService.crm_ord_152(payload).pipe(map((data) => {
      return data.data
    }))
  }

  getChildWorkOrders(params : CRM_ORD_153){
    return this.endpointService.crm_ord_153(params).pipe(map((data) => {
      return data.data;
    }))
  }

  getAllWorkOrders(params : CRM_ORD_161){
    return this.endpointService.crm_ord_161(params).pipe(map((data) => {
      return data.data;
    }))
  }

  addOrderAttachment(file: File, orderId: number): Observable<OrderResponse> {
    const formData = new FormData();

    // Add file with a specific name that matches iOS MIME types
    const fileName = file.name.toLowerCase();
    const isHeic = fileName.endsWith('.heic') || fileName.endsWith('.heif');

    // Convert HEIC to JPEG if needed (iOS common format)
    if (isHeic) {
      // You'll need to add heic2any library for this
      // For now, we'll just warn the user
      console.warn('HEIC format detected, this may cause issues on some devices');
    }

    formData.append('attachment', file);
    formData.append('order_id', orderId.toString());

    return this.endpointService.crm_ord_173({
      order_id: orderId,
      attachment: file
    }).pipe(
      map(response => response.data), // Extract the OrderResponse from PostResponse
      catchError(error => {
        console.error('Upload error:', error);
        return throwError(() => error);
      })
    );
  }

  deleteOrderAttachment(attachmentId: number, orderId: number) {
    let payload: CRM_ORD_174 = {
      attachment_id: attachmentId,
      order_id: orderId,
    }
    return this.endpointService.crm_ord_174(payload).pipe(map((data) => {
      return data
    }))
  }

  getAttachmentFile(attachmentId: number, orderId: number) {
    let payload: CRM_ORD_175 = {
      attachment_id: attachmentId,
      order_id: orderId
    }
    return this.endpointService.crm_ord_175(payload).pipe(map((data) => {
      return data
    }))
  }

  createIncident(params: CRM_ORD_180) {
    return this.endpointService.crm_ord_180(params).pipe(map((data) => {
      return data.data
    }))
  }

  addIncidentAttachment(params: CRM_ORD_181) {
    return this.endpointService.crm_ord_181(params).pipe(map((data) => {
      return data
    }))
  }

  getIncidents(params: CRM_ORD_182) {
    return this.endpointService.crm_ord_182(params).pipe(map((data) => {
      return data.data
    }))
  }


  // --------------------------------------- | Refresh | ---------------------------------------

  refreshOrder(order: OrderResponse) {
    this.currentOrder = order;
    this.orderSource.next(order);
  }


  // --------------------------------------- | Setters | ---------------------------------------

  // Update the order object
  // setOrder(order: OrderResponse, selectedCompanyId: string, source: string) {
  //   if (order.company_id && order.company_id !== selectedCompanyId) {
  //     order.contractorView = true;
  //   }
  //   order.source_tag = source;
  //   this.currentOrder = order;
  //   this.orderSource.next(order);
  // }

  setOrder(order: OrderResponse) {
    this.orderSource.next(order);
  }



  fetchAndUpdateOrder(orderId: number) {
    console.log(`fetchAndUpdateOrder called for orderId: ${orderId}`);

    // Check token before making the request using the injected TokenService
    console.log('Token before fetching order:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    const orderParams: CRM_ORD_29 = {
      order_id: orderId
    };

    this.endpointService.crm_ord_29(orderParams).pipe(
      map((response) => response.data[0])
    ).subscribe({
      next: (order: OrderResponse) => {
        console.log('Order fetched successfully:', order ? 'Order exists' : 'Order is null');

        // Check token after successful order fetch
        console.log('Token after fetching order:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

        if (order) {
          this.refreshOrder(order);
          console.log('Order refreshed in BehaviorSubject');
        } else {
          console.log('Order not found, navigating to orders-overview');
          this.route.navigate(['/orders-overview']);
        }
      },
      error: (error) => {
        console.error('Failed to fetch order', error);

        // Check token after error
        console.log('Token after fetch error:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

        this.route.navigate(['/orders-overview']);
      }
    });
  }

  updateSchedule(schedule: OrderScheduleResponse){
    this.scheduleSource.next(schedule)
  }

  fetchAndUpdateSchedule(scheduleId: number){
    // this.getOrderScheduleById(scheduleId).subscribe((data) => {
    //   this.updateSchedule(data[0])
    // })
  }

  getOrder(): OrderResponse | null {
    return this.currentOrder;
  }



  updateObservable(orderId: number) {
    this.getOrderById(orderId).subscribe({
      next: (order: OrderResponse) => {
        this.currentOrder = order;
        this.orderSource.next(order);
      },
      error: (error) => console.error(`Failed to update order with ID ${orderId}:`, error)
    });
  }


}

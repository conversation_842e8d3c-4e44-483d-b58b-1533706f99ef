import { Injectable } from '@angular/core';
import {CRM_PRD_30, CRM_PRD_48} from "../models/input/input.service";
import {EndpointService} from "./endpoints.service";
import {map} from "rxjs/operators";

@Injectable({
  providedIn: 'root'
})
export class ProductService {

  constructor(private endpointService: EndpointService) { }

  getProductInformation(product_id: number, company_id: string) {
    let params: CRM_PRD_48 = {
      company_id: company_id,
      product_id: product_id,
    }
    return this.endpointService.crm_prd_48(params).pipe(map((data)=>{
      return data.data;
    }))
  }

  getProductUpsellProducts(params: CRM_PRD_30) {
    return this.endpointService.crm_prd_30(params).pipe(map((data)=> data.data));
  }

}

import { Injectable } from '@angular/core';
import { countries, hasFlag } from 'country-flag-icons';

@Injectable({
  providedIn: 'root'
})
export class CountryService {
  private allowedCountries = ['NO', 'SE', 'DK', 'GB', 'US'];

  private countries = countries
    .filter(code => this.allowedCountries.includes(code) && hasFlag(code))
    .map(code => ({
      name: new Intl.DisplayNames(['en'], { type: 'region' }).of(code),
      iso2: code,
      dialCode: this.getDialCode(code)
    }));

  getCountries() {
    return this.countries;
  }

  private getDialCode(countryCode: string): string {
    const dialCodes: {[key: string]: string} = { // Add index signature to dialCodes object
      NO: '47',
      SE: '46',
      DK: '45',
      GB: '44',
      US: '1'
    };
    return dialCodes[countryCode] || '';
  }
}

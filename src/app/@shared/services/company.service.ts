import { Injectable } from '@angular/core';
import {map} from "rxjs/operators";
import {CRM_COY_1, CRM_COY_10, CRM_COY_18, CRM_COY_23, CRM_ORD_43} from "../models/input/input.service";
import {EndpointService} from "./endpoints.service";
import { StorageService } from '../../@core/@core/services/storage.service';
import {CompanyResponse} from "../models/company/company.module";

@Injectable({
  providedIn: 'root'
})
export class CompanyService {

  constructor(private endpointService: EndpointService, private storageService: StorageService) { }

  getCompanyDataByID(payload: CRM_COY_1) {
    return this.endpointService.crm_coy_1(payload).pipe(map((data) => {
      return data.data;
    }))
  }


  // getCompanyDataByCustomer(params : CRM_ORD_43) {
  //   return this.endpointService.crm_ord_43(params).pipe(map((data) => {
  //     return data.data;
  //   }))
  // }

  getCompanyPrivacyPolicy(companyId: string) {
    let params: CRM_COY_18 = {
      company_id: companyId,
    }
    return this.endpointService.crm_coy_18(params).pipe(map((data) => {
      return data.data.privacy_policy;
    }))
  }

  getCompanyTerms(companyId: string) {
    let params: CRM_COY_10 = {
      company_id: companyId,
    }
    return this.endpointService.crm_coy_10(params).pipe(map((data) => {
      return data.data.terms;
    }))
  }

  getCompanyGeneralSettings() {
    let params: CRM_COY_23 = {
      company_id: this.storageService.getSelectedCompanyId(),
    };

    return this.endpointService.crm_coy_23(params).pipe(map((data) => data.data));
  }
}

.payment-container{
  padding: 1rem 1rem 1rem 1rem;
}

ngb-toast {
  z-index: 10500 !important; /* Ensure it appears above other content */
}

.toast-header {
  color: white !important;
}

.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* Ensures the container spans the full viewport height */
  padding: 100px 0 0 0!important;
}

.content {
  flex: 1; /* Makes the content take up the remaining space */
}

app-footer {
  margin-top: auto;
}


<div class="page-container">
  <div class="content">
<app-top-navbar [showBackButton]="true" [companyData]="companyData" [order]="order"></app-top-navbar>

<app-page-loading-spinner *ngIf="isPageLoading"></app-page-loading-spinner>

@if (showToast) {
  <div class="toast-container position-fixed bottom-0 end-0 p-3">
    <ngb-toast
      class="bg-danger text-white"
      (hidden)="showToast = false">
      {{ 'toast.paymentExpiredMessage' | translate }}
    </ngb-toast>
  </div>
}

<!-- Display payment list if a payment hasn't been selected -->
<div *ngIf="!paymentSelectedFlag">
  <div class="col-12 col-md-7 p-0">
    <div class="payment-container" *ngIf="order">
      <div class="mb-2">
        {{ "order-details.title" | translate }} #{{ order.order_number }}
      </div>
      <div class="mb-2">
        <h4>{{ "payment.headerTitle" | translate }}</h4>
        <!-- Bind the paymentSelected event -->
        <app-payment-list [orderPayments]="validPayments" (paymentSelected)="onPaymentSelected($event)"></app-payment-list>
      </div>
    </div>
  </div>
</div>

<!-- proceed with payment if a payment has been selected -->
<div class="row m-0" *ngIf="!isPageLoading && order && paymentSelectedFlag">
  <div class="col-12 col-md-7 p-0">
    <div class="payment-container" *ngIf="order">
      <div class="mb-2">
        {{ "order-payment.paymentId" | translate }} #{{ selectedPayment?.payment_id }}
      </div>
      <h4>{{ "payment.header" | translate }}</h4>

      <div class="border rounded-4 mb-3">
        <div class="component-container">
          <div [id]="'orderLines'" class="mt-2">
            <h6 class="mb-3">{{ "order-summary.heading" | translate }}</h6>
            <!--      <div class="top" *ngIf="orderData.> 0">-->
            <div class="row header">
              <div class="col title">{{ "order-summary.description" | translate }}</div>
              <div class="col title text-end" *ngIf="order.show_prices_inc_vat == 1 && order.hide_payment_data != 1">{{ "order-summary.cost" | translate }}</div>
              <div class="col title text-end" *ngIf="order.show_prices_inc_vat == 0 && order.hide_payment_data != 1">{{ "order-summary.cost" | translate }} {{"order-summary.showIncVatLabel" | translate}}</div>
            </div>
          </div>
          <hr class="my-2">
          <app-order-lines [showBorder]="false" [showHeader]="false" [orderLines]="selectedPayment!.order_lines" [template]="true"></app-order-lines>
        </div>
      </div>
<!--      <app-payment-order-lines [paymentData]="selectedPayment" [orderData]="order"></app-payment-order-lines>-->

<!--&lt;!&ndash;    <div class="mb-4">&ndash;&gt;-->
<!--&lt;!&ndash;    <app-payment-order-lines></app-payment-order-lines>&ndash;&gt;-->
<!--&lt;!&ndash;    </div>&ndash;&gt;-->

  <div *ngIf="order.order_id && pollingCompleted" class="d-block">
    <app-payment-card [orderData]="order" [selectedPayment]="selectedPayment"></app-payment-card>
  </div>

  <div *ngIf="!pollingCompleted" class="d-flex align-items-center flex-column">
  <app-loader [isLoading]="!pollingCompleted"></app-loader>
  <p>{{ 'payment.pollingMessage' | translate }} {{ countdown }}s</p>
  </div>

  <!-- Payment component when paid -->
  <div class="mt-3" *ngIf="order.payment_status_id == 3" class="d-block d-md-none">
    <div class="alert alert-success d-flex align-items-center flex-column" role="alert">
      <h5 class="alert-heading mb-4">Betalingen ble godkjent</h5>
      <button class="btn btn-primary" (click)="navigateToOrder()"> Gå tilbake til ordren</button>
    </div>
  </div>

<!--     Payment component when paid -->
    <div class="mt-3" *ngIf="order.payment_status_id == 3">
      <div class="alert alert-danger" role="alert">
        <h5 class="alert-heading mb-4">Betalingen feilet</h5>
        <p>Feilmeldingen er: </p>
      </div>
    </div>
  <div *ngIf="orderSchedule.order_schedule_id">
  <app-payment-card [orderSchedule] = "orderSchedule" ></app-payment-card>
  </div>

  </div>
</div>
<!--  <div class="col-12 col-md-5 d-none d-md-block" style="margin-top: 165px;">-->
<!--    <div *ngIf="order.order_id">-->
<!--      <app-payment-card *ngIf="order.payment_status_id != 3" [orderData]="order"></app-payment-card>-->
<!--    </div>-->


<!--    &lt;!&ndash; Payment component when paid &ndash;&gt;-->
<!--    <div *ngIf="order.payment_status_id == 3">-->
<!--      <div class="alert alert-success d-flex align-items-center flex-column" role="alert">-->
<!--        <h5 class="alert-heading mb-4">Betalingen ble godkjent</h5>-->
<!--        <button class="btn btn-primary" (click)="navigateToOrder()"> Gå tilbake til ordren</button>-->
<!--      </div>-->
<!--    </div>-->
    </div>
  </div>

    <!-- Footer -->
    <app-footer></app-footer>
  </div>

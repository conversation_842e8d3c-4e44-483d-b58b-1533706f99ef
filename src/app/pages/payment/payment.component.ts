import {ChangeDetectorRef, Component, Input, OnInit} from '@angular/core';
import {TopNavbarComponent} from "../../@shared/components/top-navbar/top-navbar.component";
import {TranslateModule} from "@ngx-translate/core";
import {CrewResponse, OrderResponse, OrderScheduleResponse} from "../../@shared/models/order/order.module";
import {OrderService} from "../../@shared/services/order.service";
import {CompanyService} from "../../@shared/services/company.service";
import {StorageService} from "../../@core/@core/services/storage.service";
import {ActivatedRoute, NavigationEnd, Router} from "@angular/router";
import {UtilsService} from "../../@core/@core/utils/utils.service";
import {CompanyResponse} from "../../@shared/models/company/company.module";
import {SummaryComponent} from "../../@shared/components/summary/summary.component";
import {CommonModule, NgIf} from "@angular/common";
import {PaymentCardComponent} from "./payment-card/payment-card.component";
import { ScreenSizeService } from '../../@shared/services/screen-size.service';
import * as Sentry from "@sentry/angular";
import { FooterComponent } from "../../@shared/components/footer/footer.component";
import { filter, interval, Subject, switchMap, take, takeUntil, takeWhile } from 'rxjs';
import { PaymentService } from '../../@shared/services/payment.service';
import {PaymentOrderLinesComponent} from "./payment-order-lines/payment-order-lines.component";
import {OrderPaymentResponse} from "../../@shared/models/payment/payment.module";
import {PaymentListComponent} from "../order/order-details/components/payment-list/payment-list.component";
import {
  PageLoadingSpinnerComponent
} from "../../@shared/components/page-loading-spinner/page-loading-spinner.component";
import {CRM_COY_1, CRM_PAY_35} from '../../@shared/models/input/input.service';
import { NgbToast, NgbToastModule } from '@ng-bootstrap/ng-bootstrap';
import { LoaderComponent } from "../../@shared/components/loader/loader.component";
import {OrderLinesComponent} from "../../@shared/components/order-lines/order-lines.component";

@Component({
  selector: 'app-payment',
  standalone: true,
  imports: [
    TopNavbarComponent,
    TranslateModule,
    SummaryComponent,
    NgbToastModule,
    NgIf,
    PaymentCardComponent,
    CommonModule,
    FooterComponent,
    PaymentOrderLinesComponent,
    PaymentListComponent,
    PageLoadingSpinnerComponent,
    LoaderComponent,
    OrderLinesComponent
  ],
  templateUrl: './payment.component.html',
  styleUrl: './payment.component.scss'
})
export class PaymentComponent implements OnInit{
  companyData?: CompanyResponse;
  orderId : number = 0;
  crew : CrewResponse = {} as CrewResponse;
  isPageLoading: boolean = false;
  isMobile!: boolean;
  paymentSelectedFlag: boolean = false;
  paymentSelected: boolean = false;
  validPayments: OrderPaymentResponse[] = [];
  orderPayments: OrderPaymentResponse[] = [];
  selectedPayment: OrderPaymentResponse | null = null;
  showToast: boolean = false;
  pollingCompleted: boolean = true;
  countdown: number = 50;
  private unsubscribe$ = new Subject<void>();
  @Input() order: OrderResponse  = {} as OrderResponse;
  @Input() orderSchedule: OrderScheduleResponse = {} as OrderScheduleResponse;
  constructor( private orderService: OrderService,
               private companyService: CompanyService,
               private storageService: StorageService,
               private activatedRoute: ActivatedRoute,
               public utilsService: UtilsService,
               private cdr: ChangeDetectorRef,
               private paymentService: PaymentService,
               private screenSizeService: ScreenSizeService,
               private router: Router,
               ){}


  ngOnInit() {
    this.isPageLoading = true;
    const idParam = this.activatedRoute.snapshot.paramMap.get('orderId');
    if (idParam) {
      this.orderId = parseInt(idParam, 10);
      if (!isNaN(this.orderId)) {
        this.orderService.fetchAndUpdateOrder(this.orderId);
        this.orderService.order$.subscribe(order => {
          this.order = order;
          if (this.hasOrderData(order)) {
            this.isPageLoading = false;
            this.getCompanyData();
          }
        });

        this.getPayments();

      } else {
        console.error('Invalid order ID:', idParam);
        this.isPageLoading = false;
      }
    } else {
      console.error('No order ID found in route parameters');
      this.isPageLoading = false;
    }

    this.screenSizeService.isMobile$.subscribe(isMobile => {
      this.isMobile = isMobile;
    });

    Sentry.getCurrentScope().setTags({
      "order_id": this.orderId,
      "user_id": this.storageService.getUser().user_id,
    });
  }


  getCompanyData() {

    const params: CRM_COY_1 = {
      company_id: this.order.company_id
    };

    this.companyService.getCompanyDataByID(params).subscribe({
      next: (res: CompanyResponse) => {
        this.companyData = res;
        document.documentElement.style.setProperty('--primary-color', res.company_color);
      }
    });
  }

  getPayments() {
  const params: CRM_PAY_35 = {
    order_id: this.orderId,
  };

    this.paymentService.getPayments(params).subscribe(res => {
      this.orderPayments = res;

      // Filter payments that have valid status (3 or 6)
      this.validPayments = this.orderPayments.filter(payment =>
        ![3, 7, 11, 10].includes(payment.payment_status_id) && !payment.disable_customer_portal
      );

      // Check if the length of validPayments is 1, or if all payments are valid
      if (this.validPayments.length === 1) {
        this.onPaymentSelected(this.validPayments[0]);
      } else {
        this.restoreSelectedPayment();
      }

      this.getRivertyPaymentStatus();
    });

  }


  restoreSelectedPayment() {
    // Check for selectedPaymentId in query params
    this.activatedRoute.queryParams.subscribe(params => {
      const selectedPaymentId = params['selectedPaymentId'];
      if (selectedPaymentId) {

        // Convert selectedPaymentId to a number before comparing
        const selectedPaymentIdNumber = parseInt(selectedPaymentId, 10);

        this.selectedPayment = this.orderPayments.find(
          payment => payment.payment_id === selectedPaymentIdNumber
        ) || null;

        this.paymentSelectedFlag = !!this.selectedPayment;


        if (this.selectedPayment && (this.orderPayments.length != 1 || this.validPayments.length != 1)) {
          this.onPaymentSelected(this.selectedPayment);
        }
      }
    });
  }


  getRivertyPaymentStatus() {
    console.log('Selected payment from getRiverty', this.selectedPayment);

    this.activatedRoute.queryParams.pipe(
      switchMap(params => {
        const fromRiverty: boolean = params['fromRiverty'];
        if (fromRiverty || (this.selectedPayment?.payment_method_id === 11 && ![3, 5].includes(this.selectedPayment?.payment_status_id))) {
          const fetchParams = { order_id: this.orderId, payment_id: this.selectedPayment!.payment_id };
          return this.paymentService.fetchRivertyOrder(fetchParams);
        } else {
          return [];
        }
      }),
      takeUntil(this.unsubscribe$)
    ).subscribe({
      next: (res) => {
        const paymentsStatus = res.riverty_response.orderDetails.status;
        if (paymentsStatus === 'Expired') {
          this.triggerToast();
        } else if (paymentsStatus === 'Pending') {

          this.pollingCompleted = false; // Start polling
          this.countdown = 50; // Reset countdown (10 polls x 5 seconds each)

          const polling$ = interval(5000).pipe( // Poll every 5 seconds
            switchMap(() => {
              this.countdown -= 5; // Decrease countdown by 5 seconds per poll
              return this.paymentService.fetchRivertyOrder({
                order_id: this.orderId,
                payment_id: this.selectedPayment!.payment_id,
              });
            }),
            take(10), // Stop polling after 10 emissions (50 seconds)
            takeUntil(this.unsubscribe$)
          );

          polling$.subscribe({
            next: (pollRes) => {
              const polledStatus = pollRes.riverty_response.orderDetails.status;

              if (polledStatus === 'Approved') {
                this.pollingCompleted = true; // Stop polling
                this.orderService.updateObservable(this.orderId); // Update the order
                this.navigateToOrder(); // Navigate to the order page
                return;
              } else if (polledStatus === 'Expired') {
                this.pollingCompleted = true; // Stop polling
                this.triggerToast();
                return;
              }
            },
            error: (error) => {
              console.error('Error during polling', error);
            },
            complete: () => {
              if (!this.pollingCompleted) {
                this.showToast = true;
                this.paymentService.voidRivertyPayment({
                  payment_id: this.selectedPayment!.payment_id,
                }).subscribe({
                  next: (res) => {
                    console.log('Riverty payment voided after polling ended:', res);
                  },
                  error: (error) => {
                    console.error('Error voiding Riverty payment after polling ended:', error);
                  },
                });
                this.pollingCompleted = true; // Stop polling after completion
              }
            },
          });
        } else if (paymentsStatus === 'Accepted') {
          this.navigateToOrder();
        }

        this.orderService.updateObservable(this.orderId); // Updates the order$ observable with the new data
      },
      error: (error) => {
        console.error('Error fetching order data', error);
      },
    });
  }


  onPaymentSelected(payment: OrderPaymentResponse) {
    this.selectedPayment = payment;
    this.paymentSelectedFlag = true;

    // Navigate by adding the `selectedPaymentId` parameter on top of existing parameters
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { selectedPaymentId: payment.payment_id },
      queryParamsHandling: 'merge', // Ensure only `selectedPaymentId` is added
    });

    // Log the resulting URL for debugging
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      console.log('Updated URL:', this.router.url);
    });
  }

  hasOrderData(order: OrderResponse | null): boolean {
    return order !== null && Object.keys(order).length > 0;
  }

  navigateToOrder() {
    this.router.navigate(['/orders', this.orderId]);
  }


    // Function to toggle the toast message
    triggerToast() {
      this.showToast = true;
    }

    // Handle when the toast is hidden
    onToastHidden() {
      this.showToast = false;
    }

}


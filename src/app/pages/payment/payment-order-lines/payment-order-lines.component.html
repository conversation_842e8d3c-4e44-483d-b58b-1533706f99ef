<div class="border rounded-4 mb-3" *ngIf="paymentData && orderData">
  <div class="component-container">

    <div [id]="'orderLines'" class="mt-2">
      <h6 class="mb-3">{{ "order-summary.heading" | translate }}</h6>
      <!--      <div class="top" *ngIf="orderData.> 0">-->
      <div class="row header">
        <div class="col title">{{ "order-summary.description" | translate }}</div>
        <div class="col title text-end" *ngIf="orderData.show_prices_inc_vat == 1 && orderData.hide_payment_data != 1">{{ "order-summary.cost" | translate }}</div>
        <div class="col title text-end" *ngIf="orderData.show_prices_inc_vat == 0 && orderData.hide_payment_data != 1">{{ "order-summary.cost" | translate }} {{"order-summary.showIncVatLabel" | translate}}</div>
      </div>
    </div>

    <hr class="my-2">

    <div id="listedOrderLines" *ngFor="let line of paymentData.order_lines">
      <div class="row align-items-center mb-2">
        <!-- Left Side: Product Details -->
        <div class="col-7">
          <p class="mb-0 title">{{ line.order_line_name }}</p>
          <p class="mb-0">kr {{ line.unit_price_inc_vat }} x {{ line.quantity }} {{ line.unit_abbreviation }}</p>
          <p class="mb-0 font-size-xs" *ngIf="line.comment">{{ line.comment }}</p>
        </div>

        <div class="col-5 text-end">
          <span class=""> kr {{utilsService.formatCurrency(orderData.show_prices_inc_vat == 1 ? line.calculated_total_price_inc_vat : line.calculated_total_price_ex_vat, true)}},-</span>
        </div>
      </div>
    </div>

    <hr>

    <!--  Discount  -->
    <div *ngIf="paymentData.total_discount_amount_inc_vat !=  0" class="d-flex justify-content-between border-bottom mb-1 pb-1">
      <div class="">{{"order-summary.discount" | translate}}: <span *ngIf="paymentData.total_discount_amount_inc_vat"> </span>
      </div>
      <div class="">{{utilsService.formatCurrency(paymentData.total_discount_amount_inc_vat!)}}</div>
    </div>

    <!--  Total ex vat  -->
    <div class="d-flex justify-content-between mt-1">
      <div class="">{{"order-summary.total" | translate}} {{"order-summary.showExVatLabel" | translate}}</div>
      <div class="">kr {{utilsService.formatCurrency(paymentData.total_amount_ex_vat!)}},-</div>
    </div>

    <!--  VAT Breakdown  -->
   <div class="d-flex justify-content-between mt-1">
    <div>{{"payment.summary.vat" | translate}}</div>
     <div class="">kr {{utilsService.formatCurrency(paymentData.total_amount_inc_vat - paymentData.total_amount_ex_vat) }},-</div>
</div>

    <!--  Total inc vat  -->
    <div class="d-flex justify-content-between">
      <div class="fw-bold">{{"order-summary.total" | translate}}</div>
      <div class="fw-bold">kr {{utilsService.formatCurrency(paymentData.total_amount_inc_vat!!)}},-</div>
    </div>
<!--    <div class="mt-3">-->
<!--      <app-order-lines-summary></app-order-lines-summary>-->
<!--    </div>-->



  </div>
</div>

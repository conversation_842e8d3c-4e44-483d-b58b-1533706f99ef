import {Component, Input} from '@angular/core';
import {<PERSON><PERSON><PERSON>O<PERSON>, NgIf} from "@angular/common";
import {
  OrderLinesSummaryComponent
} from "../../../@shared/components/order-lines-summary/order-lines-summary.component";
import {TranslateModule} from "@ngx-translate/core";
import {CRM_PAY_35} from "../../../@shared/models/input/input.service";
import {PaymentService} from "../../../@shared/services/payment.service";
import {OrderService} from "../../../@shared/services/order.service";
import {OrderResponse} from "../../../@shared/models/order/order.module";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {OrderPaymentResponse} from "../../../@shared/models/payment/payment.module";

@Component({
  selector: 'app-payment-order-lines',
  standalone: true,
  imports: [
    <PERSON>ForOf,
    NgIf,
    OrderLinesSummaryComponent,
    TranslateModule
  ],
  templateUrl: './payment-order-lines.component.html',
  styleUrl: './payment-order-lines.component.scss'
})
export class PaymentOrderLinesComponent {
  @Input() paymentData?: OrderPaymentResponse | null;
  @Input() orderData?: OrderResponse;
  @Input() isRepeating: boolean = false

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }
}

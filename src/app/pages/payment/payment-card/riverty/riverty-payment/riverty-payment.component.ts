import {Component, EventEmitter, Inject, Input, Output} from '@angular/core';
import {AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators} from "@angular/forms";
import {AddressResponse, OrderLineResponse, OrderResponse, OrderScheduleResponse, StageAddressUnitResponse, WorkOrderAddressResponse} from "../../../../../@shared/models/order/order.module";
import {OrderPaymentResponse, PaymentMethod} from "../../../../../@shared/models/payment/payment.module";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {UtilsService} from "../../../../../@core/@core/utils/utils.service";
import {CommonModule, DOCUMENT} from "@angular/common";
import {CRM_PAY_20, CRM_PAY_21} from "../../../../../@shared/models/input/input.service";
import {TranslateModule} from "@ngx-translate/core";
import {LoaderComponent} from "../../../../../@shared/components/loader/loader.component";
import { OrderService } from '../../../../../@shared/services/order.service';


export function ssnValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const isValid = /^\d{11}$/.test(control.value);
    return isValid ? null : { invalidSsn: { value: control.value } };
  };
}

export function postalCodeValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const isValid = /^\d{4}$/.test(control.value);
    return isValid ? null : { invalidPostalCode: { value: control.value } };
  };
}


@Component({
  selector: 'app-riverty-payment',
  standalone: true,
  imports: [TranslateModule, CommonModule, FormsModule, ReactiveFormsModule, LoaderComponent],
  templateUrl: './riverty-payment.component.html',
  styleUrl: './riverty-payment.component.scss'
})
export class RivertyPaymentComponent {
  @Input() selectedPayment: OrderPaymentResponse | null = null;
  @Input() orderData: OrderResponse = {} as OrderResponse;
  @Input() orderSchedule: OrderScheduleResponse = {} as OrderScheduleResponse;
  @Input() subscription: boolean = false;
  selectedPaymentMethodType: string = ''; // Initialize as an empty string
  selectedPaymentMethod: PaymentMethod | null = null;

  selectedPaymentMethodDetails: PaymentMethod | null = null;
  selectedPaymentMethodId: number | null = null; // Initialize as null
  paymentMethods: PaymentMethod[] = [];
  paymentSelected: boolean = false;
  paymentForm: FormGroup = {} as FormGroup;
  submitted: boolean = false;
  taskableOrderLine: OrderLineResponse = {} as OrderLineResponse;
  address: WorkOrderAddressResponse = {} as WorkOrderAddressResponse;
  errorMessages: string[] = [];
  isLoading = false;
  @Output() updateOrderData: EventEmitter<OrderResponse | null> = new EventEmitter<OrderResponse | null>();


  constructor(
    private paymentService: PaymentService,
    private fb: FormBuilder,
    public utilsService: UtilsService,
    private orderService: OrderService,

    @Inject(DOCUMENT) private document: Document
  ) { }

  ngOnInit(): void {
    this.createEmptyForm();
    this.waitForOrderData();
  }


  createEmptyForm(): void {
    this.paymentForm = this.fb.group({
      first_name: ['', Validators.required],
      last_name: ['', Validators.required],
      ssn: [null, [Validators.required, ssnValidator()]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', Validators.required],
      useDeliveryAddress: [true],
      address: this.fb.group({
        street: ['', Validators.required],
        streetNumber: ['', Validators.required],
        postalCode: ['', [Validators.required, postalCodeValidator()]],
        postalPlace: ['', Validators.required]
      })
    });
  }

  waitForOrderData(): void {
    // Safely access work_orders and addresses
    const workOrders = this.orderData?.work_orders || [];
    const firstWorkOrder = workOrders[0];
    const addresses = firstWorkOrder?.addresses || [];
    this.address = addresses[0] || null; // Set to null if no addresses exist

    // Proceed with form creation and loading payment methods
    this.createForm();
    this.loadPaymentMethods();
  }

  createForm(): void {
    let firstName = '';
    let lastName = '';

    // Handle splitting recipient name
    if (this.orderData.payment_recipient?.name) {
      const nameParts = this.orderData.payment_recipient.name.split(' ');
      firstName = nameParts.slice(0, -1).join(' '); // All words except the last one as the first name
      lastName = nameParts[nameParts.length - 1] || ''; // Last word as the last name
    }

    // Handle when address is null
    const address = this.address || {}; // Fallback to an empty object if address is null
    const street = address.street ?? ''; // Use empty string if street is undefined or null
    const streetNumber = (address.number ?? '') + (address.letter ?? ''); // Concatenate number and letter
    const postalCode = address.postal_code ?? ''; // Use empty string if postal_code is undefined or null
    const postalPlace = address.city ?? ''; // Use empty string if city is undefined or null

    // Patch form values
    this.paymentForm.patchValue({
      first_name: firstName,
      last_name: lastName,
      ssn: null,
      email: this.orderData.payment_recipient?.email ?? '',
      phone: this.orderData.payment_recipient?.phone ?? '',
      address: {
        street: street,
        streetNumber: streetNumber,
        postalCode: postalCode,
        postalPlace: postalPlace
      }
    });
  }

  loadPaymentMethods(): void {
    const payload: CRM_PAY_20 = {
      payment_id: this.selectedPayment!.payment_id,
      order_id: this.selectedPayment!.order_id!,
    };
    this.paymentService.getRivertyPaymentMethods(payload).subscribe({
      next: (response) => {
        this.paymentMethods = response.paymentMethods;
      },
      error: (error) => {
        console.error('Error fetching payment methods', error);
      }
    });
  }

  selectPaymentMethod(method: PaymentMethod, index: number): void {
    const uniqueType = `${method.type}:${index}`;

    if (this.selectedPaymentMethodType === uniqueType) {
      this.selectedPaymentMethodType = ''; // Reset to empty
      this.selectedPaymentMethodId = null;
      this.selectedPaymentMethodDetails = null;
    } else {
      this.selectedPaymentMethodType = uniqueType;
      this.selectedPaymentMethodId = index;
      this.selectedPaymentMethodDetails = method;
    }
  }

  authorizePayment(): void {
    this.submitted = true;
    this.errorMessages = [];

    if (this.paymentForm.invalid) {
      return;
    }

    const formData = this.paymentForm.value;
    const params: CRM_PAY_21 = {
      payment_id: this.selectedPayment!.payment_id,
      order_id: this.orderData.order_id,
      payment_type_enum: this.selectedPaymentMethodDetails?.type ?? '',
      number_of_installments: this.selectedPaymentMethodDetails?.installment?.numberOfInstallments,
      profile_no: this.selectedPaymentMethodDetails?.installment?.installmentProfileNumber,
      first_name: formData.first_name,
      last_name: formData.last_name,
      ssn: formData.ssn,
      email: formData.email,
      phone: formData.phone,
      address: {
        street: formData.address.street,
        streetNumber:formData.address.streetNumber,
        postalCode: formData.address.postalCode,
        postalPlace: formData.address.postalPlace,
      }
    };
    this.isLoading = true;
    this.paymentService.authorizeRivertyPayment(params).subscribe({
      next: (response) => {
        if (response.riverty_response.outcome === 'Pending') {
          // Parse the secureLoginUrl
          const redirectUrl = new URL(response.riverty_response.secureLoginUrl);

          // Create a new URL object for the current location
          const merchantUrl = new URL(window.location.href);

          // Append the 'fromRiverty' parameter to the merchantUrl
          merchantUrl.searchParams.append('fromRiverty', 'true');

          // Set the modified merchantUrl as a query parameter in the secureLoginUrl
          redirectUrl.searchParams.set('merchantUrl', merchantUrl.toString());

          // Perform the redirection
          this.document.location.href = redirectUrl.toString();
        } else if (response.riverty_response.outcome === 'Accepted') {
          console.log('Outcome accepted', response);
          this.orderService.fetchAndUpdateOrder(this.orderData.order_id);
          this.document.location.href = `/orders/${this.orderData.order_id}`;
        } else if (response.riverty_response.riskCheckMessages && response.riverty_response.riskCheckMessages.length > 0) {
          console.log('else ifRisk check messages', response.riverty_response);
          this.document.location.href = response.riverty_response.secureLoginUrl + window.location.href + "&fromRiverty=true";
          // Create a Set to store unique customerFacingMessages
          const uniqueMessages = new Set();
          response.riverty_response.riskCheckMessages.forEach(msg => uniqueMessages.add(msg.customerFacingMessage));
          // Convert the Set back to an array for errorMessages
          this.errorMessages = Array.from(uniqueMessages) as string[];
        }
        this.updateOrderData.emit(response.order);

        this.isLoading = false;

      },
      error: (error) => {
        console.error('Error authorizing payment', error);
        this.errorMessages = ['An unexpected error occurred. Please try again later.'];
        this.isLoading = false;
      }
    });

  }
}

<div *ngIf="orderData">
  <div class="container p-0">
    <ul class="list-group">
      <ng-container *ngFor="let method of paymentMethods; let i = index">
        <ng-container *ngIf="method.type !== 'Invoice'">
          <li class="list-group-item border-1 mb-3 rounded-4">
            <div class="d-flex justify-content-between align-items-center py-2" (click)="selectPaymentMethod(method, i)" style="cursor: pointer;">
              <div class="d-flex align-items-center">
                <input type="radio"
                       name="paymentMethod"
                       [value]="method.type + ':' + i"
                       [(ngModel)]="selectedPaymentMethodType">              <div class="ms-2">
                  <h6 class="mb-0">{{ method.title || method.type }}
                    <span *ngIf="method.type === 'Installment'">- {{method.installment?.numberOfInstallments}} {{"riverty.installment" | translate}} </span>
                  </h6>
                  <p class="mb-0 text-muted" style="font-size: 14px;">{{ method.tag }}
                    <span *ngIf="method.type === 'Installment'">- {{method.installment?.installmentAmount}} {{"riverty.prMonth" | translate}}</span>
                  </p>
                </div>
              </div>
              <img *ngIf="method.logo" [src]="method.logo" alt="{{ method.title || method.type }}" class="ml-3" style="height: 30px;">
            </div>

            <!-- Details Section -->
            <div *ngIf="selectedPaymentMethodDetails && selectedPaymentMethodType === (method.type + ':' + i)" class="">
              <div class="container mt-1">
                <form [formGroup]="paymentForm" (ngSubmit)="authorizePayment()">
<!--                  <div class="form-container" *ngIf="orderData.payment_recipient.is_private === 1">-->
                    <div class="row mb-3">
                      <div class="col">
                        <label for="first_name" class="form-label">{{"riverty.firstName" | translate}}</label>
                        <input id="first_name" formControlName="first_name" class="form-control" placeholder="Enter first name">
                        <div *ngIf="submitted && paymentForm.get('first_name')?.invalid" class="text-danger">
                          {{"riverty.firstNameRequired" | translate}}
                        </div>
                      </div>
                      <div class="col">
                        <label for="last_name" class="form-label">{{"riverty.lastName" | translate}}</label>
                        <input id="last_name" formControlName="last_name" class="form-control" placeholder="Enter last name">
                        <div *ngIf="submitted && paymentForm.get('last_name')?.invalid" class="text-danger">
                          {{"riverty.lastNameRequired" | translate}}
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="snn" class="form-label">{{"riverty.identificationNumber" | translate}}</label>
                      <input id="snn" formControlName="ssn" type="text" class="form-control">
                      <div *ngIf="submitted && paymentForm.get('ssn')?.invalid" class="text-danger">
                        {{"riverty.identificationNumberRequired" | translate}}
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="email" class="form-label">{{"riverty.email" | translate}}</label>
                      <input id="email" formControlName="email" type="email" class="form-control" placeholder="Enter email">
                      <div *ngIf="submitted && paymentForm.get('email')?.invalid" class="text-danger">
                        {{"riverty.emailRequired" | translate}}
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="phone" class="form-label">{{"riverty.phone" | translate}}</label>
                      <input id="phone" formControlName="phone" type="tel" class="form-control" placeholder="Enter phone">
                      <div *ngIf="submitted && paymentForm.get('phone')?.invalid" class="text-danger">
                        {{"riverty.phoneRequired" | translate}}
                      </div>
                    </div>
<!--                    <div class="form-check mb-3">-->
<!--                      <input id="useDeliveryAddress" type="checkbox" formControlName="useDeliveryAddress" class="form-check-input">-->
<!--                      <label for="useDeliveryAddress" class="form-check-label">{{"riverty.useDeliveryAddress" | translate}}</label>-->
<!--                    </div>-->
                    <div formGroupName="address" class="border rounded p-3 mb-3">
                      <h5>{{"riverty.addressHeader" | translate}}</h5>
                      <div class="row mb-3">
                        <div class="col-9">
                          <label for="street" class="form-label">{{"riverty.street" | translate}}</label>
                          <input id="street" formControlName="street" class="form-control" placeholder="Enter street">
                          <div *ngIf="submitted && paymentForm.get('address.street')?.invalid" class="text-danger">
                            {{"riverty.streetRequired" | translate}}
                          </div>
                        </div>
                        <div class="col-3">
                          <label for="streetNumber" class="form-label">{{"riverty.streetNumber" | translate}}</label>
                          <input id="streetNumber" formControlName="streetNumber" class="form-control" placeholder="Enter street number">
                          <div *ngIf="submitted && paymentForm.get('address.streetNumber')?.invalid" class="text-danger">
                            {{"riverty.streetNumberRequired" | translate}}
                          </div>
                        </div>
                      </div>
                      <div class="row mb-3">
                        <div class="col-12 mb-2">
                          <label for="postalCode" class="form-label">{{"riverty.postalCode" | translate}}</label>
                          <input id="postalCode" formControlName="postalCode" class="form-control" placeholder="Enter postal code">
                          <div *ngIf="submitted && paymentForm.get('address.postalCode')?.invalid" class="text-danger">
                            {{"riverty.postalCodeRequired" | translate}}
                          </div>
                        </div>
                        <div class="col-12">
                          <label for="postalPlace" class="form-label">{{"riverty.postalPlace" | translate}}</label>
                          <input id="postalPlace" formControlName="postalPlace" class="form-control" placeholder="Enter postal place">
                          <div *ngIf="submitted && paymentForm.get('address.postalPlace')?.invalid" class="text-danger">
                            {{"riverty.postalPlaceRequired" | translate}}
                          </div>
                        </div>
                      </div>
                    </div>

<!--                  </div>-->
                  <div class="card my-3" *ngIf="selectedPaymentMethodDetails.type === 'Installment'">
                    <div class="card-body">
                      <ul class="list-unstyled">
                        <li>
                          <strong>{{"riverty.numberOfInstallments" | translate}}</strong> {{ selectedPaymentMethodDetails.installment?.numberOfInstallments }}
                        </li>
                        <li>
                          <strong>{{"riverty.installmentAmount" | translate}}</strong> {{ this.utilsService.formatCurrency(selectedPaymentMethodDetails.installment?.installmentAmount ?? 0) }} kr
                        </li>
                        <li>
                          <strong>{{"riverty.intresertRate" | translate}}</strong> {{ selectedPaymentMethodDetails.installment?.interestRate }}%
                        </li>
                        <li>
                          <strong>{{"riverty.monthlyFee" | translate}}</strong> {{ this.utilsService.formatCurrency(selectedPaymentMethodDetails.installment?.monthlyFee ?? 0) }} kr
                        </li>
                        <li>
                          <strong>{{"riverty.startupFee" | translate}}</strong> {{ this.utilsService.formatCurrency(selectedPaymentMethodDetails.installment?.startupFee ?? 0) }} kr
                        </li>
                        <li>
                          <strong>{{"riverty.totalAmount" | translate}}</strong> {{ this.utilsService.formatCurrency(selectedPaymentMethodDetails.installment?.totalAmount ?? 0 ) }} kr
                        </li>
                      </ul>
                    </div>
                  </div>

                  <!-- Error Messages -->
                  <div *ngIf="errorMessages.length > 0" class="alert alert-danger" role="alert">
                    <ul>
                      <li *ngFor="let errorMsg of errorMessages">{{ errorMsg }}</li>
                    </ul>
                  </div>
                  <button type="submit" class="btn btn-primary w-100 mb-1 d-flex justify-content-center align-items-center"> {{"riverty.payNowBtn" | translate}} <app-loader class="ms-2" [isLoading]="isLoading"></app-loader></button>
                </form>
              </div>

              <div class="text-center px-2">
                <div style="font-size: 14px;" [innerHTML]="selectedPaymentMethodDetails.legalInfo.text"></div>
                <a style="font-size: 14px;" [href]="selectedPaymentMethodDetails.legalInfo.termsAndConditionsUrl" target="_blank">{{"riverty.termsAndCondition" | translate}}</a><br>
                <a style="font-size: 14px;" [href]="selectedPaymentMethodDetails.legalInfo.privacyStatementUrl" target="_blank">{{"riverty.privacyStatement" | translate}}</a>
              </div>
            </div>
          </li>
        </ng-container>
      </ng-container>
    </ul>
  </div>

  <!-- Payment form  -->
  <div class="" *ngIf="paymentSelected">
  </div>

  <div *ngIf="!orderData">
    Loading order data...
  </div>
</div>

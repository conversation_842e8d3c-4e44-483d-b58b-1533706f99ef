<ul class="list-group">
    <!-- QuickPay -->
    <ng-container *ngIf="enabledPaymentMethods.includes(5)">
      <li class="list-group-item border mb-3 rounded-4">
        <div class="d-flex justify-content-between align-items-center py-2" (click)="onPaymentSelect(5, 'quickpay')" style="cursor: pointer;">
          <div class="d-flex align-items-center">
            <input type="radio" name="paymentMethod" class="mr-3" [(ngModel)]="selectedPaymentMethod" [value]="5">
            <div class="ms-2">
              <h6 class="mb-0">
                <ng-container *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.cardOrVipps" | translate}}</ng-container>
                <ng-container *ngIf="orderSchedule.order_schedule_id">{{"order-payment.card" | translate}}</ng-container>
              </h6>
              <p *ngIf="!orderSchedule.order_schedule_id" class="mb-0 text-muted" style="font-size: 14px;"> {{"order-payment.quickpayTag" | translate}} </p>
              <p *ngIf="orderSchedule.order_schedule_id" class="mb-0 text-muted" style="font-size: 14px;"> {{"order-payment.quickpayTagWithoutVipps" | translate}} </p>
            </div>
          </div>
          <img *ngIf="orderSchedule.order_schedule_id" src="assets/images/payment-icons/VisaMaster.png" width="100px" class="ml-3">
          <img *ngIf="!orderSchedule.order_schedule_id" src="assets/images/visa.png" width="100px" class="ml-3">
        </div>

        <!-- Details Section -->
        <div *ngIf="selectedPaymentMethod === 5 && paymentActive" class="mt-3 p-3 border rounded shadow-sm">
          <p *ngIf="!orderSchedule.order_schedule_id" class="text-muted text-center" style="font-size: 14px;">{{"order-payment.quickpayDetails" | translate}}</p>
          <p *ngIf="orderSchedule.order_schedule_id" class="text-muted text-center" style="font-size: 14px;">{{"order-payment.quickpayDetailsSubscription" | translate}}</p>
          <div class="d-flex justify-content-center mb-2">
            <app-button
            class="w-100"
            [isLoading]="loading"
            buttonClass="btn btn-primary w-100"
            (buttonClick)="continuePayment()">
            <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.pay" | translate}}</span>
            <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>
          </app-button>

<!--          <button class="btn btn-primary w-100" (click)="continuePayment()">-->
<!--              <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.pay" | translate}}</span>-->
<!--              <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>-->
<!--            </button> -->
          </div>
        </div>
      </li>
    </ng-container>

    <!-- Riverty -->
    <ng-container *ngIf="enabledPaymentMethods.includes(11)">
      <app-riverty-payment [selectedPayment]="selectedPayment" [orderData]="orderData" (updateOrderData)="updateOrderData.emit($event)" ></app-riverty-payment>
    </ng-container>

    <!-- Invoice -->
    <ng-container *ngIf="enabledPaymentMethods.includes(10)">
      <li class="list-group-item border mb-3 rounded-4">
        <div class="d-flex justify-content-between align-items-center" (click)="!!selectedPayment?.invoice_sent_at ? onPaymentSelect(10, '') : null" style="cursor: pointer;">
          <div class="d-flex align-items-center py-2">
            <input [disabled]="!!selectedPayment?.invoice_sent_at" type="radio" name="paymentMethod" class="mr-3" [(ngModel)]="selectedPaymentMethod" [value]="10">
            <div class="ms-2">
              <h6 class="mb-0">{{"order-payment.invoice" | translate}}</h6>
              <p class="text-muted m-0" style="font-size: 14px;">{{(!selectedPayment?.invoice_sent_at ? "order-payment.invoiceTag" : "order-payment.invoiceTagSent") | translate}}</p>

              <app-loader *ngIf="selectedPaymentMethod === 10 && loading" class="ms-2"></app-loader>
            </div>
          </div>
        </div>
        <!-- Details Section -->
        <div *ngIf="selectedPaymentMethod === 10 && paymentActive" class="mt-3 p-3 border rounded shadow-sm">

          <!-- Check if email exists -->
          <div *ngIf="enabledPaymentMethods.includes(10) && (orderSchedule?.payment_recipient?.email || orderData?.payment_recipient?.email)">
            <p *ngIf="orderSchedule?.payment_recipient?.email" class="text-muted text-center" style="font-size: 14px;">
              {{"order-payment.invoiceDetails" | translate}} {{orderSchedule.payment_recipient.email}}
            </p>
<!--            <p *ngIf="orderData?.payment_recipient?.email && !orderSchedule?.payment_recipient?.email" class="text-muted text-center" style="font-size: 14px;">-->
<!--              {{"order-payment.invoiceDetails" | translate}} {{orderData.payment_recipient.email}}-->
<!--            </p>-->
            <div class="d-flex justify-content-center mb-2">

              <app-button
              class="w-100"
              [isLoading]="loading"
              buttonClass="btn btn-primary w-100"
              (buttonClick)="continuePayment()">
              <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.sendInvoice" | translate}}</span>
              <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>
            </app-button>
            </div>
          </div>

          <!-- Check if email is missing -->
          <div *ngIf="enabledPaymentMethods.includes(10) && (!orderSchedule?.payment_recipient?.email && !orderData?.payment_recipient?.email)">
            <p class="text-muted text-center" style="font-size: 14px;">{{"order-payment.noEmailText" | translate}}</p>
            <div class="d-flex justify-content-center mb-2">
              <input
                type="email"
                class="form-control me-2"
                [(ngModel)]="newUserEmail"
                placeholder="{{'order-payment.email' | translate}}"
                #emailInput="ngModel"
                required
                email>

                <app-button
                class="w-100"
                [isLoading]="loading"
                buttonClass="btn btn-primary w-100"
                (buttonClick)="saveEmailToUser({ email: newUserEmail })"
                [disabled]="(emailInput.invalid && emailInput.touched) || false">
                {{"order-payment.save" | translate}}
              </app-button>
            </div>

            <div *ngIf="emailInput.invalid && emailInput.touched" class="text-danger">
              <p *ngIf="emailInput.errors?.['required']">{{ 'This field is required.' | translate }}</p>
              <p *ngIf="emailInput.errors?.['email']">{{ 'Please enter a valid email address.' | translate }}</p>
            </div>
          </div>
        </div>
      </li>
    </ng-container>

<!--     <ng-container *ngIf="enabledPaymentMethods.includes(10) && !orderSchedule?.payment_recipient?.email">-->
<!--      <li-->
<!--        class="list-group-item border mb-3 rounded-4"-->
<!--        [ngClass]="{'disabled-payment-method': true}">-->
<!--        <div class="d-flex justify-content-between align-items-center"-->
<!--          style="cursor: not-allowed; opacity: 0.5;">-->
<!--          <div class="d-flex align-items-center py-2">-->
<!--            <input-->
<!--              type="radio"-->
<!--              name="paymentMethod"-->
<!--              class="mr-3"-->
<!--              [(ngModel)]="selectedPaymentMethod"-->
<!--              [value]="10"-->
<!--              disabled>-->
<!--            <div class="ms-2">-->
<!--              <h6 class="mb-0">{{"order-payment.invoice" | translate}}</h6>-->
<!--              <p class="text-muted text-center m-0" style="font-size: 14px;">{{"Ingen email registrert, kontakt oss" | translate}}</p>-->
<!--              <app-loader *ngIf="selectedPaymentMethod === 10 && loading" class="ms-2"></app-loader>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </li>-->
<!--    </ng-container>-->


    <ng-container *ngIf="noPaymentMethodsEnabled">
      <li class="list-group-item text-center">
        <span>{{ "order-payment.noMethodsEnabled" | translate }}</span>
      </li>
    </ng-container>

    <ng-container *ngIf="orderSchedule.payment_status_id == 1">
      <li class="list-group-item text-center">
        <span> {{ "order-payment.quickpaySelected" | translate }}</span>
      </li>
    </ng-container>
  </ul>



















<!--  <ng-container *ngIf="enabledPaymentMethods.includes(5)">-->
<!--    <li class="list-group-item border mb-3 rounded-4">-->
<!--      <div class="d-flex justify-content-between align-items-center" (click)="onPaymentSelect(5, 'quickpay')" style="cursor: pointer;">-->
<!--        <div class="d-flex align-items-center">-->
<!--          <input type="radio" name="paymentMethod" class="mr-3" [(ngModel)]="selectedPaymentMethod" [value]="5">-->
<!--          <div class="ms-2">-->
<!--            <h6 class="mb-0">-->
<!--              <ng-container *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.cardOrVipps" | translate}}</ng-container>-->
<!--              <ng-container *ngIf="orderSchedule.order_schedule_id">{{"order-payment.card" | translate}}</ng-container>-->
<!--            </h6>-->
<!--            <p *ngIf="!orderSchedule.order_schedule_id" class="mb-0 text-muted" style="font-size: 14px;"> {{"order-payment.quickpayTag" | translate}} </p>-->
<!--            <p *ngIf="orderSchedule.order_schedule_id" class="mb-0 text-muted" style="font-size: 14px;"> {{"order-payment.quickpayTagWithoutVipps" | translate}} </p>-->
<!--          </div>-->
<!--        </div>-->
<!--        <img *ngIf="orderSchedule.order_schedule_id" src="assets/images/payment-icons/VisaMaster.png" width="100px" class="ml-3">-->
<!--        <img *ngIf="!orderSchedule.order_schedule_id" src="assets/images/visa.png" width="100px" class="ml-3">-->
<!--      </div>-->

<!--      &lt;!&ndash; Details Section &ndash;&gt;-->
<!--      <div *ngIf="selectedPaymentMethod === 5 && paymentActive" class="mt-3 p-3 border rounded shadow-sm">-->
<!--        <p *ngIf="!orderSchedule.order_schedule_id" class="text-muted text-center" style="font-size: 14px;">{{"order-payment.quickpayDetails" | translate}}</p>-->
<!--        <p *ngIf="orderSchedule.order_schedule_id" class="text-muted text-center" style="font-size: 14px;">{{"order-payment.quickpayDetailsSubscription" | translate}}</p>-->
<!--        <div class="d-flex justify-content-center mb-2">-->
<!--          <app-button-->
<!--            class="w-100"-->
<!--            [isLoading]="loading"-->
<!--            buttonClass="btn btn-primary w-100"-->
<!--            (buttonClick)="continuePayment()">-->
<!--            <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.pay" | translate}}</span>-->
<!--            <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>-->
<!--          </app-button>-->

<!--          &lt;!&ndash; <button class="btn btn-primary w-100" (click)="continuePayment()">-->
<!--            <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.pay" | translate}}</span>-->
<!--            <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>-->
<!--          </button> &ndash;&gt;-->
<!--        </div>-->
<!--      </div>-->
<!--    </li>-->
<!--  </ng-container>-->

<!--  &lt;!&ndash; Riverty &ndash;&gt;-->
<!--  <ng-container *ngIf="enabledPaymentMethods.includes(11)">-->
<!--    <app-riverty-payment [orderData]="orderData" (updateOrderData)="updateOrderData.emit($event)" ></app-riverty-payment>-->
<!--  </ng-container>-->

<!--  &lt;!&ndash; Svea &ndash;&gt;-->
<!--  &lt;!&ndash; <ng-container *ngIf="enabledPaymentMethods.includes(1)">-->
<!--    <li-->
<!--      class="list-group-item border-0 mb-3 shadow-sm rounded"-->
<!--    >-->
<!--      <div class="d-flex justify-content-between align-items-center" (click)="onPaymentSelect(1, 'svea-container')" style="cursor: pointer;">-->
<!--        <div class="d-flex align-items-center">-->
<!--          <input type="radio" name="paymentMethod" class="mr-3" [(ngModel)]="selectedPaymentMethod" [value]="1">-->
<!--          <div class="ms-2">-->
<!--            <h6 class="mb-0">{{"order-payment.svea" | translate}}</h6>-->
<!--            <p class="mb-0 text-muted" style="font-size: 14px;">{{"order-payment.sveaTag" | translate}} </p>-->
<!--          </div>-->
<!--        </div>-->
<!--        <img src="assets/img/svea.png" width="65px" class="ml-3">-->
<!--      </div> &ndash;&gt;-->
<!--  &lt;!&ndash; Details Section &ndash;&gt;-->
<!--  &lt;!&ndash; <div *ngIf="selectedPaymentMethod === 1 && paymentActive" class="mt-3 p-3 border rounded shadow-sm">-->
<!--    <p class="text-muted text-center" style="font-size: 14px;">{{"order-payment.sveaDetails" | translate}} </p>-->
<!--    <app-loader [isLoading]="dataLoading"></app-loader>-->
<!--    <div [innerHTML]="sveaHTMLSnippet"></div>-->
<!--    <div id="svea-scripts-container"></div>-->
<!--    <div class="d-flex justify-content-center mb-2">-->
<!--      <button class="btn btn-primary w-100" (click)="continuePayment()">-->
<!--        <span>{{"order-payment.pay" | translate}}</span>-->
<!--      </button>-->
<!--    </div>-->
<!--  </div>-->
<!--</li>-->
<!--</ng-container> &ndash;&gt;-->

<!--  &lt;!&ndash; Invoice &ndash;&gt;-->
<!--  <ng-container *ngIf="enabledPaymentMethods.includes(10)">-->
<!--    <li class="list-group-item border mb-3 rounded-4">-->
<!--      <div class="d-flex justify-content-between align-items-center" (click)="onPaymentSelect(10, '')" style="cursor: pointer;">-->
<!--        <div class="d-flex align-items-center py-2">-->
<!--          <input type="radio" name="paymentMethod" class="mr-3" [(ngModel)]="selectedPaymentMethod" [value]="10">-->
<!--          <div class="ms-2">-->
<!--            <h6 class="mb-0">{{"order-payment.invoice" | translate}}</h6>-->
<!--            <p class="text-muted text-center m-0" style="font-size: 14px;">{{"order-payment.invoiceTag" | translate}}</p>-->

<!--            <app-loader *ngIf="selectedPaymentMethod === 10 && loading" class="ms-2"></app-loader>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--      &lt;!&ndash; Details Section &ndash;&gt;-->
<!--      <div *ngIf="selectedPaymentMethod === 10 && paymentActive" class="mt-3 p-3 border rounded shadow-sm">-->

<!--        &lt;!&ndash; Check if email exists &ndash;&gt;-->
<!--        <div *ngIf="enabledPaymentMethods.includes(10) && (orderSchedule?.payment_recipient?.email || orderData?.payment_recipient?.email)">-->
<!--          <p *ngIf="orderSchedule?.payment_recipient?.email" class="text-muted text-center" style="font-size: 14px;">-->
<!--            {{"order-payment.invoiceDetails" | translate}} {{orderSchedule.payment_recipient.email}}-->
<!--          </p>-->
<!--          <p *ngIf="orderData?.payment_recipient?.email && !orderSchedule?.payment_recipient?.email" class="text-muted text-center" style="font-size: 14px;">-->
<!--            {{"order-payment.invoiceDetails" | translate}} {{orderData.payment_recipient.email}}-->
<!--          </p>-->
<!--          <div class="d-flex justify-content-center mb-2">-->

<!--            <app-button-->
<!--              class="w-100"-->
<!--              [isLoading]="loading"-->
<!--              buttonClass="btn btn-primary w-100"-->
<!--              (buttonClick)="continuePayment()">-->
<!--              <span *ngIf="!orderSchedule.order_schedule_id">{{"order-payment.sendInvoice" | translate}}</span>-->
<!--              <span *ngIf="orderSchedule.order_schedule_id">{{"order-payment.createSubscription" | translate}}</span>-->
<!--            </app-button>-->
<!--          </div>-->
<!--        </div>-->

<!--        &lt;!&ndash; Check if email is missing &ndash;&gt;-->
<!--        <div *ngIf="enabledPaymentMethods.includes(10) && (!orderSchedule?.payment_recipient?.email && !orderData?.payment_recipient?.email)">-->
<!--          <p class="text-muted text-center" style="font-size: 14px;">{{"order-payment.noEmailText" | translate}}</p>-->
<!--          <div class="d-flex justify-content-center mb-2">-->
<!--            <input-->
<!--              type="email"-->
<!--              class="form-control me-2"-->
<!--              [(ngModel)]="newUserEmail"-->
<!--              placeholder="{{'order-payment.email' | translate}}"-->
<!--              #emailInput="ngModel"-->
<!--              required-->
<!--              email>-->

<!--            <app-button-->
<!--              class="w-100"-->
<!--              [isLoading]="loading"-->
<!--              buttonClass="btn btn-primary w-100"-->
<!--              (buttonClick)="saveEmailToUser({ email: newUserEmail })"-->
<!--              [disabled]="(emailInput.invalid && emailInput.touched) || false">-->
<!--              {{"order-payment.save" | translate}}-->
<!--            </app-button>-->
<!--          </div>-->

<!--          <div *ngIf="emailInput.invalid && emailInput.touched" class="text-danger">-->
<!--            <p *ngIf="emailInput.errors?.['required']">{{ 'This field is required.' | translate }}</p>-->
<!--            <p *ngIf="emailInput.errors?.['email']">{{ 'Please enter a valid email address.' | translate }}</p>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </li>-->
<!--  </ng-container>-->

<!--  &lt;!&ndash; <ng-container *-->



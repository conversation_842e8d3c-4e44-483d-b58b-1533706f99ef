import {Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2} from '@angular/core';
import {OrderResponse, OrderScheduleResponse} from "../../../@shared/models/order/order.module";
import {OrderPaymentResponse, PaymentMethodResponse} from "../../../@shared/models/payment/payment.module";
import {DomSanitizer, SafeHtml} from "@angular/platform-browser";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {CRM_ORD_92, CRM_PAY_0, CRM_PAY_6, USM_USR_4} from "../../../@shared/models/input/input.service";
import {PaymentService} from "../../../@shared/services/payment.service";
import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {FormsModule} from "@angular/forms";
import {RivertyPaymentComponent} from "./riverty/riverty-payment/riverty-payment.component";
import {LoaderComponent} from "../../../@shared/components/loader/loader.component";
import {Router} from "@angular/router";
import { EndpointService } from '../../../@shared/services/endpoints.service';
import { tap } from 'rxjs';
import { ButtonComponent } from "../../../@shared/components/button/button.component";


@Component({
  selector: 'app-payment-card',
  standalone: true,
  imports: [CommonModule, TranslateModule, FormsModule, RivertyPaymentComponent, LoaderComponent, ButtonComponent],
  templateUrl: './payment-card.component.html',
  styleUrl: './payment-card.component.scss'
})
export class PaymentCardComponent implements OnInit {
  @Input() selectedPayment: OrderPaymentResponse | null = null;
  @Input() orderData: OrderResponse = {} as OrderResponse;
  @Input() orderSchedule: OrderScheduleResponse = {} as OrderScheduleResponse;
  @Input() subscription: boolean = false;
  selectedPaymentMethod: number = 0;
  selectedPaymentContainer: string = '';
  paymentMethods: PaymentMethodResponse[] = [];
  paymentSelected: boolean = false;
  paymentActive: boolean = false;
  enabledPaymentMethods: number[] = [];
  sveaHTMLSnippet: SafeHtml= '' as SafeHtml;
  loading: boolean = false;
  noPaymentMethodsEnabled: boolean = false;
  @Input() dataLoading: boolean = false;
  @Output() updateOrderData: EventEmitter<OrderResponse | null> = new EventEmitter<OrderResponse | null>();
  @Output() updateOrderScheduleData: EventEmitter<OrderScheduleResponse | null> = new EventEmitter<OrderScheduleResponse | null>;
  newUserEmail: string = '';

  constructor(public utilsService: UtilsService,
    private elementRef: ElementRef,
    private paymentService: PaymentService,
    private endpointService: EndpointService,
    private sanitizer: DomSanitizer, private renderer2: Renderer2, private orderService: OrderService,   private router: Router) {}

  ngOnInit(): void {
    this.fetchPaymentMethods(this.subscription);
    // console.log('email in order schedule', this.orderSchedule.payment_recipient.email);
  }


  handleSveaEmbed(html: string, containerId: string) {
    this.dataLoading = true;

    // Parsing html string into DOM nodes
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Extract and remove script tags
    const allScripts = Array.from(doc.scripts);
    allScripts.forEach(script => script.parentNode!.removeChild(script));

    // Separate scripts into two groups: external and inline
    const externalScripts = allScripts.filter(script => !!script.src);
    const inlineScripts = allScripts.filter(script => !script.src);

    // Sanitize and bind the HTML, without the scripts
    this.sveaHTMLSnippet = this.sanitizer.bypassSecurityTrustHtml(doc.body.innerHTML);

    // Adding a slight delay to ensure sveaHTMLSnippet is added to the DOM
    setTimeout(() => {
      const scriptContainer = this.elementRef.nativeElement.querySelector(`#${containerId}`);
      let scriptsLoaded = 0;

      // Handle external scripts
      externalScripts.forEach(script => {
        const newScript = this.renderer2.createElement('script');
        this.renderer2.setAttribute(newScript, 'type', 'text/javascript');
        this.renderer2.setAttribute(newScript, 'src', script.src);

        // When script loads, check if all scripts have loaded
        this.renderer2.listen(newScript, 'load', () => {
          scriptsLoaded++;
          if (scriptsLoaded === allScripts.length) {
            this.dataLoading = false;
          }
        });

        // Append the new script element to the script container
        this.renderer2.appendChild(scriptContainer, newScript);
      });

      // Handle inline scripts
      inlineScripts.forEach(script => {
        const newScript = this.renderer2.createElement('script');
        newScript.innerHTML = script.innerText;
        this.renderer2.appendChild(scriptContainer, newScript);
        scriptsLoaded++;
      });

      // If there are no external scripts, then loading is finished
      if (externalScripts.length === 0) {
        this.dataLoading = false;
      }
    }, 0);
  }

  fetchPaymentMethods(subscription: boolean = false){
    let params: CRM_PAY_6;
    if (this.orderData && this.orderData.payment_recipient) {
      params = {
        company_id: this.orderData.company_id,
        private_customer: this.orderData.payment_recipient.is_private
      }
    }
    else if (this.orderSchedule && this.orderSchedule.payment_recipient) {
      params = {
        company_id: this.orderSchedule.company_id,
        private_customer: this.orderSchedule.payment_recipient.user_id ? 1 : 0
      }
    } else {
      console.error('No valid order data or order schedule data available');
      return;
    }

    this.paymentService.getPaymentMethods(params).subscribe(res => {
      this.paymentMethods = res;
      let anyMethodsEnabled = false;
      for (const paymentMethod of res) {
        if (subscription && paymentMethod.subscription_allowed === 0) continue;
        if (paymentMethod.enabled === 1) {
          anyMethodsEnabled = true;
          this.enabledPaymentMethods.push(paymentMethod.payment_method_id);
        }
      }
      this.noPaymentMethodsEnabled = !anyMethodsEnabled;
    });
  }

  saveEmailToUser(params: USM_USR_4): void {
    this.loading = true;
    this.endpointService.usm_usr_4(params).pipe(
      tap(() => {
        if (this.orderData?.order_id) {
          this.orderService.fetchAndUpdateOrder(this.orderData.order_id);
        }
        if (this.orderSchedule?.order_schedule_id) {
          this.orderService.fetchAndUpdateSchedule(this.orderSchedule.order_schedule_id);
        }
        this.loading = false;
      })

    ).subscribe({
      // next: () => {
      //   if (this.orderData.order_id) {
      //     this.orderData.payment_recipient.email = this.newUserEmail;
      //   } else if (schedule && this.orderSchedule.order_schedule_id) {
      //     this.orderSchedule.payment_recipient.email = this.newUserEmail;
      //   }
      //   console.log('User email updated and order/schedule fetched.');
      // },
      error: (error) => {
        console.error('Error updating user email and fetching order/schedule:', error);
        this.loading = false;
      }
    });
  }

  continuePayment() {
    if (this.loading) return;
    this.loading = true;

    this.paymentSelected = false;

    if (!this.subscription) {
      const params: CRM_PAY_0 = {
        order_id: this.orderData.order_id,
        payment_method_id: this.selectedPaymentMethod,
        payment_id: this.selectedPayment?.payment_id
      };

      this.paymentService.createPayment(params).subscribe(
        (res) => {
          if ([2].includes(this.selectedPaymentMethod)) {
            // Handle payment method 2
            this.loading = false;
          }
          else if ([1].includes(this.selectedPaymentMethod)) {
            // Handle payment method 1
            this.loading = false;
            // this.handleSveaEmbed(res.Gui!.Snippet, this.selectedPaymentContainer);
          }
          else if ([5].includes(this.selectedPaymentMethod)) {
            // Handle payment method 5
            this.loading = false;
            console.log(res)
            window.location.href = res.url;
          }
          else if (this.selectedPaymentMethod === 10) {
            // Handle payment method 10
            this.updateOrderData.emit(null);
            this.loading = false;
            this.router.navigate([`/orders/${this.orderData.order_id}`]);
          }
        },
        (error) => {
          // Handle error
          console.error('Payment creation failed:', error);
          this.loading = false;
        }
      );
    }
    else {
      // if (this.selectedPaymentMethod === 5) {
      //   let payload: CRM_PAY_17 = {
      //     order_schedule_id: this.orderSchedule.order_schedule_id,
      //     payment_method_id: this.selectedPaymentMethod
      //   };
      //
      //   this.paymentService.createPaymentSubscription(payload).subscribe(
      //     (res: OrderPaymentResponse) => {
      //       this.loading = false;
      //       // window.location.href = res.url;
      //     },
      //     (error) => {
      //       console.error('Subscription payment creation failed:', error);
      //       this.loading = false;
      //     }
      //   );
      // }
      if (this.selectedPaymentMethod === 10) {
        let payload: CRM_ORD_92 = {
          order_schedule_id: this.orderSchedule.order_schedule_id,
          payment_method_id: this.selectedPaymentMethod
        };

        this.orderService.updateOrderSchedulePaymentMethod(payload).subscribe(
          (res) => {
            this.loading = false;
            this.updateOrderScheduleData.emit(res);
          },
          (error) => {
            console.error('Order schedule payment method update failed:', error);
            this.loading = false;
          }
        );
      }
    }
  }

  onPaymentSelect(paymentMethodId: number, container: string) {
    this.paymentActive = true
    this.paymentSelected = true;
    this.selectedPaymentMethod = paymentMethodId;
    this.selectedPaymentContainer = container;
  }


  checkPaymentStatus(){
    setTimeout(function(){
      window.location.reload();
    }, 2000);
  }
}

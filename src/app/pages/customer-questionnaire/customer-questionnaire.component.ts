import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {
  OrderResponse, OrderCustomerQuestionResponse, OrderCustomerQuestionChoiceResponse
} from '../../@shared/models/order/order.module';
import {_CRM_ORD_152,
  CRM_ORD_33,
} from '../../@shared/models/input/input.service';
import { CompanyResponse } from '../../@shared/models/company/company.module';
import { OrderService } from '../../@shared/services/order.service';
import { StorageService } from '../../@core/@core/services/storage.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilsService } from '../../@core/@core/utils/utils.service';
import { TopNavbarComponent } from '../../@shared/components/top-navbar/top-navbar.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormGroup } from '@angular/forms';
import { CommonModule } from '@angular/common';
import * as Sentry from '@sentry/angular';
import {ButtonComponent} from "../../@shared/components/button/button.component";

export interface TempQuestion extends OrderCustomerQuestionResponse {
  failedValidation: boolean;
}

@Component({
  selector: 'app-customer-questionnaire',
  standalone: true,
  imports: [TopNavbarComponent, TranslateModule, CommonModule, ButtonComponent],
  templateUrl: './customer-questionnaire.component.html',
  styleUrl: './customer-questionnaire.component.scss',
})
export class CustomerQuestionnaireComponent implements OnInit {
  @Input() isOrderConfirmed: boolean = false;
  @Output() confirmComplete: EventEmitter<void> = new EventEmitter<void>();

  orderId: number = 0;
  order: OrderResponse = {} as OrderResponse;
  companyData?: CompanyResponse;
  questions: TempQuestion[] = [];
  isRequired: OrderResponse[] = [];
  form: FormGroup = new FormGroup({});
  isLoading: boolean = false;

  constructor(
    private orderService: OrderService,
    private storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    public utilsService: UtilsService,
    private router: Router,

  ) {}

  ngOnInit() {
    const idParam = this.activatedRoute.snapshot.paramMap.get('orderId');
    if (idParam) {
      this.orderId = parseInt(idParam, 10);
      if (!isNaN(this.orderId)) {
        this.orderService.fetchAndUpdateOrder(this.orderId);
        this.orderService.order$.subscribe((order) => {
          this.order = order;
          this.loadQuestions();
        });
      } else {
        console.error('Invalid order ID:', idParam);
      }
    } else {
      console.error('No order ID found in route parameters');
    }

    Sentry.getCurrentScope().setTags({
      "order_id": this.orderId,
      "user_id": this.storageService.getUser().user_id,
    });
  }


  loadQuestions() {
    if (this.order.customer_questions) {
      this.questions = this.order.customer_questions.map(question => ({
        ...question,
        failedValidation: false,
      }));
      console.log('Questions:', this.questions);
    } else {
      console.error('No customer questions found for this order');
    }
  }


  checkSelectionChange(choice: OrderCustomerQuestionChoiceResponse, question: TempQuestion, event: Event) {
    const target = event.target as HTMLInputElement;
    choice.value = target.checked ? 1 : 0;

    if (question.choices.some(c => c.value === 1)) {
      question.failedValidation = false;
    }

    let _payload: _CRM_ORD_152 = {
      order_choice_id: choice.order_choice_id,
      value: choice.value,
      input: choice.input,
      order_id: this.order.order_id,
    };
    this.updateChoice(_payload);
  }


  radioSelectionChange(choices: OrderCustomerQuestionChoiceResponse[], selectedChoice: OrderCustomerQuestionChoiceResponse, question: TempQuestion, event: Event) {
    const target = event.target as HTMLInputElement;
    choices.forEach(choice => {
      choice.value = (choice === selectedChoice && target.checked) ? 1 : 0;
    });

    if (question.choices.some(c => c.value === 1)) {
      question.failedValidation = false;
    }

    let _payload: _CRM_ORD_152 = {
      order_choice_id: selectedChoice.order_choice_id,
      value: selectedChoice.value,
      input: selectedChoice.input,
      order_id: this.order.order_id,
    };
    this.updateChoice(_payload);
  }


  updateGhostInputs(choice: OrderCustomerQuestionChoiceResponse, event: Event) {
    const target = event.target as HTMLInputElement;
    choice.input = target.value;

    let payload: _CRM_ORD_152 = {
      order_choice_id: choice.order_choice_id,
      value: choice.value,
      input: choice.input,
      order_id: this.order.order_id,
    };
    console.log('payload ghost ' , payload)

    this.updateChoice(payload);
  }


  updateChoice(choice: _CRM_ORD_152) {
    this.orderService.updateQuestionChoice(choice).subscribe({
      next: () => {
        console.log('Choice updated successfully');
      },
      error: (error) => {
        console.error('Error updating choice:', error);
      },
    });
  }


  confirmOrderAsCustomer() {
    let anyQuestionsFailedValidation = false;

    for (const question of this.questions) {
      let answered = false;
      if (question.required === 1) {
        answered = question.choices.some(choice => choice.value === 1);
        if (!answered) {
          question.failedValidation = true;
          anyQuestionsFailedValidation = true;
        } else {
          question.failedValidation = false;
        }
      } else {
        question.failedValidation = false;
      }
    }

    if (anyQuestionsFailedValidation) {
      return;
    }

    const params: CRM_ORD_33 = {
      order_id: this.order.order_id,
    };

    this.isLoading = true;
    this.orderService.confirmOrderAsCustomer(params).subscribe({
      next: () => {
        this.isLoading = false;
        this.router.navigate([`orders/${this.orderId}`]);
      },
      error: (error) => {
        console.error(error);
        this.router.navigate([`orders/${this.orderId}`]);
        this.isLoading = false;
      },
    });
  }
}

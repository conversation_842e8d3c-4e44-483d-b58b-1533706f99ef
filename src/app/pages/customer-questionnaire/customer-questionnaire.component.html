<app-top-navbar [showBackButton]='true' [companyData]="companyData" [order]="order"></app-top-navbar>
<div class="row d-flex align-items-center justify-content-center m-0">
  <div class="page-container col-12 col-md-7" *ngIf="order">
<!--    <div class="text-center mb-4">-->
<!--      {{"order-details.title" | translate}} #{{order.order_number}}-->
<!--      <h4>-->
<!--        &lt;!&ndash; {{ "cq.header" | translate}} {{order.main_product_name}} &ndash;&gt;-->
<!--      </h4>-->
<!--    </div>-->

    <div class="p-2">
      <form>
        <div class="border rounded-4 contact-container mb-2" *ngFor="let question of questions">
          <h6 class="text-bold mb-3">{{question.question_text}}</h6>
          <div class="form-group" *ngFor="let choice of question.choices">
            <label class="mb-2 d-flex align-items-center">
              <!-- Checkbox Input -->
              <input
                *ngIf="question.radio_selection == 0"
                [disabled]="isOrderConfirmed"
                [checked]="!!choice.value"
                [style]="question.failedValidation ? 'border: 1px solid red' : ''"
                type="checkbox"
                class="form-check-input"
                style="width: 20px; height: 20px;"
                (change)="checkSelectionChange(choice, question, $event)"
              />

              <!-- Radio Input -->
              <input
                *ngIf="question.radio_selection == 1"
                [disabled]="isOrderConfirmed"
                [checked]="!!choice.value"
                [style]="question.failedValidation ? 'border: 1px solid red' : ''"
                style="width: 20px; height: 20px;"
                type="radio"
                [id]="choice.choice_id"
                [name]="question.order_question_id"
                class="form-check-input"
                (change)="radioSelectionChange(question.choices, choice, question, $event)"
              />

              <span class="ms-2">{{choice.choice_name}}</span>
            </label>

            <!-- Ghost Input -->
            <div class="ps-3" *ngIf="choice.choice_ghost_text && choice.value === 1">
              <input
                [disabled]="isOrderConfirmed"
                type="text"
                [value]="choice.input"
                class="form-control my-1"
                [placeholder]="choice.choice_ghost_text"
                (change)="updateGhostInputs(choice, $event)"
              />
            </div>
          </div>
        </div>
      </form>
    </div>

    <div class="my-3 text-center">
      <app-button
        [disabled]="isLoading"
        (buttonClick)="confirmOrderAsCustomer()">
        {{ "common.submit" | translate }}
      </app-button>
    </div>
  </div>
</div>

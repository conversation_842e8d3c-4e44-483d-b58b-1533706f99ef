<div class="login-background" style="display: flex; flex-direction: column; flex: 1; min-height: 100vh;">
  <div class="col d-flex justify-content-center align-items-center">
    <div class="login-container flex-grow-1">
      <div class="d-flex justify-content-center">
        <div class="content-wrapper">
          <img src="../../../../assets/images/Logo_positive_medium.png"
               class="logo-image"
               alt="between logo">
          <h1>{{"login.subheader" | translate}}</h1>
          <p *ngIf="!isOtpSent">{{"login.loginText" | translate}}</p>
          <p *ngIf="isOtpSent">{{"otp.enterPin" | translate}}</p>

          <!-- Phone login section -->
          <div *ngIf="!isOtpSent">
            <div class="form-group">
              <div class="input-group mb-2">
                <app-phone-input
                  (phoneNumberChange)="handlePhoneNumberChange($event)"
                  (phoneNumberValid)="handlePhoneNumberValid($event)"
                  [placeholder]="'common.phone'"
                  [isRequired]="true">
                </app-phone-input>
              </div>
            </div>

            <div class="alert alert-danger text-center my-4" *ngIf="invalidOtp">
              {{"login.invalidOtp" | translate}}
            </div>
            <div class="form-group text-center mt-3">
              <app-button
                [disabled]="!phoneValid"
                [isLoading]="isLoading"
                buttonClass="btn btn-primary w-100"
                (buttonClick)="onOtpSend()">
                {{"login.sendCode" | translate}}
              </app-button>
            </div>
          </div>

          <!-- Otp section -->
          <div *ngIf="isOtpSent">
            <div class="otp-inputs mb-2 d-flex justify-content-center align-items-center">
              <ng-otp-input
                [formCtrl]="otpControl"
                [config]="{
                  length: 4,
                  inputClass: 'otp-custom-input',
                  containerClass: 'otp-custom-input',
                  allowNumbersOnly: true }"
                (onInputChange)="onOtpChange($event)"
              ></ng-otp-input>
            </div>
            <div class="d-flex align-items-center flex-column">
              <p class="mb-0" *ngIf="!isOtpSent">{{"otp.didntReceiveSMS" | translate}}</p>
              <p class="cursor-pointer" *ngIf="!isOtpResend" (click)="resendOtp()">
                <ins>{{"otp.sendSMSAgain" | translate}}</ins>
              </p>
              <p class="cursor-pointer" *ngIf="isOtpResend">
                <ins>{{"otp-modal.otpSentPleaseTryAgain" | translate}} {{resendCountdown}} {{"otp-modal.seconds" | translate}}</ins>
              </p>
              <div class="alert alert-danger w-100 text-center mx-4" *ngIf="invalidOtp" role="alert">
                {{"otp.invalidOTP" | translate}}
              </div>
            </div>

            <div class="buttons d-flex justify-content-center mt-4">
              <app-button
                buttonClass="btn btn-secondary"
                (buttonClick)="goBackToPhoneInput()">
                {{"otp.goBack" | translate}}
              </app-button>

              <app-button
                [isLoading]="isLoading"
                buttonClass="btn btn-primary"
                (buttonClick)="onConfirm()">
                {{ 'otp.confirmBtn' | translate }}
              </app-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <app-footer></app-footer>
</div>

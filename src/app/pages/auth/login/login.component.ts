import { Component, Input, OnInit } from '@angular/core';
import {FormControl, Validators} from "@angular/forms";
import { AuthService } from '../../../@core/@core/services/auth.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { USM_USR_8, USM_USR_9 } from "../../../@shared/models/input/input.service";
import { PhoneInputComponent } from "../../../@shared/components/phone-input/phone-input.component";
import { NgOtpInputModule } from "ng-otp-input";
import { CommonModule } from "@angular/common";
import {ActivatedRoute, Route, RouterOutlet} from "@angular/router";
import { ButtonComponent } from "../../../@shared/components/button/button.component";
import {
  LanguageSwitchComponent
} from "../../../@shared/components/footer/component/language-switch/language-switch.component";
import {FooterComponent} from "../../../@shared/components/footer/footer.component";
import { ToastService } from '../../../@core/@core/services/toast.service';

@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [TranslateModule, PhoneInputComponent, NgOtpInputModule, RouterOutlet, CommonModule, ButtonComponent, LanguageSwitchComponent, FooterComponent]
})
export class LoginComponent implements OnInit {
  otpControl: FormControl = new FormControl();
  phoneControl: FormControl = new FormControl('', [Validators.required, Validators.minLength(8)]);
  otp: string = '';
  invalidOtp = false;
  isOtpResend = false;
  isOtpSent = false;
  phoneDisplay: string = '';
  isAuthenticated = false;
  isLoading = false;
  otpError = '';
  resendCountdown = 60;
  isOtpResent = false;
  isInvalidPhoneLength = false;
  phoneValid: boolean = false;

  constructor(private authService: AuthService, private toastService: ToastService, private translate: TranslateService) {}

  ngOnInit(): void {
    // Initialize any required logic here
  }

  onOtpChange(otp: string): void {
    this.otp = otp;
  }

  onOtpSend(): void {
    this.isLoading = true;
    console.log("Phone control value:", this.phoneControl.value);
    if (this.phoneDisplay) {
      this.initiateOtp();
    } else {
      console.error("Phone number is empty after setting phoneDisplay.");
      this.isLoading = false;
    }
  }

  onConfirm(): void {
    this.isLoading = true;
    this.sendOtp();
  }

  initiateOtp() {
    if (!this.phoneDisplay) {
      console.error("Phone number is empty");
      this.isLoading = false;
      return;
    }
    const params: USM_USR_9 = {
      phone: this.phoneDisplay
    };
    console.log("params phone", params.phone);

    this.authService.initiateOtp(params).subscribe({
      next: (res) => {
        this.isOtpSent = true;
        this.isLoading = false;
      },
      error: () => {
        this.toastService.showError(this.translate.instant("otp.otpFailed"));
        this.isLoading = false;
      },
      complete: () => {}
    });
  }

  sendOtp() {
    this.invalidOtp = false;

    const params: USM_USR_8 = {
      phone: this.phoneDisplay,
      otp: this.otp.toString()
    };

    this.authService.sendOtpAndLogin(params).subscribe({
      next: (res) => {
        console.log('Login successful', res);
        this.authService.handleLoginSuccess(res);
        this.invalidOtp = false;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Login error', error);
        this.invalidOtp = true;
        this.isLoading = false;
      },
      complete: () => {}
    });
  }

  resendOtp() {
    this.initiateOtp();
    this.startCountdown();
  }

  startCountdown(): void {
    this.isOtpResend = true;
    this.resendCountdown = 60;
    const countdownInterval = setInterval(() => {
      this.resendCountdown--;
      if (this.resendCountdown === 0) {
        clearInterval(countdownInterval);
        this.isOtpResend = false;
      }
    }, 1000);
  }

  handlePhoneNumberChange(phoneNumber: string | null) {
    if (phoneNumber === null) {
      this.phoneDisplay = '';
    } else {
      this.phoneDisplay = phoneNumber;
      this.isInvalidPhoneLength = this.phoneDisplay.length < 8;
    }
    console.log("Updated phone display:", this.phoneDisplay);
  }

  handlePhoneNumberValid(isValid: boolean): void {
    this.phoneValid = isValid;
  }

  goBackToPhoneInput(): void {
    this.isOtpSent = false;
    this.otpControl.reset();
    this.invalidOtp = false;
  }
}

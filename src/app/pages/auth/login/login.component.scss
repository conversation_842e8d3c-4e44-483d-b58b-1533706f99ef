@import '../../../../../src/styles.scss';

.login-background {
  background: linear-gradient(to right, map-get($theme-colors, "primary") 10%, $primary-bg 50%);

  @media (max-width: 767px) {
    background: $primary-bg;
  }
}

.login-container {
  padding: 1rem;

  @media (max-width: 767px) {
    padding: 2rem 1.5rem;
    max-width: 100%;
  }

  // Add styles for h1 and p elements
  h1 {
    font-size: 2rem; // Default size
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;

    @media (max-width: 767px) {
      font-size: 1.5rem; // Smaller font size on mobile
    }

    @media (max-width: 380px) {
      font-size: 1.25rem; // Even smaller for very narrow screens
    }
  }

  p {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

::ng-deep .otp-input {
  border-radius: 10px !important;
  width: 50px;
}

@media screen and (max-width: 420px) {
  ::ng-deep .otp-input {
    width: 50px !important;
    height: 50px !important;
  }
}

// Add styles for the content wrapper
.content-wrapper {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;

  @media (max-width: 767px) {
    max-width: 100%;
  }
}

// Add styles for the logo
.logo-image {
  width: 200px;
  max-width: 80%;
  height: auto;
  margin-bottom: 1.5rem;
}

// Improve button spacing on mobile
.buttons {
  gap: 1rem;

  @media (max-width: 767px) {
    width: 100%;

    app-button {
      flex: 1;
    }
  }
}

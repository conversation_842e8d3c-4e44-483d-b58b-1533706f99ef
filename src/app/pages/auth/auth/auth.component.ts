import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { AuthService } from '../../../@core/@core/services/auth.service';
import { CRM_ORD_38 } from "../../../@shared/models/input/input.service";
import { Subscription } from 'rxjs';
import { TokenService } from '../../../@core/@core/services/token.service';

@Component({
  selector: 'app-auth',
  standalone: true,
  imports: [],
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss']
})
export class AuthComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();

  token: string = '';
  orderId: number = 0;
  orderScheduleId: number = 0;
  isAuthenticated: boolean = false;
  isLoading: boolean = false;
  accessLevel: number = 0;
  serviceRecipient: number = 0;
  isLoggedIn: boolean = false;

  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private tokenService: TokenService,
    private router: Router
  ) {

  }

  ngOnInit(): void {
    this.subscriptions.add(
      this.route.queryParams.subscribe(q => {
        this.token = q['token'];
        this.orderId = q['order_id'];
        this.accessLevel = q['access_level'];
        this.orderScheduleId = q['order_schedule_id'];
        this.serviceRecipient = q['service_recipient'];

        if (this.orderId && this.token) {
          this.handleOrderLogin();
        } else if (this.orderScheduleId && this.token) {
          this.handleOrderScheduleLogin();
        } else {
          // Use isLoggedIn method here directly
          const loggedIn = this.tokenService.isLoggedIn(); // Assuming this returns a boolean

          if (!loggedIn) {
            this.router.navigate(['/login']);
          }
        }
      })
    );
  }

  private handleOrderLogin(): void {
    console.log('handleOrderLogin called');
    this.isLoading = true;
    console.log('isLoading set to true');

    const params: CRM_ORD_38 = {
      order_id: this.orderId,
      order_token: this.token,
      service_recipient: this.serviceRecipient
    };

    console.log('Authentication params:', params);

    this.authService.login(params).subscribe({
      next: (res) => {
        console.log('Authentication successful', res);
        this.authService.handleLoginSuccessFromOrderToken(res);

        // Add a small delay to ensure token is saved before navigation
        setTimeout(() => {
          console.log('Checking token before navigation:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');
          this.router.navigate([`orders/${this.orderId}`]);
          this.isLoading = false;
          console.log('isLoading set to false');
        }, 300);
      },
      error: () => {
        // Handle authentication error
        this.router.navigate([`orders-overview/`]);
        this.isAuthenticated = false;
        this.isLoading = false;
        console.log('isAuthenticated set to false');
        console.log('isLoading set to false');
      }
    });
  }

  private handleOrderScheduleLogin(): void {
    console.log('handleOrderScheduleLogin called');
    this.isLoading = true;
    console.log('isLoading set to true');

    const params: CRM_ORD_38 = {
      order_schedule_id: this.orderScheduleId,
      order_token: this.token,
    };

    console.log('Authentication params:', params);

    this.authService.login(params).subscribe({
      next: (res) => {
        console.log('Authentication successful', res);
        this.authService.handleLoginSuccessFromOrderToken(res);

        // Add a small delay to ensure token is saved before navigation
        setTimeout(() => {
          console.log('Checking token before navigation:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');
          this.router.navigate([`subscriptions/${this.orderScheduleId}`]);
          this.isLoading = false;
          console.log('isLoading set to false');
        }, 300);
      },
      error: () => {
        // Handle authentication error
        this.router.navigate([`orders-overview/`]);
        this.isAuthenticated = false;
        this.isLoading = false;
        console.log('isAuthenticated set to false');
        console.log('isLoading set to false');
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}

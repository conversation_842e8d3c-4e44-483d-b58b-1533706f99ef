import {Component, OnInit} from '@angular/core';
import {BottomNavbarComponent} from "../../../@shared/components/bottom-navbar/bottom-navbar.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderResponse, OrderScheduleResponse} from "../../../@shared/models/order/order.module";
import {OrderService} from "../../../@shared/services/order.service";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {Router} from "@angular/router";
import {CRM_ORD_29} from "../../../@shared/models/input/input.service";
import {CommonModule} from "@angular/common";
import {ButtonComponent} from "../../../@shared/components/button/button.component";
import { AuthService } from '../../../@core/@core/services/auth.service';
import {UserResponse} from "../../../@shared/models/user/user.module";
import {FooterComponent} from "../../../@shared/components/footer/footer.component";
import {OrderCardComponent} from "../../../@shared/components/order-card/order-card.component";
import {TopNavbarComponent} from "../../../@shared/components/top-navbar/top-navbar.component";
import {PageLoadingSpinnerComponent} from "../../../@shared/components/page-loading-spinner/page-loading-spinner.component";

@Component({
  selector: 'app-orders-overview',
  standalone: true,
  imports: [
    BottomNavbarComponent,
    TranslateModule,
    CommonModule,
    ButtonComponent,
    FooterComponent,
    OrderCardComponent,
    TopNavbarComponent,
    PageLoadingSpinnerComponent
  ],
  templateUrl: './orders-overview.component.html',
  styleUrl: './orders-overview.component.scss'
})
export class OrdersOverviewComponent implements OnInit {
  allOrders: OrderResponse[] = [];
  allOrdersTotalItems: number = 0;
  allOrdersLoading: boolean = false;
  currentPage: number = 1;
  pageSize: number = 10;
  allOrdersTotalPages: number = 0;
  userData: UserResponse = {} as UserResponse;

  constructor(
    public utilsService: UtilsService,
    private orderService: OrderService,
    public translate: TranslateService,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit() {
    document.documentElement.style.setProperty('--primary-color', '#448c74');
    this.fetchAllOrdersData();
    this.authService.fetchUser().subscribe({
      next: response => {
        this.userData = response;
      },
      error: error => {
        console.error("Error fetching user data:", error);
      },
    });
  }

  fetchAllOrdersData() {
    this.allOrdersLoading = true;
    const params = {
      paginate: 1,
      limit: this.pageSize,
      page: this.currentPage
    };

    this.orderService.getOrdersAsUser(params).subscribe({
      next: (res) => {
        this.allOrders = res.data;
        this.allOrdersTotalPages = res.total_pages;
        this.allOrdersTotalItems = res.total_items;
        this.allOrdersLoading = false;
      },
      error: (error) => {
        console.error('Error fetching orders:', error);
        this.allOrdersLoading = false;
      }
    });
  }

  onNext(): void {
    if (this.currentPage < this.getTotalPages()) {
      this.currentPage++;
      this.fetchAllOrdersData();
      window.scrollTo(0, 0);
    }
  }

  onPrev(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.fetchAllOrdersData();
      window.scrollTo(0, 0);
    }
  }

  getTotalPages(): number {
    return this.allOrdersTotalPages;
  }

  navigateToOrderDetails(order: OrderResponse): void {
    this.router.navigate([`/orders/${order.order_id}`]);
  }

  removeFocus(event: Event) {
    (event.target as HTMLElement).blur();
  }
}

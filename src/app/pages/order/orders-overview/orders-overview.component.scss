.main-nav-pills .nav-link.active {
  color: #000000;
  background-color: #E0E3E7;
  border-radius: 1rem;
  cursor: pointer;
}

.main-nav-pills .nav-link {
  color: #000000;
  cursor: pointer;
}

.nav-tabs {
  border-bottom: 2px solid #ddd;

  .nav-item {
    margin-bottom: -1px;

    .nav-link {
      display: block;
      padding: 10px 20px;
      border: 1px solid transparent;
      border-radius: 0;
      color: #555;
      font-weight: bold;
      text-align: center;
      cursor: pointer;
      box-sizing: border-box;

      &.active {
        color: #000;
        border-color: #ddd #ddd transparent;
        border-bottom: 2px solid #000;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        margin-bottom: -1px;
      }

      &:hover {
        color: #000;
      }
    }
  }
}

.order-tab {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  padding-bottom: 0;
}

.order-tab-item {
  flex: 1;
  text-align: center;
  margin-bottom: -2px !important;
}

.order-tab-item.active {
  border-bottom: 2px solid #000;
}

.order-tab-text {
  display: block;
  padding: 10px;
  text-decoration: none;
  color: inherit;
  white-space: nowrap;
}


.btn:active {
  background-color: var(--primary);

}

.btn:focus {
  outline: none;
}

app-footer {
  margin-top: auto;
}

.btn-primary {
  background-color: #448c74 !important;
  border-color: #448c74 !important;
}

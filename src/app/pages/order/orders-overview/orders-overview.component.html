<div style="display: flex; flex-direction: column; flex: 1; min-height: 100vh;">
  <app-top-navbar></app-top-navbar>
  <div class="page-container px-2">
    <div class="fs-4 mb-2">
      {{ "order-list.hello" | translate }} {{userData.first_name}}!
      <i class="fa-regular fa-hand-wave" style="color: #ffce35;"></i>
    </div>
    <div class="fs-2 mb-2">
      {{ "order-list.title" | translate }}
    </div>

    <!-- Orders List -->
    <div *ngIf="allOrdersLoading">
      <app-page-loading-spinner></app-page-loading-spinner>
    </div>
    <div *ngIf="allOrders.length === 0 && !allOrdersLoading" class="text-muted text-center">
      {{"order-list.noAllOrders" | translate}}
    </div>
    <app-order-card *ngFor="let order of allOrders" [order]="order"></app-order-card>

    <!-- Pagination controls -->
    <div class="pagination-controls d-flex justify-content-between align-items-center"
         *ngIf="getTotalPages() > 10">
      <button class="btn btn-primary py-2"
              style="min-width: 110px;"
              (click)="onPrev()"
              (mouseup)="removeFocus($event)"
              [disabled]="currentPage === 1">
        {{"common.previous" | translate}}
      </button>
      <span class="black mx-2"> Page {{currentPage}} of {{getTotalPages()}}</span>
      <button class="btn btn-primary py-2"
              style="min-width: 110px;"
              (click)="onNext()"
              (mouseup)="removeFocus($event)"
              [disabled]="currentPage === getTotalPages()">
        {{"common.next" | translate}}
      </button>
    </div>
  </div>
  <app-footer [paymentVisible]="false" *ngIf="!allOrdersLoading"></app-footer>
</div>

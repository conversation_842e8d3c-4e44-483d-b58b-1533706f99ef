<div class="app-wrapper">
  <app-top-navbar *ngIf="companyData" [companyData]="companyData" [order]="order"></app-top-navbar>
  <app-page-loading-spinner *ngIf="isPageLoading"></app-page-loading-spinner>

<div class="row page-container m-0" *ngIf="!isPageLoading" style="flex:1">
<div>
 <app-order-information *ngIf="order" [company]="companyData"></app-order-information>
  <div class="row">
  <div class="col-12 col-md-7">
    <div *ngIf="isMainViewVisible">
      <div *ngIf="isUpdatingStatus" class="status-update-message mb-2">
        <div class="d-flex align-items-center">
          <mat-spinner class="theme-spinner me-2" diameter="20"></mat-spinner>
          <span>{{ 'order.details.updating_status_message' | translate }}</span>
        </div>
      </div>
      <div class="status-update-message-paid mb-2" *ngIf="!isUpdatingStatus && statusUpdateMessage">
        <span>{{ statusUpdateMessage }}</span>
      </div>
      <app-work-orders-list *ngIf="order" [company]="companyData"></app-work-orders-list>

      <div class="col-12 d-block d-md-none">
      <app-repeating-job [compactView]="true"></app-repeating-job>
     </div>

      <app-contact-address [companyData]="companyData" [seller]="true" [orderData]="order"></app-contact-address>

      <div *ngIf="hasCustomerQuestions()" class="contact-container bg-white border rounded-4 mt-2 mb-4">
        <!-- If specifications are answered -->
        <h6 *ngIf="isSpecificationAnswered(); else notAnsweredTemplate">{{ "cq.answered" | translate }}</h6>

        <!-- If specifications are not answered -->
        <ng-template #notAnsweredTemplate>
          <h6>{{ "cq.preparation" | translate }}</h6>
          <p>{{ "cq.description" | translate }}</p>
          <div class="ms-auto">
          <app-button (buttonClick)="navigateToCQ()">{{ "cq.btn1" | translate }}</app-button>
          </div>
        </ng-template>

        <!-- Common description or additional content -->
        <p *ngIf="isSpecificationAnswered()">{{ "cq.descriptionAnswered" | translate }}</p>
        <!-- Button for when specifications are answered -->
        <button *ngIf="isSpecificationAnswered()" class="py-2 font-10 btn btn-outline-dark small" (click)="navigateToCQ()">{{ "cq.btn2" | translate }}</button>
      </div>


      <div *ngIf="[5, 6, 7].includes(order.order_status_id)" class="contact-container bg-white border rounded-4 mb-4">
        <h6>{{"order-feedback.howWasOurService" | translate}}</h6>
        <p>{{"order-feedback.helpUsImprove" | translate}}</p>
        <app-rating [order]="order" [rating]="selectedRating" [comment]="comment" [isReadOnly]="isReadOnly"></app-rating>
      </div>

      <div class="mb-4">
        <app-notes [orderData]="order!"></app-notes>
      </div>

      <div class="mb-4">
        <app-attached-images *ngIf="order"></app-attached-images >
      </div>

      <div class="mb-4">
      <app-crew-reports *ngIf="order"></app-crew-reports>
    </div>

      <div class="mb-4">
        <app-important-information *ngIf="order"></app-important-information>
      </div>

      <div class="mb-4">
        <app-payment-summary *ngIf="order?.hide_payment_data != 1"></app-payment-summary >
      </div>

    </div>
  </div>

  <div  class="col-12 col-md-5 d-none d-md-block">
    <div *ngIf="order?.order_lines?.length !== 0 && (!order.repeating || order.project) && orderLinesValidPaymentCheck">
      <app-order-lines [orderLines]="order.order_lines"></app-order-lines>
    </div>
    <app-repeating-job [compactView]="true"></app-repeating-job>
    <app-repeating-payment></app-repeating-payment>
  </div>
  </div>
</div>



<div *ngIf="order.order_lines" class="col-12 d-block d-md-none">
  <div *ngIf="order?.order_lines?.length !== 0 && (!order.repeating || order.project) && orderLinesValidPaymentCheck">
    <app-order-lines [orderLines]="order.order_lines"></app-order-lines>
  </div>
    <app-repeating-payment></app-repeating-payment>
  </div>
</div>


    <app-footer *ngIf="!isPageLoading"></app-footer>
</div>
    <app-payment-button *ngIf="showPaymentButton && order.hide_payment_data != 1" [orderData]="order!"></app-payment-button>

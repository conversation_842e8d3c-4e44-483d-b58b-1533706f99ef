<div class="modal-header">
  <h5 class="modal-title">{{ 'order.details.allWorkOrders' | translate }}</h5>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss('Cross click')"></button>
</div>
<div class="modal-body">
  <div *ngIf="allWorkOrders.length > 0; else noWorkOrders">
    <div *ngFor="let workOrder of allWorkOrders; let i = index; let last = last" class="title d-flex justify-content-between align-items-center mb-3">
      <div class="flex-grow-1">
        <!-- Main content similar to the main list -->
        <div class="p-3 border rounded">
          <!-- Title and Icons -->
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex flex-column">
              <h5>
                {{ workOrder.work_order_title || ("order.details.workorder" | translate) }}
              </h5>
              <h6 *ngIf="!orderData.work_order_schedule_templates" class="p-1" style="font-size:small; background-color: #448c74; border-radius: 5px; color:white">
                {{ "order.details.workorder" | translate }}
              </h6>
            </div>
            <p *ngIf="!workOrder.customer_notification_sent_at || (workOrder.customer_notification_sent_at && workOrder.work_order_status_id > 0)" class="mb-0 custom-badge">{{ getStatusText(workOrder.work_order_status_id) }}</p>
            <p *ngIf="workOrder.customer_notification_sent_at && workOrder.work_order_status_id < 1" class="mb-0 custom-badge">{{ getStatusText(9) }}</p>
          </div>

          <!-- Execution Date -->
          <div class="title d-flex">
            <div style="width:23px;"><i class="fa-regular fa-calendar fa-lg" style="padding-right: 7px;"></i></div>
            <span *ngIf="workOrder.execution_at" class="text-capitalize">
              {{ (workOrder.execution_at | date: 'EEEE, dd. MMMM yyyy') }}
            </span>
            <span *ngIf="!workOrder.execution_at">
              {{ 'common.noDate' | translate }}
            </span>
          </div>

          <!-- Time Display -->
          <div *ngIf="workOrder.arrival_from || orderData.execution_at" class="title d-flex">
            <div style="width:23px;">
              <i class="fa-regular fa-clock fa-lg pe-1"></i>
            </div>
            <span>
              <ng-container *ngIf="workOrder.arrival_from">
                {{ "order.details.arrivalWindow" | translate }}: {{ workOrder.arrival_from }} - {{ workOrder.arrival_to || ("order.details.unknownEndTime" | translate) }}
              </ng-container>
              <ng-container *ngIf="!workOrder.arrival_from">
                {{ orderData.execution_at ? (orderData.execution_at | date: 'HH:mm') : ('order.details.noTime' | translate) }}
              </ng-container>
            </span>
          </div>

          <!-- Addresses -->
          <div class="mt-1" *ngIf="workOrder.addresses && workOrder.addresses.length > 0">
            <div *ngFor="let address of workOrder.addresses">
              <i class="fa-regular fa-location-dot"></i> {{ workOrder.addresses.length > 1 ? address.address_name + ': ' : '' }} {{ address.display }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #noWorkOrders>
    <p>{{ 'order.details.noWorkOrders' | translate }}</p>
  </ng-template>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="activeModal.close('Close click')">
    {{ 'common.close' | translate }}
  </button>
</div>

import {Component, Input} from '@angular/core';
import {OrderResponse, WorkOrderResponse} from "../../../../../@shared/models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {CommonModule} from "@angular/common";

@Component({
  selector: 'app-full-work-order-list',
  standalone: true,
  imports: [TranslateModule, CommonModule],
  templateUrl: './full-work-order-list.component.html',
  styleUrl: './full-work-order-list.component.scss'
})
export class FullWorkOrderListComponent {
@Input() allWorkOrders: WorkOrderResponse[] = [];
@Input() orderData: OrderResponse = {} as OrderResponse;
constructor(public activeModal: NgbActiveModal, private translate: TranslateService) {}

  getStatusText(statusId: number): string {
    switch (statusId) {
      case 0:
        return this.translate.instant('order.details.order_scheduled');
      case 1:
        return this.translate.instant('order.details.in_progress');
      case 2:
        return this.translate.instant('order.details.completed');
      case 8:
        return this.translate.instant('order.details.cancelled');
      case 9:
        return this.translate.instant('order.details.crew_on_way');
      default:
        return this.translate.instant('order.details.unknown_status');
    }
  }
}

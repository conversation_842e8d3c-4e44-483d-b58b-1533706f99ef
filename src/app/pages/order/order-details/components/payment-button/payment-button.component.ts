import { Component, Input, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { OrderResponse } from '../../../../../@shared/models/order/order.module';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from "../../../../../@shared/components/button/button.component";
import { TokenService } from '../../../../../@core/@core/services/token.service';

@Component({
  selector: 'app-payment-button',
  standalone: true,
  imports: [TranslateModule, CommonModule, ButtonComponent],
  templateUrl: './payment-button.component.html',
  styleUrls: ['./payment-button.component.scss']
})
export class PaymentButtonComponent {
  @Input() orderData: OrderResponse = {} as OrderResponse;
  @Input() isLoading: boolean = false;
  @Input() buttonText: string = "Ready to Pay";
  @Input() redirectUrl: string = '/payment';
  @Input() paymentSummary:boolean = false;
  constructor(private router: Router, private injector: Injector) {}

  goToPayment(): void {
    if (!this.isLoading) {
      // Get the existing TokenService instance from the injector
      const tokenService = this.injector.get(TokenService);
      console.log('Token before navigating to payment:', tokenService.getToken() ? 'Token exists' : 'Token is null');

      this.router.navigate([`/payment/${this.orderData.order_id}`]);
    }
  }
}

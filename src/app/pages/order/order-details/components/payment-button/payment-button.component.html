<!--<div class="payment-button-container p-3 py-4">-->
<!--  <div class="payment-content container">-->
<!--    <div class="row justify-content-center align-items-center">-->
<!--      <div class="col-12 d-flex justify-content-center mb-3 px-4">-->
<!--        <h6 class="text-center my-auto">{{ 'order-details.paymentText' | translate }}</h6>-->
<!--      </div>-->
<!--    </div>-->
<!--      <div class="col-12  d-flex align-items-center">-->
<!--        <app-button [buttonClass]="'btn btn-primary w-100 w-md-auto mb-4'" [disabled]="isLoading" (buttonClick)="goToPayment()">{{ 'order-details.paymentBtn' | translate }}</app-button>-->
<!--      </div>-->
<!--  </div>-->
<!--</div>-->
<div *ngIf="true">
  <app-button
    [buttonClass]="'btn btn-primary'"
    [disabled]="isLoading"
    (buttonClick)="goToPayment()">
    {{ 'order-details.paymentBtn' | translate }}
  </app-button>
</div>

<div *ngIf="!paymentSummary" class="payment-button-container p-3 py-4">
  <div class="payment-content container text-center">
    <h6>{{ 'order-details.paymentText' | translate }}</h6>
    <div class="row justify-content-center mt-2">
      <div class="col-12 col-md-auto">
        <app-button
          [buttonClass]="'btn btn-primary'"
          [disabled]="isLoading"
          (buttonClick)="goToPayment()">
          {{ 'order-details.paymentBtn' | translate }}
        </app-button>
      </div>
    </div>
  </div>
</div>

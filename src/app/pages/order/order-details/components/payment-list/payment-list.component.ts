import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {CommonModule, NgFor, NgIf} from "@angular/common";
import {OrderResponse} from "../../../../../@shared/models/order/order.module";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {UtilsService} from "../../../../../@core/@core/utils/utils.service";
import {OrderService} from "../../../../../@shared/services/order.service";
import {CRM_PAY_30, CRM_PAY_35} from "../../../../../@shared/models/input/input.service";
import {OrderPaymentResponse} from "../../../../../@shared/models/payment/payment.module";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {Router} from "@angular/router";
import {CompanyResponse} from "../../../../../@shared/models/company/company.module";
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";

@Component({
  selector: 'app-payment-list',
  standalone: true,
  imports: [CommonModule, NgIf, NgFor, TranslateModule, ButtonComponent],
  templateUrl: './payment-list.component.html',
  styleUrl: './payment-list.component.scss'
})
export class PaymentListComponent {
  @Output() paymentSelected: EventEmitter<OrderPaymentResponse> = new EventEmitter<OrderPaymentResponse>();
  @Input() orderPayments: OrderPaymentResponse[] = [];
  orderData: OrderResponse = {} as OrderResponse;

    constructor(public utilsService: UtilsService,
                public translate: TranslateService,
  ) {}

  goToPayment(paymentLine: OrderPaymentResponse): void {
    this.paymentSelected.emit(paymentLine);
  }
}

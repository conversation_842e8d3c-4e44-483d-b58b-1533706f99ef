<div class="payment-selection-container">
<!--    <h5 class="mb-4">{{ 'order-details.selectPayment' | translate }}</h5>-->

    <div *ngFor="let line of orderPayments" class="mb-3">
        <div *ngIf="line.payment_status_id !== 3 && line.payment_status_id !== 6 && line.payment_status_id !== 7" class="card shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">

                    <!-- Payment Details -->
                    <div class="col-12 col-md-8">

                        <h6 class="card-title mb-1"><strong>{{ 'order-summary.amount' | translate }}: </strong> kr {{ utilsService.formatCurrency(line.total_amount_inc_vat) }},-</h6>
                    </div>

                    <!-- Go to payment -->
                    <div class="col-12 col-md-4 text-center text-md-end mt-3 mt-md-0">
                        <app-button [buttonClass]="'btn btn-primary'" (buttonClick)="goToPayment(line)"> {{ 'payment.summary.button' | translate }}</app-button>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!--     No Available Payments Message -->
<!--    <div *ngIf="orderPayments.filter(p => p.payment_status_id !== 3 && p.payment_status_id !== 6).length === 0" class="alert alert-info">-->
<!--        {{ 'order-details.noAvailablePayments' | translate }}-->
<!--    </div>-->
</div>


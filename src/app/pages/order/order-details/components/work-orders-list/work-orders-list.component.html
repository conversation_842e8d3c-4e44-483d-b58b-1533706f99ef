<div *ngIf="!compactView">
  <div *ngIf="workOrders.length > 0" class="border rounded-4 mb-4 ">
    <div *ngFor="let workOrder of workOrders; let i = index; let last = last"
         class=" title d-flex justify-content-between align-items-center">
      <div class="flex-grow-1" [class.border-bottom]="!last">
        <!-- Main content -->
        <div class="p-3">
          <!-- Title and Icons -->
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex flex-column">
              <h5>{{ workOrder.work_order_title || "order.details.workorder" | translate }}</h5>
              <h6 *ngIf="!orderData.work_order_schedule_templates" class="p-1" style="font-size:small; background-color: #448c74; border-radius: 5px; color:white">{{ "order.details.workorder" | translate }}</h6>
            </div>
            <p *ngIf="!workOrder.customer_notification_sent_at || (workOrder.customer_notification_sent_at && workOrder.work_order_status_id > 0)"
              class="mb-0 custom-badge">{{ getStatusText(workOrder.work_order_status_id) }}</p>
            <p *ngIf="workOrder.customer_notification_sent_at && workOrder.work_order_status_id < 1"
               class="mb-0 custom-badge">{{ getStatusText(9) }}</p>
          </div>

          <!-- Execution Date -->
          <div class="title d-flex">
            <div style="width:23px;"><i class="fa-regular fa-calendar fa-lg" style="padding-right: 7px;"></i></div>
            <span *ngIf="workOrder.execution_at" class="text-capitalize">
          {{ (workOrder.execution_at | date: 'EEEE, dd. MMMM yyyy') }}
        </span>
            <span *ngIf="!workOrder.execution_at">{{ 'common.noDate' | translate }}</span>
          </div>

          <!-- Time Display -->
          <div *ngIf="(workOrder.arrival_from || orderData.execution_at) && !isMultiDay(workOrder)" class="title d-flex">
            <div style="width:23px;"><i class="fa-regular fa-clock fa-lg pe-1"></i></div>
            <span>
          <ng-container *ngIf="workOrder.arrival_from">{{ "order.details.arrivalWindow" | translate }}: {{ workOrder.arrival_from }}- {{ workOrder.arrival_to || ("order.details.unknownEndTime" | translate) }}</ng-container>
          <ng-container *ngIf="!workOrder.arrival_from">{{ workOrder.execution_at ? (workOrder.execution_at | date: 'HH:mm') : ('order.details.noTime' | translate) }}</ng-container>
        </span>
          </div>

          <!-- Addresses -->
          <div class="mt-1" *ngIf="workOrder.addresses && workOrder.addresses.length > 0">
            <div *ngFor="let address of workOrder.addresses">
              <i class="fa-regular fa-location-dot"></i> {{ workOrder.addresses.length > 1 ? address.address_name + ': ' : '' }} {{ address.display }}
            </div>
          </div>

          <!-- See Crew Toggle -->

          <!-- Crew Details -->
          <div *ngIf="isCrewVisible[i]">
            <div *ngIf="workOrder.users && workOrder.users.length > 0"
                 class="d-flex justify-content-between align-items-center">
              <div class="flex-grow-1">
                <app-contact-address [seller]="false" [orderData]="orderData"
                                     [workOrder]="workOrder"></app-contact-address>
              </div>
            </div>
          </div>
          <div>
            <a *ngIf="workOrder.users.length > 0" href="#" (click)="toggleCrewVisibility(i); $event.preventDefault();">{{ isCrewVisible[i] ? ('order.details.hideCrew' | translate) : ('order.details.seeCrew' | translate) }}</a>
          </div>

        </div>
      </div>
    </div>
<!--    <hr *ngIf="!last" class="mt-0">-->
    <div class="ps-3 mb-3">
      <app-button *ngIf="allWorkOrders.length > 5 " [buttonClass]="'btn btn-primary py-1'" (buttonClick)="openAllWorkOrdersModal()">{{ 'order.workOrder.seeAll' | translate }} (+{{ allWorkOrders.length -5 }})</app-button>
    </div>
  </div>
</div>
<!-- Repeating Work Order Template Quote -->
<div *ngIf="compactView">
  <div class="border rounded-4 p-2 mb-2">
      <h6 class="mb-0">{{ 'repeating.order' | translate }}</h6>
<!--    <h6 *ngIf="displayCount > 1" class="mb-0">{{ 'repeating.order' | translate }}</h6>-->

  <div *ngFor="let workOrder of workOrders; let i = index">
    <div class=" rounded-4 p-2">
      <div class="d-flex align-items-center">
        <div>
          <i class="fa-light fa-calendar fa-2xl" style="color: var(--primary-color);"></i>
        </div>
        <div class="ms-2">
          <div class="text-capitalize">
            {{ workOrder.execution_at | date: 'EEEE, dd. MMMM yyyy' }}
          </div>
          <div>
            {{ workOrder.execution_at | date: 'HH:mm' }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Show More Button -->
    <button
      *ngIf="showLessPressed"
      class="toggle-button mb-2"
      (click)="showMoreOrders()">
      {{ 'repeating.seeMore' | translate }} <i class="fa-regular fa-chevron-down" style="color: var(--primary-color);"></i>
    </button>

    <!-- Show Less Button -->
    <button
      *ngIf="showMorePressed"
      class="toggle-button mb-2"
      (click)="showLessOrders()">
      {{ 'repeating.seeLess' | translate }} <i class="fa-regular fa-chevron-up" style="color: var(--primary-color);"></i>
    </button>
  </div>

</div>


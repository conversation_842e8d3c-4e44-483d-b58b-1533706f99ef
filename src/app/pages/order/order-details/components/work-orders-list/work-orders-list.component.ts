import {Component, Input, OnInit} from '@angular/core';
import {ContactComponent} from "../../../../../@shared/components/contact-address/contact.component";
import {DatePipe, Ng<PERSON>orOf, NgIf, NgTemplateOutlet} from "@angular/common";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {OrderResponse, StageAddressUnitResponse, WorkOrderResponse,} from "../../../../../@shared/models/order/order.module";
import {UtilsService} from "../../../../../@core/@core/utils/utils.service";
import {OrderService} from "../../../../../@shared/services/order.service";
import {CompanyResponse} from "../../../../../@shared/models/company/company.module";
import {CRM_ORD_161} from "../../../../../@shared/models/input/input.service";
import {last} from "rxjs";
import {FullWorkOrderListComponent} from "../full-work-order-list/full-work-order-list.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";

@Component({
  selector: 'app-work-orders-list',
  standalone: true,
  imports: [ContactComponent, DatePipe, NgForOf, NgIf, NgTemplateOutlet, TranslateModule, ButtonComponent],
  templateUrl: './work-orders-list.component.html',
  styleUrl: './work-orders-list.component.scss'
})
export class WorkOrdersListComponent implements OnInit {
  @Input() compactView: Boolean = false
  @Input() company?: CompanyResponse = {} as CompanyResponse;
  @Input() template: WorkOrderResponse = {} as WorkOrderResponse;
  @Input() isQuote: boolean = false;
  workOrders: WorkOrderResponse[] = [];
  orderData: OrderResponse = {} as OrderResponse;
  addresses: StageAddressUnitResponse[] = [];
  showMorePressed = false;
  showLessPressed = true;
  isCrewVisible: boolean[] = [];
  displayCount: number = 1;
  allWorkOrders: WorkOrderResponse[] = [];

  constructor(
    public utilsService: UtilsService,
    private orderService: OrderService,
    private modalService: NgbModal,
    private translate: TranslateService) {
  }

  ngOnInit(): void {
    this.orderService.order$.subscribe(order => {
      if (order.order_id) {
        this.orderData = order;
        this.getAllWorkOrders()
      }
    });
    this.isCrewVisible = this.workOrders.map(() => false);
  }
  toggleCrewVisibility(index: number): void {
    this.isCrewVisible[index] = !this.isCrewVisible[index];
  }


  getAllWorkOrders() {
    if (this.template !== undefined || null) {
      const params: CRM_ORD_161 = {
        order_id: this.orderData.order_id,
        template_filter: false,
        parent_work_order_id: this.template.work_order_id
      };
      this.orderService.getAllWorkOrders(params).subscribe((res: WorkOrderResponse[]) => {
        if(!this.compactView) {
          res = res.filter(workOrder => workOrder.parent_work_order_id === null);
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        res = res.filter(workOrder => {
          const workOrderDate = new Date(workOrder.execution_at);
          return workOrderDate >= today;
        });

        res.sort((a, b) => {
          const dateA = new Date(a.execution_at).getTime();
          const dateB = new Date(b.execution_at).getTime();
          return dateA - dateB;
        });

        this.allWorkOrders = res;

        if(!this.compactView) {
        this.workOrders = res.slice(0, 5);
          }

        if(this.compactView) {
          this.workOrders = res.slice(0, this.displayCount);
        }
      });
    }
  }


  getStatusText(statusId: number): string {
    switch (statusId) {
      case 0:
        return this.translate.instant('order.details.order_scheduled');
      case 1:
        return this.translate.instant('order.details.in_progress');
      case 2:
        return this.translate.instant('order.details.completed');
      case 8:
        return this.translate.instant('order.details.cancelled');
      case 9:
        return this.translate.instant('order.details.crew_on_way');
      default:
        return this.translate.instant('order.details.unknown_status');
    }
  }


  showMoreOrders(): void {
    this.displayCount = 9;
    this.workOrders = this.allWorkOrders.slice(0, this.displayCount);
    this.showMorePressed = true;
    this.showLessPressed = false;
  };

  showLessOrders(): void {
    this.displayCount = 1;
    this.workOrders = this.allWorkOrders.slice(0, this.displayCount);
    this.showMorePressed = false;
    this.showLessPressed = true;
  }


  openAllWorkOrdersModal() {
    const modalRef = this.modalService.open(FullWorkOrderListComponent, { centered: true });
    modalRef.componentInstance.allWorkOrders = this.allWorkOrders;
    modalRef.componentInstance.orderData = this.orderData;
  }

  isMultiDay(workOrder: WorkOrderResponse): boolean {
    if (workOrder && workOrder.execution_at && workOrder.execution_to) {
      const executionAt = new Date(workOrder.execution_at);
      const executionTo = new Date(workOrder.execution_to);
      return executionAt.getDate() !== executionTo.getDate();
    }
    return false;
  }

  protected readonly last = last;
}

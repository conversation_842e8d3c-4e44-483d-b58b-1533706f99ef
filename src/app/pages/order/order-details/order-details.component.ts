import {Component, OnInit, Renderer2} from '@angular/core';
import {BottomNavbarComponent} from "../../../@shared/components/bottom-navbar/bottom-navbar.component";
import {
  CRM_COY_1, CRM_ORD_161, CRM_PAY_35, CRM_PAY_39} from "../../../@shared/models/input/input.service";
import {OrderCustomerQuestionResponse, OrderResponse, WorkOrderResponse} from "../../../@shared/models/order/order.module";
import {OrderService} from "../../../@shared/services/order.service";
import {ActivatedRoute, Router} from "@angular/router";
import {CompanyService} from "../../../@shared/services/company.service";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {TokenService} from "../../../@core/@core/services/token.service";
import {CompanyGeneralSettingsResponse, CompanyResponse} from "../../../@shared/models/company/company.module";
import {TopNavbarComponent} from "../../../@shared/components/top-navbar/top-navbar.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import {interval, last, Subscription} from "rxjs";
import {NotesComponent} from "../../../@shared/components/notes/notes.component";
import {ImportantInformationComponent} from "../../../@shared/components/important-information/important-information.component";
import {SummaryComponent} from "../../../@shared/components/summary/summary.component";
import {PaymentCardComponent} from "../../payment/payment-card/payment-card.component";
import {ButtonComponent} from "../../../@shared/components/button/button.component";
import {ProductService} from "../../../@shared/services/product.service";
import { OrderInformationComponent } from "../../../@shared/components/order-information/order-information.component";
import { ScreenSizeService } from '../../../@shared/services/screen-size.service';
import { ContactComponent } from "../../../@shared/components/contact-address/contact.component";
import { FooterComponent } from "../../../@shared/components/footer/footer.component";
import { PaymentButtonComponent } from "./components/payment-button/payment-button.component";
import {AttachedImagesComponent} from "../../../@shared/components/attached-images/attached-images.component";
import {CustomerQuestionnaireComponent,} from "../../customer-questionnaire/customer-questionnaire.component";
import {RatingComponent} from "../../../@shared/components/rating/rating.component";
import * as Sentry from "@sentry/angular";
import {OrderLinesComponent} from "../../../@shared/components/order-lines/order-lines.component";
import {PaymentService} from "../../../@shared/services/payment.service";
import {OrderPaymentResponse} from "../../../@shared/models/payment/payment.module";
import {PaymentListComponent} from "./components/payment-list/payment-list.component";
import {RepeatingPaymentComponent} from "../../../@shared/components/repeating-payment/repeating-payment.component";
import {PageLoadingSpinnerComponent} from "../../../@shared/components/page-loading-spinner/page-loading-spinner.component";
import {WorkOrdersListComponent} from "./components/work-orders-list/work-orders-list.component";
import {AddPaymentDetailsComponent} from "../../../@shared/components/add-payment-details/add-payment-details.component";
import {PaymentSummaryComponent} from "../../../@shared/components/payment-summary/payment-summary.component";
import {
  RepeatingJobComponent
} from "../../../@shared/components/repeating-job/repeating-job.component";
import {
  OrderLinesSummaryComponent
} from "../../../@shared/components/order-lines-summary/order-lines-summary.component";
import {MatProgressSpinner} from "@angular/material/progress-spinner";
import {CrewReportsComponent} from "../../../@shared/components/crew-reports/crew-reports.component";

@Component({
  selector: 'app-order-details',
  standalone: true,
  imports: [
    BottomNavbarComponent,
    TopNavbarComponent,
    TranslateModule,
    CommonModule,
    NotesComponent,
    ImportantInformationComponent,
    SummaryComponent,
    PaymentCardComponent,
    ButtonComponent,
    OrderInformationComponent,
    // PageLoadingSpinnerComponent,
    ContactComponent,
    FooterComponent,
    PaymentButtonComponent,
    AttachedImagesComponent,
    TranslateModule,
    CustomerQuestionnaireComponent,
    RatingComponent,
    OrderLinesComponent,
    PaymentListComponent,
    RepeatingPaymentComponent,
    PageLoadingSpinnerComponent,
    WorkOrdersListComponent,
    AddPaymentDetailsComponent,
    PaymentSummaryComponent,
    RepeatingJobComponent,
    OrderLinesSummaryComponent,
    MatProgressSpinner,
    CrewReportsComponent,
  ],
  templateUrl: './order-details.component.html',
  styleUrls: ['./order-details.component.scss']
})
export class OrderDetailsComponent implements OnInit {

  companyData?: CompanyResponse;
  orderId: number = 0;
  order: OrderResponse = {} as OrderResponse;
  isPageLoading: boolean = false;
  isMobile!: boolean;
  isMainViewVisible = true;
  companyGeneralSettings: CompanyGeneralSettingsResponse = {} as CompanyGeneralSettingsResponse;
  workOrder: WorkOrderResponse = {} as WorkOrderResponse;
  customerQuestions: OrderCustomerQuestionResponse[] = [];
  showPaymentButton: boolean = false;

  selectedRating: number = 0;
  comment: string = '';
  isReadOnly: boolean = false;

  payments: OrderPaymentResponse[] = [];
  repeatingPayments: OrderPaymentResponse[] = [];
  private pollingSubscription: Subscription | null = null;
  private maxPollingAttempts: number = 15;
  private pollingAttempts: number = 0;
  isUpdatingStatus: boolean = false;
  statusUpdateMessage: string = '';

  constructor(
    private orderService: OrderService,
    private companyService: CompanyService,
    private storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    public utilsService: UtilsService,
    private screenSizeService: ScreenSizeService,
    private router: Router,
    private translate: TranslateService,
    private paymentsService: PaymentService,
    private tokenService: TokenService,
  ) {}

  ngOnInit() {
    // Check if token exists when component is initialized
    console.log('Token in OrderDetailsComponent.ngOnInit:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    this.isPageLoading = true;
    const idParam = this.activatedRoute.snapshot.paramMap.get('orderId');
    const paymentIdParam = this.activatedRoute.snapshot.queryParamMap.get('payment_id'); // Get payment ID from URL
    const fromQuickpay = this.activatedRoute.snapshot.queryParamMap.get('from_quickpay');
    if (idParam) {
      this.orderId = parseInt(idParam, 10);
      if (!isNaN(this.orderId)) {
        this.fetchOrderById(this.orderId);
      } else {
        console.error('Invalid order ID:', idParam);
        this.isPageLoading = false;
      }
    } else {
      console.error('No order ID found in route parameters');
      this.isPageLoading = false;
    }
    this.screenSizeService.isMobile$.subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    Sentry.getCurrentScope().setTags({
      "order_id": this.orderId,
      "user_id": this.storageService.getUser().user_id,
    });
    if (paymentIdParam) {

      const paymentId = parseInt(paymentIdParam, 10);
      if (!isNaN(paymentId) && (fromQuickpay === 'true')) {
        this.isUpdatingStatus = true;
        this.pollPaymentStatus(paymentId); // Start polling the payment status
      } else {
        console.error('Invalid payment ID:', paymentIdParam);
      }
    }
  }


  pollPaymentStatus(paymentId: number) {
    const pollingInterval = interval(2000); // Poll every 2 seconds

    this.pollingSubscription = pollingInterval.subscribe(() => {
      this.pollingAttempts++;
      this.getPayments(paymentId); // Call the updated getPayments method with paymentId
    });
  }

  private removeQueryParamsAndReload(params: string[], reload: boolean = false) {
    const queryParams = { ...this.activatedRoute.snapshot.queryParams };

    params.forEach(param => delete queryParams[param]);

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: queryParams,
      replaceUrl: true
    }).then(() => {
      if (reload) {
        window.location.reload();
      }
    });
  }


  ngOnDestroy() {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
    }
  }


  fetchOrderById(orderId: number) {
    // Check if token exists before making the API call
    console.log('Token in OrderDetailsComponent.fetchOrderById before API call:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    this.orderService.getOrderById(orderId).subscribe({
      next: (order) => {
        if (this.hasOrderData(order) && order.order_id === this.orderId) {
          this.order = order;
          // this.orderLinesValidPaymentCheck()
            this.getAllWorkOrders()
          this.isPageLoading = false;
          if (this.order.company_id) {
            this.fetchCompanyByCustomer();
          }
          this.loadCustomerQuestions();
          if (this.order.order_id) {
            this.getPayments();
          }
          this.selectedRating = this.order.feedback_rating;
          this.comment = this.order.feedback_comment || '';
          this.isReadOnly = this.comment.trim().length > 0;
        } else {
          console.log('Order data is invalid or outdated');
          this.isPageLoading = false;
        }
      },
      error: (error) => {
        console.error('Error fetching order:', error);
        this.isPageLoading = false;
      }
    });
  }


  fetchCompanyByCustomer() {
    const params: CRM_COY_1 = {
      company_id: this.order.company_id
    };
    this.companyService.getCompanyDataByID(params).subscribe({
      next: (res: CompanyResponse) => {
        this.companyData = res;
        document.documentElement.style.setProperty('--primary-color', res.company_color);

        this.storageService.saveSelectedCompany(res);
            this.companyService.getCompanyGeneralSettings().subscribe(res => {
             this.companyGeneralSettings = res;
              });
      },
      error: (error: any) => {
        console.log("error");
      }
    });
  }


  hasOrderData(order: OrderResponse | null): boolean {
    return order !== null && Object.keys(order).length > 0;
  }


  navigateToCQ() {
    // Get the existing TokenService instance
    console.log('Token before navigating to customer questionnaire:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    this.router.navigate([`cq/${this.orderId}`]);
  }


  loadCustomerQuestions() {
    if (this.order?.customer_questions && this.order.customer_questions.length > 0) {
      this.customerQuestions = this.order.customer_questions;
    } else {
      console.log('No customer questions found for this order');
    }
  }


  isSpecificationAnswered(): boolean {
    if (this.order?.customer_questions?.length > 0) {
      return this.order.customer_questions.every(q => q.answered > 0);
    }
    return false;
  }


  hasCustomerQuestions(): boolean {
    return this.order?.customer_questions?.length > 0;
  }


  getPayments(paymentId?: number) {
    // Use the injected TokenService
    console.log('Token before getting payments:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    const params: CRM_PAY_35 = {
      order_id: this.order.order_id,
    };

    this.paymentsService.getPayments(params).subscribe(res => {
      this.payments = res;

      if (paymentId) {
        const payment = res.find(p => p.payment_id === paymentId);
        if (payment && [3, 4, 5, 6, 7, 10, 11].includes(payment.payment_status_id)) {
          this.isUpdatingStatus = false;

          if(payment.payment_status_id == 3) {
            this.statusUpdateMessage = this.translate.instant('payment.details.payment_completed');
          }

          if (this.pollingSubscription) {
            this.pollingSubscription.unsubscribe();
            this.pollingSubscription = null;
          }

          this.removeQueryParamsAndReload(['from_quickpay', 'payment_id']);

          this.orderService.getOrderById(this.orderId).subscribe({
             next: (order) => {
               this.orderService.setOrder(order);
             }});

        } else if (this.pollingAttempts >= this.maxPollingAttempts) {
          this.isUpdatingStatus = false;

          if (this.pollingSubscription) {
            this.pollingSubscription.unsubscribe();
            this.pollingSubscription = null;
          }

          this.removeQueryParamsAndReload(['from_quickpay', 'payment_id'], true);

          window.location.reload()
        }
      }

      this.showPaymentButton = this.payments
        .filter(payment => !payment.disable_customer_portal)
        .some(payment => ![3, 11, 10, 7].includes(payment.payment_status_id));
    });
    this.getRepeatingPayments();
  }


  getAllWorkOrders() {
    const params: CRM_ORD_161 = {
      order_id: this.order.order_id,
    };
    this.orderService.getAllWorkOrders(params).subscribe((res: WorkOrderResponse[]) => {
      this.order.work_orders = res;
    });
  }


  getRepeatingPayments() {
    const params: CRM_PAY_39 = {
      order_id: this.order.order_id,
    };

    this.paymentsService.getRepeatingPayments(params).subscribe(res => {
      this.repeatingPayments = res;
    });
  }


  get orderLinesValidPaymentCheck() {
    const isValid = this.order.order_lines.some(line => line.payment_status_id != 3)
    return isValid;
  }

  protected readonly last = last;
}

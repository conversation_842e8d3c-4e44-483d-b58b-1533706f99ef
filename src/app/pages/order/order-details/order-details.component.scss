@use 'src/styles' as *;
img.rounded-circle {
  border-radius: 50%;
}

.contact-icon {
  transition: color 0.3s;
}

.contact-icon:active {
  color: #336654 !important;
}

.status-icons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
}

.status-item {
  flex: 1;
  text-align: center;
  position: relative;

  &::before, &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 50%;
    height: 2px;
    background-color: #e0e0e0;
    z-index: 0;
  }

  &::before {
    left: 0;
    transform: translateX(-100%);
  }

  &::after {
    right: 0;
    transform: translateX(100%);
  }

  i {
    font-size: 24px;
    color: #212529;
    transition: color 0.3s, background-color 0.3s;
    background: #e0e0e0;
    padding: 10px;
    border-radius: 50%;
    position: relative;
    z-index: 1;
  }

  &.active i {
    color: #fff;
    background: #448c74;
  }

  &.animated i {
    animation: pulse 1.5s infinite;
  }

  &:first-child::before {
    display: none;
  }
  &:last-child::after {
    display: none;
  }
}


.status-update-message {
  background-color: rgba(243, 220, 143, 0.5);
  padding: 15px;
  border-radius: 5px;
}

.status-update-message-paid {
  background-color: rgba(68, 140, 116, 0.5);
  padding: 15px;
  border-radius: 5px;
}



/* Keyframes for the pulsing effect */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(68, 140, 116, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(68, 140, 116, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(68, 140, 116, 0);
  }
}



import {Component, OnInit} from '@angular/core';
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {CompanyService} from "../../../@shared/services/company.service";
import {ActivatedRoute, Router} from "@angular/router";
import {CompanyResponse} from "../../../@shared/models/company/company.module";
import {CRM_COY_1} from "../../../@shared/models/input/input.service";
import {TranslateModule} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {TopNavbarComponent} from "../../../@shared/components/top-navbar/top-navbar.component";
import {BottomNavbarComponent} from "../../../@shared/components/bottom-navbar/bottom-navbar.component";
import {FooterComponent} from "../../../@shared/components/footer/footer.component";

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [TranslateModule, CommonModule, TopNavbarComponent, BottomNavbarComponent, FooterComponent],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit {
  orderId: number | null = null;
  companyData: CompanyResponse = {} as CompanyResponse;


  constructor(
    private utilsService: UtilsService,
    public storageService: StorageService,
    private companyService: CompanyService,
    private route: ActivatedRoute,
    private router: Router) {
  }

  ngOnInit(): void {
    let companyId = this.route.snapshot.paramMap.get('company_id');
    let params: CRM_COY_1 = {
      company_id: companyId!
    }
    if(companyId) {
      this.companyService.getCompanyDataByID(params).subscribe(res => {
        this.companyData = res;
        console.log('companyData', this.companyData);
      });
    }
  }

  backBtnClick() {
    this.router.navigate([`/orders-overview`]);
  }

}


<app-top-navbar [showBackButton]="true" [companyData]="companyData"></app-top-navbar>
<div class="wrapper d-flex flex-column min-vh-100">
  <div class="page-container container d-flex align-items-center justify-content-center flex-column flex-grow-1">
    <div class="contact-card card p-4">
      <h2 class="card-title text-center">{{ companyData.company_name }}</h2>
      <ul class="list-group list-group-flush">
        <li class="list-group-item" *ngIf="companyData?.address?.display">
          <strong>{{ "contact.companyAddress" | translate }}:</strong> {{ companyData.address.display }}
        </li>
        <li class="list-group-item">
          <strong>{{ "contact.companyEmail" | translate }}:</strong> {{ companyData.email }}
        </li>
        <li class="list-group-item">
          <strong>{{ "contact.companyPhone" | translate }}:</strong> {{ companyData.phone }}
        </li>
        <li class="list-group-item">
          <strong>{{ "contact.companyOrgNumber" | translate }}:</strong> {{ companyData.organisation_number }}
        </li>
      </ul>
    </div>
  </div>
  <app-footer class="mt-auto"></app-footer>
</div>

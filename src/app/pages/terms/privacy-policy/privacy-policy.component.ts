import { Component, OnInit } from '@angular/core';
import { FooterComponent } from "../../../@shared/components/footer/footer.component";
import { TopNavbarComponent } from "../../../@shared/components/top-navbar/top-navbar.component";
import { UtilsService } from '../../../@core/@core/utils/utils.service';
import { StorageService } from '../../../@core/@core/services/storage.service';
import { CompanyService } from '../../../@shared/services/company.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyResponse } from '../../../@shared/models/company/company.module';
import { TranslateModule } from '@ngx-translate/core';
import { BottomNavbarComponent } from '../../../@shared/components/bottom-navbar/bottom-navbar.component';
import { CRM_COY_1, CRM_ORD_43 } from '../../../@shared/models/input/input.service';

@Component({
  selector: 'app-privacy-policy',
  standalone: true,
  imports: [TranslateModule, TopNavbarComponent, BottomNavbarComponent, FooterComponent],
  templateUrl: './privacy-policy.component.html',
  styleUrl: './privacy-policy.component.scss'
})
export class PrivacyPolicyComponent implements OnInit {

    constructor(
      private utilsService: UtilsService,
      private storageService: StorageService,
      private companyService: CompanyService,
      private route: ActivatedRoute,
      private router: Router) {}


    orderId : number | null = null;
    companyData : CompanyResponse= {} as CompanyResponse;
    companyId: string | null = '';
    privacyHTML: string = '';
    termsHTML: string = '';

    ngOnInit(): void {
      this.companyId = this.route.snapshot.paramMap.get('company_id');
      this.orderId = this.storageService.getOrderId();
      // this.fetchCompany()
      if(this.companyData.company_id){
        this.fetchCompanyByCustomer(this.companyData.company_id);
        console.log("order id", this.orderId)
      }
      this.companyService.getCompanyPrivacyPolicy(this.companyId!).subscribe(res => {
        this.privacyHTML = res;
      });
      this.companyService.getCompanyTerms(this.companyId!).subscribe(res => {
        this.termsHTML = res;
      });
    }

    fetchCompany(){
      const payload: CRM_COY_1 = {
        company_id: this.companyId!
      }
      this.companyService.getCompanyDataByID(payload).subscribe(res => {
        this.companyData = res;
      });
    }

    fetchCompanyByCustomer(company_id : string){
      const params : CRM_COY_1 = {
        company_id : company_id
      }
      this.companyService.getCompanyDataByID(params).subscribe(res => {
        this.companyData = res;


        this.storageService.saveSelectedCompany(res);
      })
    }

    backBtnClick(){
      this.router.navigate([`/orders-overview`]);
    }

  }

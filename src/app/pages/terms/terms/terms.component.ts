import {Component, OnInit} from '@angular/core';
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {StorageService} from "../../../@core/@core/services/storage.service";
import {CompanyService} from "../../../@shared/services/company.service";
import {ActivatedRoute, Router} from "@angular/router";
import {CompanyResponse} from "../../../@shared/models/company/company.module";
import {CRM_COY_1, CRM_ORD_43} from "../../../@shared/models/input/input.service";
import {TranslateModule} from "@ngx-translate/core";
import {TopNavbarComponent} from "../../../@shared/components/top-navbar/top-navbar.component";
import {BottomNavbarComponent} from "../../../@shared/components/bottom-navbar/bottom-navbar.component";
import {FooterComponent} from "../../../@shared/components/footer/footer.component";

@Component({
  selector: 'app-terms',
  standalone: true,
  imports: [TranslateModule, TopNavbarComponent, BottomNavbarComponent, FooterComponent],
  templateUrl: './terms.component.html',
  styleUrl: './terms.component.scss'
})
export class TermsComponent implements OnInit{

  constructor(
    private utilsService: UtilsService,
    private storageService: StorageService,
    private companyService: CompanyService,
    private route: ActivatedRoute,
    private router: Router) {}


  orderId : number | null = null;
  companyData : CompanyResponse= {} as CompanyResponse;
  companyId: string | null = '';
  privacyHTML: string = '';
  termsHTML: string = '';

  ngOnInit(): void {
    this.companyId = this.route.snapshot.paramMap.get('company_id');
    this.orderId = this.storageService.getOrderId();
    if(this.orderId){
      if(this.companyData) {
        this.fetchCompany()
      }
      // this.fetchCompanyByCustomer(this.orderId);
      console.log("order id", this.orderId)
    }

    // this.companyService.getCompanyPrivacyPolicy(this.companyId!).subscribe(res => {
    //   this.privacyHTML = res;
    // });
    this.companyService.getCompanyTerms(this.companyId!).subscribe(res => {
      this.termsHTML = res;
    });
  }

  fetchCompany(){
    const payload: CRM_COY_1 = {
      company_id: this.companyId!
    }
    this.companyService.getCompanyDataByID(payload).subscribe(res => {
      this.companyData = res;
    });
  }

  // fetchCompanyByCustomer(orderId : number){
  //   const params : CRM_ORD_43 = {
  //     order_id : orderId
  //   }
  //   this.companyService.getCompanyDataByCustomer(params).subscribe(res => {
  //     this.companyData = res;
  //
  //     //save company
  //     this.storageService.saveSelectedCompany(res);
  //   })
  // }

  backBtnClick(){
    this.router.navigate([`/orders-overview`]);
  }
}

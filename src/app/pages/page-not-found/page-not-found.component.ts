import { CommonModule } from '@angular/common';
import {Component, OnInit} from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FooterComponent } from "../../@shared/components/footer/footer.component";

@Component({
  selector: 'app-page-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule, FooterComponent],
  templateUrl: './page-not-found.component.html',
  styleUrl: './page-not-found.component.scss'
})
export class PageNotFoundComponent implements OnInit{

  attemptedUrl: string | null = null;

  constructor(private router: Router, private route: ActivatedRoute, public translate: TranslateService) { }

  ngOnInit(): void {
    // Capture the attempted URL
    this.attemptedUrl = this.router.url;
    console.error(`Error: Page not found - ${this.attemptedUrl}`);
  }
}

/* page-not-found.component.scss */
.page-not-found {
    text-align: center;
    margin-top: 50px;
  }

  .page-not-found a {
    font-size: 1.2rem;
    color: #007bff;
    text-decoration: none;
  }

  .page-not-found a:hover {
    text-decoration: underline;
  }


.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* Ensure it spans the full viewport height */
  padding: 1rem 0 0 0 !important;
}

app-footer {
  margin-top: auto;
}

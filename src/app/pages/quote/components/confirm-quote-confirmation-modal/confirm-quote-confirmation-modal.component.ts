import {Component, Input} from '@angular/core';
import {OrderResponse} from "../../../../@shared/models/order/order.module";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";

@Component({
  selector: 'app-confirm-quote-confirmation-modal',
  standalone: true,
  imports: [CommonModule, TranslateModule, ButtonComponent],
  templateUrl: './confirm-quote-confirmation-modal.component.html',
  styleUrl: './confirm-quote-confirmation-modal.component.scss'
})
export class ConfirmQuoteConfirmationModalComponent {
  @Input() order: OrderResponse | undefined;

  constructor(public activeModal: NgbActiveModal) {}

  confirm() {
    this.activeModal.close('confirm');
  }
}

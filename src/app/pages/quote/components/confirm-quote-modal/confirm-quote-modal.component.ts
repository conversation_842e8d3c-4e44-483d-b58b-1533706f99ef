import {Component, CUSTOM_ELEMENTS_SCHEMA, Input} from '@angular/core';
import '@dotlottie/player-component';
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {OrderResponse} from "../../../../@shared/models/order/order.module";

@Component({
  selector: 'app-confirm-quote-modal',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './confirm-quote-modal.component.html',
  styleUrl: './confirm-quote-modal.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ConfirmQuoteModalComponent {
  @Input() order!: OrderResponse;
  constructor(public activeModal: NgbActiveModal) {}

  //
  // confirm() {
  //   this.activeModal.close('confirm');
  // }
  modalClose(): void {
    this.activeModal.close('confirm');
  }

  dismissModal(): void {
    this.activeModal.dismiss('cancel');
  }

}

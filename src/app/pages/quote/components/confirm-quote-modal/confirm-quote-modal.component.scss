
@import 'src/styles';

.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  position: relative;
  max-width: 500px;
  width: 90%;
}

.modal-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.modal-content h2 {
  margin-top: 0;
  color: #333333;
}

.modal-content p {
  color: #666666;
  font-size: 16px;
  margin: 20px 0;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 28px;
  color: #aaaaaa;
  cursor: pointer;
}

.close-button:hover {
  color: #000000;
}

.btn-primary {
  background-color: map-get($theme-colors, "primary");
  border: none;
  color: white;
}

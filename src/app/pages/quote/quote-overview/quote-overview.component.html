<div class="overview-container">
  <div class="mb-3">
    <img src="../../../../assets/images/Logo_positive_medium.png" class="mb-4" style="width: 200px;" alt="between logo">
  </div>
  <div class="fs-4 mb-2">
    {{ "order-list.hello" | translate }} {{userData.first_name}}! <i class="fa-regular fa-hand-wave" style="color: #FFD43B;"></i>
  </div>
  <div class="fs-2 mb-2">
    {{ "order-list.title" | translate }}
  </div>

  <div>
    <div *ngIf="dataLoading" class="text-center">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>

<!--    <div *ngIf="!dataLoading && filteredOrders.length === 0" class="text-center">-->
<!--      <p>{{ "order-list.noOrders" | translate }}</p>-->
<!--    </div>-->

    <div *ngIf="!dataLoading">
      <!-- Content for orders -->
      <div class="order-container mb-3 border rounded-4" *ngFor="let order of orders">
        {{ order.main_product_name }}
        <hr style="margin-bottom: 0.5rem; margin-top: 0.5rem; border-width: 2px ">
        <div class="mb-3">{{ order.company_name }}</div>
        <div class="d-flex justify-content-between mb-2">
          <span>{{ "quotes.orderDate" | translate }}</span>
          <span>{{ utilsService.formatDateTime(order.execution_at!, 'EEEE, DD. MMMM yyyy') }}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>{{ "quotes.time" | translate }}</span>
          <span>{{ utilsService.formatDateTime(order.execution_at!, 'HH:mm') }}
            - {{ utilsService.formatDateTime(order.execution_to!, 'HH:mm') }}</span>
        </div>
        <div class="d-flex justify-content-center">
          <app-button
          buttonClass="btn btn-primary small"
          (buttonClick)="openDetails(order)">
          {{ "quote.viewDetials" | translate }}
        </app-button>
        </div>
      </div>

      <!-- Pagination controls -->
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <a class="page-link" (click)="onPrev()">{{ "order-list.previous" | translate }}</a>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <a class="page-link" (click)="onNext()">{{ "order-list.next" | translate }}</a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>
<app-footer></app-footer>

import { Component, OnInit } from '@angular/core';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import { OrderService } from "../../../@shared/services/order.service";
import {OrderResponse, OrderScheduleResponse} from "../../../@shared/models/order/order.module";
import {CRM_ORD_29} from "../../../@shared/models/input/input.service";
import { CommonModule } from "@angular/common";
import {UtilsService} from "../../../@core/@core/utils/utils.service";
import {ButtonComponent} from "../../../@shared/components/button/button.component";
import { Router} from "@angular/router";
import {StorageService} from "../../../@core/@core/services/storage.service";
import { BottomNavbarComponent } from '../../../@shared/components/bottom-navbar/bottom-navbar.component';
import {TabsMenuModel} from "../../../@shared/models/tabs-menu/tabs-menu";
import {AuthService} from "../../../@core/@core/services/auth.service";
import {UserResponse} from "../../../@shared/models/user/user.module";
import {FooterComponent} from "../../../@shared/components/footer/footer.component";

@Component({
  selector: 'app-quote-overview',
  standalone: true,
  imports: [TranslateModule, CommonModule, ButtonComponent, BottomNavbarComponent, FooterComponent],
  templateUrl: './quote-overview.component.html',
  styleUrls: ['./quote-overview.component.scss']
})
export class QuoteOverviewComponent implements OnInit {
  orders: OrderResponse[] = [];
  subscriptions: OrderScheduleResponse[] = [];
  dataLoading: boolean = false;
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 0;
  activeTab: string = "orders";
  userData: UserResponse = {} as UserResponse;
  tabs: TabsMenuModel[] = [
    {
      name: this.translate.instant("order-list.orders"),
      value: 'orders',
      translationKey: "order-list.orders"
    },
    {
      name: this.translate.instant('order-list.subscriptions'),
      value: 'subscriptions',
      translationKey: 'order-list.subscriptions'
    }
  ];


  constructor(
    public utilsService: UtilsService,
    private orderService: OrderService,
    private storageService: StorageService,
    public translate: TranslateService,
    private router: Router,
    private authService: AuthService
  ) {

  }

  ngOnInit() {
    this.fetchOrders();
    this.translate.get("order-list.orders").subscribe(() => {
      this.tabs = [
        {
          name: this.translate.instant("order-list.orders"),
          value: 'orders',
          translationKey: "order-list.orders"
        },
        {
          name: this.translate.instant('order-list.subscriptions'),
          value: 'subscriptions',
          translationKey: 'order-list.subscriptions'
        }
      ];
    });
    this.authService.fetchUser().subscribe({
      next: response => {
        this.userData = response;
      },
      error: error => {
        console.error("Error:", error);
      },
    });
  }

  fetchOrders(): void {
    this.dataLoading = true;
    const params: CRM_ORD_29 = {
      paginate: 1,
      limit: this.pageSize,
      page: this.currentPage,
      order_status_ids: [0],
    };

    this.orderService.getOrdersAsUser(params).subscribe({
      next: (res) => {
        if (res.data.length > 0) {
          this.orders = res.data;
          this.totalPages = res.total_pages;
        }
        this.dataLoading = false;
      },
      error: (error) => {
        console.error('Error fetching orders:', error);
        this.dataLoading = false;
      }
    });
  }


  fetchSubscriptions(): void {
    this.dataLoading = true;
    this.orderService.getSubscriptions().subscribe({
      next: (res) => {
        this.subscriptions = res;
        console.log("SUBSCRIPTION", res);
        this.dataLoading = false;
      },
      error: (error) => {
        console.error('Error fetching subscriptions:', error);
        this.dataLoading = false;
      }
    });
  }

  goToPage(n: number): void {
    if (n > this.totalPages || n < 1) {
      return;
    }
    this.currentPage = n;
    this.fetchOrders();
  }

  onNext(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.fetchOrders();
    }
  }

  onPrev(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.fetchOrders();
    }
  }

  handleActiveTab(value: string) {
    this.currentPage = 1;
    this.activeTab = value;
    if (value === 'subscriptions') {
      this.fetchSubscriptions();
    } else {
      this.fetchOrders();
    }
  }

  openDetails(order: OrderResponse) {
    this.router.navigate(['/quote-details/' + order.order_id]);
  }

  openSubscriptionDetails(subscription: OrderScheduleResponse) {
    this.router.navigate(['/subscriptions/' + subscription.order_schedule_id]);
  }
}

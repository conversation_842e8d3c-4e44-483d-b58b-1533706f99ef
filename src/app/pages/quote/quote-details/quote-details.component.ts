import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {
  CRM_COY_1,
  CRM_ORD_161,
  CRM_ORD_40,
   CRM_ORD_47,
  USM_USR_9
} from "../../../@shared/models/input/input.service";
import { CompanyService } from "../../../@shared/services/company.service";
import { ActivatedRoute } from "@angular/router";
import { StorageService } from "../../../@core/@core/services/storage.service";
import { CompanyGeneralSettingsResponse, CompanyResponse } from "../../../@shared/models/company/company.module";
import { OrderService } from "../../../@shared/services/order.service";
import {OrderResponse, WorkOrderResponse} from "../../../@shared/models/order/order.module";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { UtilsService } from "../../../@core/@core/utils/utils.service";
import { NotesComponent } from "../../../@shared/components/notes/notes.component";
import {CommonModule, ViewportScroller} from "@angular/common";
import { AttachedImagesComponent } from "../../../@shared/components/attached-images/attached-images.component";
import { UpsellComponent } from "../../../@shared/components/upsell/upsell.component";
import {ImportantInformationComponent} from "../../../@shared/components/important-information/important-information.component";
import { SummaryComponent } from "../../../@shared/components/summary/summary.component";
import { TopNavbarComponent } from "../../../@shared/components/top-navbar/top-navbar.component";
import { FooterComponent } from "../../../@shared/components/footer/footer.component";
import { ButtonComponent } from "../../../@shared/components/button/button.component";
import { PageLoadingSpinnerComponent } from "../../../@shared/components/page-loading-spinner/page-loading-spinner.component";
import { ScreenSizeService } from '../../../@shared/services/screen-size.service';
import { OrderInformationComponent } from "../../../@shared/components/order-information/order-information.component";
import { ContactComponent } from "../../../@shared/components/contact-address/contact.component";
import {AuthService} from "../../../@core/@core/services/auth.service";
import {OTPModalComponent} from "../../../@shared/components/otpmodal/otpmodal.component";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastService} from "../../../@core/@core/services/toast.service";
import {ToastsContainer} from "../../../@shared/components/toast/toast-container";
import * as Sentry from "@sentry/angular";
import {OrderLinesComponent} from "../../../@shared/components/order-lines/order-lines.component";
import {
  RepeatingPaymentComponent
} from "../../../@shared/components/repeating-payment/repeating-payment.component";
import {
  WorkOrdersListComponent
} from "../../order/order-details/components/work-orders-list/work-orders-list.component";
import {ConfirmQuoteModalComponent} from "../components/confirm-quote-modal/confirm-quote-modal.component";
import {OrderPaymentResponse} from "../../../@shared/models/payment/payment.module";
import {PaymentService} from "../../../@shared/services/payment.service";
import {
  OrderLinesSummaryComponent
} from "../../../@shared/components/order-lines-summary/order-lines-summary.component";
import {
  RepeatingJobComponent
} from "../../../@shared/components/repeating-job/repeating-job.component";
import {ProductService} from "../../../@shared/services/product.service";
import {ProductInformationResponse} from "../../../@shared/models/product/product.module";
import {
  ConfirmQuoteConfirmationModalComponent
} from "../components/confirm-quote-confirmation-modal/confirm-quote-confirmation-modal.component";

@Component({
  selector: 'app-quote-details',
  standalone: true,
  imports: [TranslateModule, OTPModalComponent, NotesComponent, CommonModule, AttachedImagesComponent, UpsellComponent, ImportantInformationComponent, SummaryComponent, TopNavbarComponent, FooterComponent, ButtonComponent, PageLoadingSpinnerComponent, OrderInformationComponent, ContactComponent, ToastsContainer, OrderLinesComponent, RepeatingPaymentComponent, WorkOrdersListComponent, OrderLinesSummaryComponent, RepeatingJobComponent],
  templateUrl: './quote-details.component.html',
  styleUrls: ['./quote-details.component.scss']
})

export class QuoteDetailsComponent implements OnInit {
  companyData?: CompanyResponse;
  orderId: number = 0;
  order!: OrderResponse;
  isQuote: boolean = true;
  isLoading: boolean = false;
  isPageLoading: boolean = false;
  isMobile!: boolean;
  productId: number = 0;
  isAllInformationChecked: boolean = false;
  companyGeneralSettings: CompanyGeneralSettingsResponse = {} as CompanyGeneralSettingsResponse;
  repeatingPayments: OrderPaymentResponse[] = [];
  payments: OrderPaymentResponse[] = [];
  productInfos: ProductInformationResponse[] = [];
  @Output() otpStatusResponse: EventEmitter<any> = new EventEmitter<any>();
  @Output() otpConfirmed: EventEmitter<any> = new EventEmitter<any>();
  showRequiredError: boolean = false;

  constructor(
    private orderService: OrderService,
    private authService: AuthService,
    private companyService: CompanyService,
    private activatedRoute: ActivatedRoute,
    private storageService: StorageService,
    public utilsService: UtilsService,
    private screenSizeService: ScreenSizeService,
    private paymentService: PaymentService,
    private modalService: NgbModal,
    private translate: TranslateService,
    private toastService: ToastService,
    private productService: ProductService,
    private viewportScroller: ViewportScroller
) {}

  ngOnInit(): void {
    this.isPageLoading = true;
    const idParam = this.activatedRoute.snapshot.paramMap.get('orderId');
    if (idParam) {
      this.orderId = parseInt(idParam, 10);
      this.orderService.order$.subscribe(order => {
        this.order = order;
        if (order.order_lines.length > 0 ) {
          this.productId = this.utilsService.getOrderData(this.order, 'product_id') as number;
        }
        this.getRepeatingPayments()
        this.isPageLoading = false;
        // if(this.order.)
        if(this.order.company_id) {
          this.fetchCompanyByCustomer();
          this.getAllWorkOrders()
        }
      });
      this.checkForRequiredItems();

      Sentry.getCurrentScope().setTags({
        "order_id": this.orderId,
        "user_id": this.storageService.getUser().user_id,
      });
    }
  }



  checkForRequiredItems() {
    if(this.order.order_lines.length > 0 && this.productId) {
      this.productService.getProductInformation(this.productId, this.order.company_id).subscribe(res => {
        this.productInfos = res;
    const hasRequiredItems = this.productInfos.some(
      (info) => info.require_ack_on_important_information === 1
    );
    // Set isAllInformationChecked based on the result
    this.isAllInformationChecked = !hasRequiredItems;
      });
    }else{
      this.isAllInformationChecked = true;
    }
  }


  fetchCompanyByCustomer() {
    const params: CRM_COY_1= {
      company_id: this.order.company_id
    };
    this.companyService.getCompanyDataByID(params).subscribe({
      next: (res: CompanyResponse) => {
        this.companyData = res;
        this.storageService.saveSelectedCompany(res);
            this.companyService.getCompanyGeneralSettings().subscribe(res => {
            this.companyGeneralSettings = res;
            });
      },
      error: (error: any) => {
        console.log(error);
      }
    });
  }

  hasOrderData(order: OrderResponse | null): boolean {
    return order !== null && Object.keys(order).length > 0;
  }

  onConfirmQuote(): void {
    // 1) Check required items first
    if (!this.isAllInformationChecked) {
      this.toastService.showError(
        this.translate.instant('order-summary.confirmQuoteInformationErrorPopup')
      );
      // Scroll to bottom to show error
      this.viewportScroller.scrollToPosition([0, document.body.scrollHeight]);
      this.showRequiredError = true;
      return;
    }

    // 2) If everything is ok, show the confirmation modal
    this.showRequiredError = false;
    const modalRef = this.modalService.open(ConfirmQuoteConfirmationModalComponent, { centered: true });
    modalRef.componentInstance.order = this.order; // if you want to pass the order

    modalRef.result
      .then((result) => {
        if (result === 'confirm') {
          // 3) The user clicked "confirm", so do your final confirm logic
          this.acceptOrderAsCustomer();
        }
      })
      .catch((error) => {
        console.error('Modal dismissed:', error);
      });
  }




    //
    // // If true, proceed with the rest of your method
    // let modalRef = this.modalService.open(OTPModalComponent, { centered: true });
    // modalRef.componentInstance.order = this.order;
    // modalRef.componentInstance.redirect = false;
    // modalRef.componentInstance.otpConfirmed.subscribe((confirmed: boolean) => {
    //   if (confirmed) {
    //     this.acceptOrderAsCustomer();
    //   }
    //   this.otpStatusResponse.emit(confirmed);
    //   window.scrollTo(0, 0); // Scroll to the top of the page
    // });



  acceptOrderAsCustomer() {
    this.isLoading = true;
    const params: CRM_ORD_40 = {
      order_id: this.order!.order_id
    };
    console.log(this.isAllInformationChecked, "isAllInformationChecked accept");

    this.orderService.acceptOrderAsCustomer(params).subscribe({
      next: () => {
        this.isLoading = false;
        this.orderService.fetchAndUpdateOrder( this.orderId);
      },
      error: (error) => {
        console.log(error);
        this.isLoading = false;
        if (error.status === 403 && error.error && error.error.data) {
          let params: CRM_ORD_47 = {
            order_id: this.order!.order_id,
            order_line: {
              order_line_id: error.error.data.order_line_id,
              product_id: error.error.data.product_id,
              quantity: error.error.data.stock_quantity
            }
          };
          this.orderService.patchOrderLine(params).subscribe({
            next: (res) => {
              this.order = res;
            },
            error: (err) => {
              console.log(err);
            }
          });
        }
      }
    });
  }

  initiateOtp(phone: string) {
    const params: USM_USR_9 = {
      phone: phone.replace(/\s/g, '+')
    };
    this.authService.initiateOtp(params).subscribe({
      next: () => {},
      error: (error) => {
        console.log(error);
      }
    });
  }

  isRequiredchecked(event: boolean){
    this.isAllInformationChecked = event;
    console.log("EVENT", event);
    if (event) {
      this.showRequiredError = false; // Reset error when checkbox is checked
    }
  }

  convertDecimalHoursToHoursAndMinutes(decimalHours: number): string {
    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);
    return `${hours}h ${minutes}m`;
  }

  getAllWorkOrders() {
    const params: CRM_ORD_161 = {
      order_id: this.order.order_id,
    };
    this.orderService.getAllWorkOrders(params).subscribe((res: WorkOrderResponse[]) => {
      this.order.work_orders = res;
    });
  }

  getRepeatingPayments() {
    const payload = {
      order_id: this.order!.order_id,
    };

    this.paymentService.getRepeatingPayments(payload).subscribe(res => {
      this.repeatingPayments = res;

      const hasActiveSubscription = this.repeatingPayments.some(payment => payment.subscription_active);

      if (hasActiveSubscription && this.order.order_status_id === 0) {
        this.acceptOrderAsCustomer();
      }
    });

    this.paymentService.getPayments(payload).subscribe(res => {
      this.payments = res;
    });
  }

  get orderLinesValidPaymentCheck() {
    const isValid = this.order.order_lines.some(line => line.payment_status_id != 3)
    return isValid;
  }
  //
  // get quoteDaysLeft(): number | null {
  //   if (!this.order?.quote_sent_at || !this.companyGeneralSettings?.quote_expiry_days) {
  //     return null;
  //   }
  //   const sentAt = new Date(this.order.quote_sent_at);
  //   const expiryDate = new Date(sentAt);
  //   expiryDate.setDate(expiryDate.getDate() + this.companyGeneralSettings.quote_expiry_days);
  //   const now = new Date();
  //   const diffInMs = expiryDate.getTime() - now.getTime();
  //   const daysLeft = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  //   return daysLeft > 0 ? daysLeft : 0;
  // }

  // get isQuoteExpired(): boolean {
  //   if (!this.order?.quote_sent_at || !this.companyGeneralSettings?.quote_expiry_days) {
  //     return false;
  //   }
  //   const sentAt = new Date(this.order.quote_sent_at);
  //   const expiryDate = new Date(sentAt);
  //   expiryDate.setDate(expiryDate.getDate() + this.companyGeneralSettings.quote_expiry_days);
  //   return new Date() > expiryDate;
  // }


}

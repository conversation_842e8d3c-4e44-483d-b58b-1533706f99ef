.contact-container{
  padding: 0.75rem;
}


.payment-button-container {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 10px 0;
  box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.payment-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-primary {
  white-space: nowrap;
}

.app-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* ensure at least full screen height */
}

.bottom-section {
  margin-top: auto;
}

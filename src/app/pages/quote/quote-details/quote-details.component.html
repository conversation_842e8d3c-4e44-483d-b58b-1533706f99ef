<div class="app-wrapper">
<app-top-navbar *ngIf="companyData" [companyData]="companyData" [order]="order"></app-top-navbar>
<app-page-loading-spinner *ngIf="isPageLoading"></app-page-loading-spinner>

<div class="row page-container m-0" style="flex:1">
<app-order-information *ngIf="order"></app-order-information>
  <div class="col-12 col-md-7">
<!--    <div *ngIf="companyGeneralSettings.quote_expiry_days && order.quote_sent_at" class="alert alert-info d-flex align-items-center">-->
<!--      <i class="fa-regular fa-clock me-2"></i>-->
<!--      <span>-->
<!--    {{ "order-summary.quoteDaysLeft" | translate }}: {{ quoteDaysLeft }}-->
<!--        {{ quoteDaysLeft === 1 ? ("order-summary.day" | translate) : ("order-summary.days" | translate) }}-->
<!--  </span>-->
<!--    </div>-->

    <app-work-orders-list *ngIf="order" [company]="companyData" [isQuote]="isQuote"></app-work-orders-list>

    <div class="col-12 d-block d-md-none">

      <div *ngIf="order.order_lines!.length > 0 && (!order.repeating || order.project) && orderLinesValidPaymentCheck">
        <app-order-lines [orderLines]="order.order_lines" [payments]="payments"></app-order-lines>
      </div>
      <app-repeating-job [compactView]="true"></app-repeating-job>
      <app-repeating-payment></app-repeating-payment>
    </div>

    <app-contact-address [companyData]="companyData" [seller]="true" [orderData]="order"></app-contact-address>

    <div class="mb-4">
      <app-notes [orderData]="order!"></app-notes>
    </div>

    <div class="mb-4">
      <app-attached-images *ngIf="order"></app-attached-images>
    </div>

    <div class="mb-4">
      <app-upsell *ngIf="hasOrderData(order) && order.hide_payment_data != 1"></app-upsell>
    </div>


    <div class="mb-4">
      <div id="important-information-section">
        <app-important-information *ngIf="hasOrderData(order)" [showRequiredError]="showRequiredError" (allRequiredChecked)="isRequiredchecked($event)"></app-important-information>
      </div>
    </div>
  </div>

  <div class="col-12 col-md-5 d-none d-md-block">
    <div *ngIf="order?.order_lines!.length > 0 && (!order.repeating || order.project) && orderLinesValidPaymentCheck">
      <app-order-lines [orderLines]="order.order_lines"></app-order-lines>
    </div>
    <app-repeating-job [compactView]="true"></app-repeating-job>
    <app-repeating-payment *ngIf="order.hide_payment_data != 1"></app-repeating-payment>
  </div>
</div>

  <div class="payment-button-container p-3 py-4">
    <div class="payment-content container">
      <div class="row justify-content-center">
        <div class="col-12 col-md-auto">
          <app-button  [isLoading]="isLoading" [buttonClass]="isMobile ? 'btn btn-primary w-100' : 'btn btn-primary'" (buttonClick)="onConfirmQuote()">{{ "order-summary.confirmQuoteBtn" | translate }}</app-button>
        </div>
      </div>
    </div>
  </div>
<app-footer *ngIf="!isPageLoading"></app-footer>
</div>

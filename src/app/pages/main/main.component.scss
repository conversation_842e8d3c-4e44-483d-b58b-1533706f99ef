@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300&display=swap');

.maintenance-container {
  display: flex;
  flex-direction: column;
  justify-content: center; /* Vertically center */
  align-items: center;     /* Horizontally center */
  min-height: 100vh;
  padding: 0 20px;
  text-align: center;
  font-family: 'Open Sans', Arial, sans-serif; /* Elegant font */
  color: #333;
}

.maintenance-container dotlottie-player {
  width: 300px;
  height: 300px;
  margin-bottom: 1em;
}

/* Loading Bar Styles */
.loading-bar-container {
  width: 80%;
  max-width: 400px;
  margin-bottom: 1.5em;
}

.loading-bar {
  width: 100%;
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}


.loading-progress {
  width: 60%; /* Full width */
  height: 100%;
  background-color: #448C74;
  animation: blinking 1.5s infinite;
  border-radius: 5px;
}

/* Blinking animation */
@keyframes blinking {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Existing styles for text */
.maintenance-container h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
  font-weight: 300; /* Lighter weight for elegance */
}

.maintenance-container p {
  font-size: 1.2em;
  margin: 0.3em 0;
  font-weight: 300;
}

import {Component, OnInit} from '@angular/core';
import {OrderDetailsComponent} from "../order/order-details/order-details.component";
import {PaymentComponent} from "../payment/payment.component";
import {OrderResponse, ScheduleStageAddressUnitResponse} from "../../@shared/models/order/order.module";
import {CompanyResponse} from "../../@shared/models/company/company.module";
import {OrderService} from "../../@shared/services/order.service";
import {StorageService} from "../../@core/@core/services/storage.service";
import {TokenService} from "../../@core/@core/services/token.service";
import {ActivatedRoute} from "@angular/router";
import {UtilsService} from "../../@core/@core/utils/utils.service";
import {CommonModule} from "@angular/common";
import {CustomerQuestionnaireComponent} from "../customer-questionnaire/customer-questionnaire.component";
import {QuoteDetailsComponent} from "../quote/quote-details/quote-details.component";
import * as Sentry from "@sentry/angular";

@Component({
  selector: 'app-main',
  standalone: true,
  imports: [
    OrderDetailsComponent,
    PaymentComponent,
    CommonModule,
    CustomerQuestionnaireComponent,
    QuoteDetailsComponent
  ],
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss',
})
export class MainComponent implements OnInit{
  orderId : number = 0;
  order: OrderResponse  = {} as OrderResponse;
  companyData: CompanyResponse = {} as CompanyResponse;
  addresses: ScheduleStageAddressUnitResponse[] = [];
  isLoading: boolean = false;
  orderData: OrderResponse = {} as OrderResponse;


  constructor(
    private orderService: OrderService,
    private storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    public utilsService: UtilsService,
    private tokenService: TokenService,
  ){}

  ngOnInit() {
    // Check if token exists when component is initialized
    console.log('Token in MainComponent.ngOnInit:', this.tokenService.getToken() ? 'Token exists' : 'Token is null');

    const idParam = this.activatedRoute.snapshot.paramMap.get('orderId');
    // Subscribe to queryParams to check for from_quickpay
    this.activatedRoute.queryParams.subscribe(params => {
      const fromQuickpay = params['from_quickpay'] === 'true';
      if (fromQuickpay) {
        setTimeout(() => {
          this.orderService.fetchAndUpdateOrder (this.orderId);
        }, 300);
      }
    });

    if (idParam) {
      this.orderId = parseInt(idParam, 10);
      if (!isNaN(this.orderId)) {
        this.orderService.fetchAndUpdateOrder(this.orderId);
        this.orderService.order$.subscribe(order => {
          this.order = order;
        });
      } else {
        console.error('Invalid order ID:', idParam);
      }
    } else {
      console.error('No order ID found in route parameters');
    }

    Sentry.getCurrentScope().setTags({
      "order_id": this.orderId,
      "user_id": this.storageService.getUser().user_id,
    });
  }

}

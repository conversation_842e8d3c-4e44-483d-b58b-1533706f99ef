import {ApplicationRef, Component, OnInit} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { AuthService } from './@core/@core/services/auth.service';
import { CommonModule } from "@angular/common";
import { TranslateService } from '@ngx-translate/core';
import {environment} from "../environments/environment";
import {concat, first, interval} from "rxjs";
import {ServiceWorkerModule, SwUpdate} from "@angular/service-worker";
import {ToastsContainer} from "./@shared/components/toast/toast-container";
import { ChatWidgetComponent } from './@shared/components/chat-widget/chat-widget.component';
import * as Sentry from "@sentry/angular";
import { TokenService } from './@core/@core/services/token.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    ToastsContainer,
    ChatWidgetComponent
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'customer-v2';
  isLoggedIn: boolean = false;

  constructor(
    private tokenService: TokenService,
    private router: Router,
    private translate: TranslateService,
    private swUpdate: SwUpdate,
    private appRef: ApplicationRef,
    trace: Sentry.TraceService
  ) {
  }

  ngOnInit() {
    // Retrieve the language from localStorage or fallback to 'no'
    const savedLanguage = localStorage.getItem('language') || 'no';
    this.translate.setDefaultLang(savedLanguage);
    this.translate.use(savedLanguage);
  
    // Update the <html lang> attribute
    const htmlTag = document.querySelector('html');
    if (htmlTag) {
      htmlTag.setAttribute('lang', savedLanguage);
      console.log(`Set <html> lang attribute on initialization: ${savedLanguage}`);
    } else {
      console.error('Failed to find <html> element during initialization');
    }
  
    // Update the <meta http-equiv="Content-Language"> tag
    let metaTag = document.querySelector('meta[http-equiv="Content-Language"]');
    if (!metaTag) {
      // If the meta tag doesn't exist, create it
      metaTag = document.createElement('meta');
      metaTag.setAttribute('http-equiv', 'Content-Language');
      document.head.appendChild(metaTag);
      console.log('<meta http-equiv="Content-Language"> tag created during initialization');
    }
    metaTag.setAttribute('content', savedLanguage);
    console.log(`Set <meta http-equiv="Content-Language"> content on initialization: ${savedLanguage}`);
  
    if (environment.production) {
      this.checkUpdates();
    }
  }

  checkUpdates() {
    // Allow the app to stabilize first, before starting
    // polling for updates with `interval()`.
    const appIsStable$ = this.appRef.isStable.pipe(first(isStable => isStable === true));
    const everySixHours$ = interval(10 * 1000);
    const everySixHoursOnceAppIsStable$ = concat(appIsStable$, everySixHours$);

    everySixHoursOnceAppIsStable$.subscribe(async () => {
      try {
        const updateFound = await this.swUpdate.checkForUpdate();
        if(updateFound) {
          console.log("A new version is available.");
          console.log("Prompting user to accept updates")
          if (confirm(this.translate.instant("applicationUpdateMessage"))) {
            console.log("User has accepted updates")
            console.log("Updating...")
            document.location.reload();
          }
        }
        else {

          console.log("Already on the latest version.")
        }
      } catch (err) {
        console.error('Failed to check for updates:', err);
      }
    });
  }

}

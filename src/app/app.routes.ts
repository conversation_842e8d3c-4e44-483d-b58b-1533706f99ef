import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {LoginComponent} from "./pages/auth/login/login.component";
import {authGuard as AuthorizeGuard } from './@core/@core/guards/auth.guard';
import {QuoteDetailsComponent} from "./pages/quote/quote-details/quote-details.component";
import {OrdersOverviewComponent} from "./pages/order/orders-overview/orders-overview.component";
import {TermsComponent} from "./pages/terms/terms/terms.component";
import {ContactComponent} from "./pages/terms/contact/contact.component";
import {PaymentComponent} from "./pages/payment/payment.component";
import {AuthComponent} from "./pages/auth/auth/auth.component";
import {MainComponent} from "./pages/main/main.component";
import { CustomerQuestionnaireComponent } from './pages/customer-questionnaire/customer-questionnaire.component';
import { PrivacyPolicyComponent } from './pages/terms/privacy-policy/privacy-policy.component';
import { PageNotFoundComponent } from './pages/page-not-found/page-not-found.component';
export const routes: Routes = [
  { path: '', redirectTo: 'orders-overview', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'quote-details/:orderId', component: QuoteDetailsComponent, canActivate: [AuthorizeGuard] },
  { path: 'orders-overview', component: OrdersOverviewComponent, canActivate: [AuthorizeGuard] },
  { path: 'cq/:orderId', component: CustomerQuestionnaireComponent, canActivate: [AuthorizeGuard] },
  { path: 'orders/:orderId', component: MainComponent, canActivate: [AuthorizeGuard] },
  { path: 'payment/:orderId', component: PaymentComponent, canActivate: [AuthorizeGuard] },
  { path: 'subscriptions/:id', component: OrdersOverviewComponent, canActivate: [AuthorizeGuard] },
  { path: 'terms/:company_id', component: TermsComponent },
  { path: 'privacy/:company_id', component: PrivacyPolicyComponent },
  { path: 'contact/:company_id', component: ContactComponent, canActivate: [AuthorizeGuard] },
  { path: 'auth', component: AuthComponent },
  { path: '**', component: PageNotFoundComponent }
];


@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }

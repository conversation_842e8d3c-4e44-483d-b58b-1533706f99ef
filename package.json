{"name": "customer-v2", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build && npm run sentry:sourcemaps", "watch": "ng build --watch --configuration development", "test": "ng test", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org betweenas-77 --project customer-v2 dist/customer-v2 && sentry-cli sourcemaps upload --org betweenas-77 --project customer-v2 dist/customer-v2"}, "private": true, "dependencies": {"@angular/animations": "^17.3.8", "@angular/cdk": "^17.3.8", "@angular/common": "^17.3.8", "@angular/compiler": "^17.3.8", "@angular/core": "^17.3.8", "@angular/forms": "^17.3.8", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.3.8", "@angular/platform-browser-dynamic": "^17.3.8", "@angular/router": "^17.3.8", "@angular/service-worker": "^17.3.8", "@dintero/checkout-web-sdk": "^0.8.8", "@dotlottie/player-component": "^2.7.12", "@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.2", "@editorjs/list": "^2.0.6", "@editorjs/paragraph": "^2.11.7", "@fortawesome/fontawesome-pro": "^6.5.2", "@lottiefiles/dotlottie-web": "^0.36.0", "@material/dialog": "^15.0.0-canary.7f224ddd4.0", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@sentry/angular": "^8.22.0", "@sentry/cli": "^2.33.1", "@types/hammerjs": "^2.0.45", "bootstrap": "^5.3.3", "country-flag-icons": "^1.5.12", "date-fns": "^3.6.0", "google-libphonenumber": "^3.2.35", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "ng-otp-input": "^1.9.3", "ngx-bootstrap": "^12.0.0", "ngx-cookie-service": "^17.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.7", "@angular/cli": "^17.3.7", "@angular/compiler-cli": "^17.3.8", "@types/google-libphonenumber": "^7.4.30", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}
# Prepare sources
#FROM 759976795385.dkr.ecr.eu-west-1.amazonaws.com/sandbox-common:node as node

ARG AWS_ECR_REPOSITORY_MIRROR_URL_NODE=162725507128.dkr.ecr.eu-west-1.amazonaws.com/sandbox-common:node18.10.0
ARG AWS_ECR_REPOSITORY_MIRROR_URL_NGINX=162725507128.dkr.ecr.eu-west-1.amazonaws.com/sandbox-common:nginx-alpine

FROM ${AWS_ECR_REPOSITORY_MIRROR_URL_NODE} as node
ARG ENVIRONMENT="sandbox"
ARG ENV="sandbox"
#FROM node:18.10.0 as node
LABEL maintainer="<EMAIL>"

WORKDIR /app
COPY . /app
#COPY . .
RUN npm config set "@fortawesome:registry" https://npm.fontawesome.com/
RUN npm config set "//npm.fontawesome.com/:_authToken" "C45DC7AB-6035-4B12-843E-22FE46C97762"

RUN npm install
RUN npm add -g @angular/cli
RUN npm add -g env-cmd
RUN ng build --configuration ${ENVIRONMENT}; exit 0
RUN ng build --configuration ${ENVIRONMENT}; exit 0


#FROM 759976795385.dkr.ecr.eu-west-1.amazonaws.com/sandbox-common:nginx-alpine

FROM ${AWS_ECR_REPOSITORY_MIRROR_URL_NGINX}
#FROM nginx:1.23.1-alpine

# Copy Nginx configuration file into appropriate location (see docs of nginx image)
COPY provisioning/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
COPY provisioning/nginx.conf /etc/nginx/conf.d/default.conf



# Copy static files to nginx work folder
COPY --from=node /app/dist/customer-v2 /var/www/customer-v2/

# Add redirecting requests and error logs to docker log
RUN ln -sf /dev/stdout /var/log/nginx/customer-v2.access.log \
	&& ln -sf /dev/stderr /var/log/nginx/customer-v2.error.log

# Start nginx
ENTRYPOINT [ "/entrypoint.sh" ]
CMD ["nginx", "-g", "daemon off;"]

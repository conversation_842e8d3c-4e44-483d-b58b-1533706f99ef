server {
  # Define server port
  listen       80;
  # Define server name
  server_name  sandbox-customer.between.as  www.sandbox-customer.between.as ;

  # Define resolver
  resolver 8.8.8.8 valid=10s;
  resolver_timeout 10s;

  # Define root of 'site' (the directory that contains static file of your Web App)
  root         /var/www/customer-v2;
  # Define index file
  index        index.html;

  # Access log
  access_log /var/log/nginx/customer-v2.access.log;

  # Error log
  error_log /var/log/nginx/customer-v2.error-web.log;

  # All requests to root location should return index.html page
  location / {
    try_files $uri /index.html = 404;

    location /api {}
  }
 }
